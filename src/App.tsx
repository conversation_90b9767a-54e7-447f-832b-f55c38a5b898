import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/hooks/useAuth";
import { ClerkProvider, useAuth } from "@clerk/clerk-react";
import DezhIndex from "./pages/DezhIndex";
import Dashboard from "./pages/Dashboard";
import DashboardSubscription from "./pages/DashboardSubscription";
import DashboardProfile from "./pages/DashboardProfile";
import DashboardKnowledgeBase from "./pages/DashboardKnowledgeBase";
import DashboardTools from "./pages/DashboardTools";
import DashboardTransports from "./pages/DashboardTransports";
import DashboardSessions from "./pages/DashboardSessions";
import DashboardTestAgent from "./pages/DashboardTestAgent";
import EmbedChatbot from "./pages/EmbedChatbot";
import WidgetPreview from "./pages/WidgetPreview";
import CustomerSupportChat from "./pages/CustomerSupportChat";
import AboutUs from "./pages/AboutUs";
import PrivacyPolicy from "./pages/PrivacyPolicy";
import TermsOfService from "./pages/TermsOfService";
import NotFound from "./pages/NotFound";
import GuideLayout from "./pages/guides/GuideLayout";
import { useEffect, useState } from "react";
import { apiService } from "./services/api";
import ProtectedRoute from "./components/ProtectedRoute";
import RokovoWidgetLoader from "./components/RokovoWidget";
import GuidesIndex from "./pages/guides/GuidesIndex";
import TransportsGuide from "./pages/guides/TransportsGuide";
import TelegramGuide from "./pages/guides/TelegramGuide";
import DiscordGuide from "./pages/guides/DiscordGuide";
import ApiGuide from "./pages/guides/ApiGuide";
import ChatbotGuide from "./pages/guides/ChatbotGuide";
import KnowledgeBaseGuide from "./pages/guides/KnowledgeBaseGuide";
import DashboardGuide from "./pages/guides/DashboardGuide";
const queryClient = new QueryClient();
const PUBLISHABLE_KEY = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY;

if (!PUBLISHABLE_KEY) {
  throw new Error("Add your Clerk Publishable Key to the .env file");
}
const App = () => {
  const { isLoaded, isSignedIn, getToken } = useAuth();
  const [ready, setReady] = useState(false);

  useEffect(() => {
    const init = async () => {
      if (isLoaded && isSignedIn) {
        const token = await getToken({ template: "Rokovo-core" });
        // const token = "";
        if (token) {
          apiService.setToken(token);
        }
      }
      setReady(true);
    };

    init();
  }, [isLoaded, isSignedIn]);

  if (!isLoaded || !ready) return null;

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <RokovoWidgetLoader />

          <Routes>
            <Route path="/" element={<DezhIndex />} />
            <Route
              path="/dashboard"
              element={
                <ProtectedRoute>
                  <Dashboard />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/subscription"
              element={
                <ProtectedRoute>
                  <DashboardSubscription />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/profile"
              element={
                <ProtectedRoute>
                  <DashboardProfile />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/knowledge-base"
              element={
                <ProtectedRoute>
                  <DashboardKnowledgeBase />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/tools"
              element={
                <ProtectedRoute>
                  <DashboardTools />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/transports"
              element={
                <ProtectedRoute>
                  <DashboardTransports />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/sessions"
              element={
                <ProtectedRoute>
                  <DashboardSessions />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/test-agent"
              element={
                <ProtectedRoute>
                  <DashboardTestAgent />
                </ProtectedRoute>
              }
            />
            <Route path="/embed/chatbot" element={<EmbedChatbot />} />
            <Route path="/widget/preview" element={<WidgetPreview />} />
            <Route path="/support" element={<CustomerSupportChat />} />
            <Route path="/about-us" element={<AboutUs />} />
            <Route path="/privacy-policy" element={<PrivacyPolicy />} />
            <Route path="/terms-of-service" element={<TermsOfService />} />

            {/* Guide System Routes */}
            <Route path="/guides" element={<GuideLayout />}>
              <Route index element={<GuidesIndex />} />
              <Route path="transports" element={<TransportsGuide />} />
              <Route path="transports/telegram" element={<TelegramGuide />} />
              <Route path="transports/discord" element={<DiscordGuide />} />
              <Route path="transports/api" element={<ApiGuide />} />
              <Route path="transports/chatbot" element={<ChatbotGuide />} />
              <Route path="knowledge-base" element={<KnowledgeBaseGuide />} />
              <Route path="dashboard" element={<DashboardGuide />} />
            </Route>

            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </TooltipProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
};

export default App;
