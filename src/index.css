@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 11% 8%;
    --foreground: 210 40% 98%;

    --card: 210 11% 8%;
    --card-foreground: 210 40% 98%;

    --popover: 210 11% 8%;
    --popover-foreground: 210 40% 98%;

    --primary: 14 100% 50%;
    --primary-foreground: 210 11% 8%;

    --secondary: 215 16% 15%;
    --secondary-foreground: 210 40% 98%;

    --muted: 215 16% 15%;
    --muted-foreground: 217 10% 65%;

    --accent: 14 100% 50%;
    --accent-foreground: 210 11% 8%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 215 16% 15%;
    --input: 215 16% 15%;
    --ring: 14 100% 50%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5% 26%;
    --sidebar-primary: 240 6% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 5% 96%;
    --sidebar-accent-foreground: 240 6% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217 91% 60%;

    /* Simplified color palette - only using accent blue and primary */
    --accent-blue: 220 100% 70%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
  }

  html {
    scroll-behavior: smooth;
  }

  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
  }
}

@layer components {
  .section-container {
    @apply max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-24;
  }

  .section-title {
    @apply text-4xl md:text-5xl lg:text-6xl font-semibold tracking-tight text-foreground leading-tight;
  }

  .section-subtitle {
    @apply text-lg md:text-xl text-muted-foreground mt-6 max-w-3xl leading-relaxed;
  }

  .btn-primary {
    @apply bg-white/10 backdrop-blur-md border border-white/20 hover:bg-white/20 text-foreground font-medium py-3 px-6 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl relative overflow-hidden;
  }

  .btn-secondary {
    @apply bg-white/5 backdrop-blur-md border border-white/10 hover:bg-white/10 text-foreground hover:text-foreground font-medium py-3 px-6 rounded-lg transition-all duration-300 relative overflow-hidden;
  }

  .glassmorphism {
    @apply bg-white/10 backdrop-blur-md border border-white/20 shadow-lg;
  }

  .glassmorphism-hover {
    @apply hover:bg-white/20 hover:border-white/30 hover:shadow-xl transition-all duration-300;
  }

  .card-modern {
    @apply bg-card/50 backdrop-blur-sm border border-border rounded-xl p-6 transition-all duration-300 hover:bg-card/70 hover:border-border/50;
  }

  .animate-on-scroll {
    @apply opacity-0 translate-y-4;
  }

  .animate-fade-in {
    @apply opacity-100 translate-y-0;
  }

  .gradient-bg {
    background: radial-gradient(600px circle at 50% 200px, hsl(var(--primary) / 0.15), transparent 40%);
  }

  .hero-gradient {
    background: linear-gradient(180deg, hsl(var(--background) / 0) 0%, hsl(var(--background) / 0.8) 100%),
                radial-gradient(900px circle at 50% 200px, hsl(var(--primary) / 0.08), transparent 40%);
  }

  .simple-glow {
    box-shadow: 
      0 0 10px hsl(var(--primary) / 0.3),
      0 0 20px hsl(var(--primary) / 0.1);
  }
}

.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }
.stagger-6 { animation-delay: 0.6s; }

/* Professional AI-themed animations */
@keyframes pulse-simple {
  0%, 100% { 
    box-shadow: 0 0 10px hsl(var(--primary) / 0.3),
                0 0 20px hsl(var(--primary) / 0.1);
  }
  50% { 
    box-shadow: 0 0 15px hsl(var(--primary) / 0.5),
                0 0 30px hsl(var(--primary) / 0.2);
  }
}

@keyframes pulse-glow {
  0%, 100% { 
    box-shadow: 0 0 20px hsl(var(--primary) / 0.1),
                0 0 40px hsl(var(--primary) / 0.05);
    transform: scale(1);
  }
  50% { 
    box-shadow: 0 0 30px hsl(var(--primary) / 0.2),
                0 0 60px hsl(var(--primary) / 0.1);
    transform: scale(1.02);
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-15px); }
}

@keyframes neural-pulse {
  0%, 100% { 
    opacity: 0.4;
    transform: scale(1);
  }
  50% { 
    opacity: 0.8;
    transform: scale(1.1);
  }
}

@keyframes data-flow {
  0% { 
    transform: translateX(-100%) scaleX(0);
    opacity: 0;
  }
  50% {
    transform: translateX(0%) scaleX(1);
    opacity: 1;
  }
  100% {
    transform: translateX(100%) scaleX(0);
    opacity: 0;
  }
}

@keyframes slide-infinite {
  0% { transform: translateX(0); }
  100% { transform: translateX(-50%); }
}

.animate-pulse-simple { animation: pulse-simple 2s ease-in-out infinite; }
.animate-pulse-glow { animation: pulse-glow 4s ease-in-out infinite; }
.animate-float { animation: float 6s ease-in-out infinite; }
.animate-neural-pulse { animation: neural-pulse 3s ease-in-out infinite; }
.animate-data-flow { animation: data-flow 3s ease-in-out infinite; }
.animate-slide-infinite { animation: slide-infinite 40s linear infinite; }
