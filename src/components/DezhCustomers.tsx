import React from "react";
import { Star, TrendingUp, Users, Clock, ArrowRight, CheckCircle } from "lucide-react";
import { useNavigate } from "react-router-dom";
const DezhCustomers = () => {
  const navigate = useNavigate();
  const testimonials = [
    {
      quote: "Rokovo reduced our support ticket volume by 85% in the first month. Our team can now focus on complex issues while AI handles routine requests flawlessly.",
      author: "<PERSON>",
      role: "Head of Customer Success",
      company: "TechFlow Solutions",
      avatar: "SC",
      metrics: {
        improvement: "85%",
        metric: "ticket reduction"
      }
    },
    {
      quote: "The setup was incredibly smooth. Within 30 minutes, we had a fully functional AI agent that understood our product documentation and could handle billing inquiries.",
      author: "<PERSON>",
      role: "Operations Director", 
      company: "CloudBridge Inc",
      avatar: "MR",
      metrics: {
        improvement: "30min",
        metric: "setup time"
      }
    },
    {
      quote: "Our customer satisfaction scores improved by 40% after implementing Rokovo. Customers love getting instant, accurate responses 24/7.",
      author: "<PERSON>",
      role: "Customer Experience Lead",
      company: "InnovateLabs",
      avatar: "EZ",
      metrics: {
        improvement: "40%",
        metric: "satisfaction boost"
      }
    }
  ];
  return <section className="py-24 bg-muted/20">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="text-center mb-20">
        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-6">
          Trusted by forward-thinking
          <span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent"> companies</span>
        </h2>
        <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
          See how leading companies are transforming their customer support with Rokovo
        </p>
      </div>


      {/* Enhanced Testimonials */}
      <div className="grid lg:grid-cols-3 gap-8 mb-20">
        {testimonials.map((testimonial, index) => (
          <div key={index} className="bg-card border border-border rounded-xl p-8 hover:bg-card/80 hover:border-primary/20 hover:shadow-xl transition-all duration-300 group relative overflow-hidden">
            {/* Background gradient on hover */}
            <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            
            <div className="relative z-10">
              {/* Rating */}
              <div className="flex gap-1 mb-6">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 fill-primary text-primary group-hover:scale-110 transition-transform" style={{ transitionDelay: `${i * 50}ms` }} />
                ))}
              </div>

              {/* Quote */}
              <blockquote className="text-foreground leading-relaxed mb-6 text-sm">
                "{testimonial.quote}"
              </blockquote>

              {/* Author */}
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-primary/10 group-hover:bg-primary/20 rounded-full flex items-center justify-center transition-colors">
                  <span className="text-sm font-semibold text-primary">{testimonial.avatar}</span>
                </div>
                <div className="flex-1">
                  <div className="font-semibold text-foreground">{testimonial.author}</div>
                  <div className="text-sm text-muted-foreground">{testimonial.role}</div>
                  <div className="text-sm text-muted-foreground font-medium">{testimonial.company}</div>
                </div>
                <div className="text-right">
                  <div className="text-xl font-bold text-primary group-hover:scale-110 transition-transform">{testimonial.metrics.improvement}</div>
                  <div className="text-xs text-muted-foreground">{testimonial.metrics.metric}</div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Social proof & CTA section */}
      <div className="bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10 rounded-2xl p-8 text-center border border-primary/20">
        <h3 className="text-2xl font-bold text-foreground mb-4">Join the AI Support Revolution</h3>
        <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
          See why leading companies choose Rokovo to transform their customer support
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-6">
          <button
            onClick={() => navigate("/dashboard")}
            className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-4 rounded-xl font-semibold transition-all duration-200 inline-flex items-center group hover:scale-105 hover:shadow-xl"
          >
            <span>Start Your Free Trial</span>
            <ArrowRight className="ml-2 w-4 h-4 transition-transform group-hover:translate-x-1" />
          </button>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>Setup in 5 minutes • No credit card required</span>
          </div>
        </div>
        
        {/* Trust indicators */}
        <div className="flex flex-wrap justify-center gap-6 text-xs text-muted-foreground/60">
          <div className="flex items-center gap-1">
            <CheckCircle className="w-3 h-3 text-green-500" />
            <span>SOC 2 Compliant</span>
          </div>
          <div className="flex items-center gap-1">
            <CheckCircle className="w-3 h-3 text-green-500" />
            <span>GDPR Ready</span>
          </div>
          <div className="flex items-center gap-1">
            <CheckCircle className="w-3 h-3 text-green-500" />
            <span>99.9% Uptime</span>
          </div>
        </div>
      </div>


    </div>
  </section>;
};
export default DezhCustomers;