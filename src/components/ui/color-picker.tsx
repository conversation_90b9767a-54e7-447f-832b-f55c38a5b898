import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

interface ColorPickerProps {
  label: string;
  value: string;
  onChange: (color: string) => void;
  presetColors?: string[];
  className?: string;
}

const DEFAULT_PRESETS = [
  '#ff6600', // Orange (Rokovo primary)
  '#3b82f6', // Blue
  '#10b981', // Green
  '#f59e0b', // Yellow
  '#ef4444', // Red
  '#8b5cf6', // Purple
  '#06b6d4', // Cyan
  '#f97316', // Orange
  '#84cc16', // Lime
  '#ec4899', // Pink
  '#6b7280', // Gray
  '#1f2937', // Dark Gray
];

const ColorPicker: React.FC<ColorPickerProps> = ({
  label,
  value,
  onChange,
  presetColors = DEFAULT_PRESETS,
  className
}) => {
  const [inputValue, setInputValue] = useState(value);
  const [isValidColor, setIsValidColor] = useState(true);
  const { toast } = useToast();

  // Validate hex color
  const validateHexColor = (color: string): boolean => {
    const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    return hexRegex.test(color);
  };

  // Check color contrast for accessibility
  const getContrastRatio = (color: string): number => {
    // Convert hex to RGB
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);

    // Calculate relative luminance
    const getLuminance = (c: number) => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    };

    const luminance = 0.2126 * getLuminance(r) + 0.7152 * getLuminance(g) + 0.0722 * getLuminance(b);
    
    // Calculate contrast ratio with white background
    const whiteL = 1;
    return (whiteL + 0.05) / (luminance + 0.05);
  };

  // Update input value when prop changes
  useEffect(() => {
    setInputValue(value);
    setIsValidColor(validateHexColor(value));
  }, [value]);

  const handleColorChange = (newColor: string) => {
    setInputValue(newColor);
    
    if (validateHexColor(newColor)) {
      setIsValidColor(true);
      onChange(newColor);
      
      // Check accessibility
      const contrast = getContrastRatio(newColor);
      if (contrast < 3) {
        toast({
          title: "Low contrast warning",
          description: "This color may have poor contrast with white text. Consider a darker shade for better accessibility.",
          variant: "destructive"
        });
      }
    } else {
      setIsValidColor(false);
    }
  };

  const handleInputBlur = () => {
    if (!validateHexColor(inputValue)) {
      // Reset to last valid value
      setInputValue(value);
      setIsValidColor(true);
      toast({
        title: "Invalid color",
        description: "Please enter a valid hex color (e.g., #ff6600).",
        variant: "destructive"
      });
    }
  };

  const handlePresetClick = (color: string) => {
    handleColorChange(color);
  };

  return (
    <div className={cn("space-y-3", className)}>
      <Label htmlFor={`color-${label}`}>{label}</Label>
      
      {/* Color Input Row */}
      <div className="flex gap-2">
        <div className="relative">
          <input
            type="color"
            value={isValidColor ? inputValue : '#000000'}
            onChange={(e) => handleColorChange(e.target.value)}
            className="w-12 h-10 border border-border rounded-md cursor-pointer disabled:cursor-not-allowed"
            aria-label={`${label} color picker`}
          />
          {/* Color preview overlay for better visual feedback */}
          <div 
            className="absolute inset-1 rounded-sm pointer-events-none"
            style={{ backgroundColor: isValidColor ? inputValue : '#000000' }}
          />
        </div>
        
        <Input
          id={`color-${label}`}
          type="text"
          value={inputValue}
          onChange={(e) => handleColorChange(e.target.value)}
          onBlur={handleInputBlur}
          placeholder="#ff6600"
          className={cn(
            "flex-1 font-mono",
            !isValidColor && "border-destructive focus:border-destructive"
          )}
          aria-describedby={!isValidColor ? `color-${label}-error` : undefined}
        />
      </div>

      {/* Error message */}
      {!isValidColor && (
        <p id={`color-${label}-error`} className="text-sm text-destructive">
          Please enter a valid hex color (e.g., #ff6600)
        </p>
      )}

      {/* Preset Colors */}
      <div className="space-y-2">
        <Label className="text-xs text-muted-foreground">Quick Colors</Label>
        <div className="grid grid-cols-6 gap-2">
          {presetColors.map((color) => (
            <Button
              key={color}
              type="button"
              variant="outline"
              size="sm"
              className={cn(
                "w-8 h-8 p-0 border-2 rounded-md transition-all hover:scale-110",
                inputValue === color && "ring-2 ring-primary ring-offset-2"
              )}
              style={{ backgroundColor: color }}
              onClick={() => handlePresetClick(color)}
              aria-label={`Select color ${color}`}
              title={color}
            >
              <span className="sr-only">{color}</span>
            </Button>
          ))}
        </div>
      </div>

      {/* Accessibility Info */}
      {isValidColor && (
        <div className="text-xs text-muted-foreground">
          <div className="flex items-center gap-2">
            <div 
              className="w-4 h-4 rounded border"
              style={{ backgroundColor: inputValue }}
            />
            <span>Contrast ratio: {getContrastRatio(inputValue).toFixed(1)}:1</span>
            {getContrastRatio(inputValue) >= 4.5 && (
              <span className="text-green-600">✓ WCAG AA</span>
            )}
            {getContrastRatio(inputValue) >= 3 && getContrastRatio(inputValue) < 4.5 && (
              <span className="text-yellow-600">⚠ WCAG AA Large</span>
            )}
            {getContrastRatio(inputValue) < 3 && (
              <span className="text-red-600">✗ Poor contrast</span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ColorPicker;
