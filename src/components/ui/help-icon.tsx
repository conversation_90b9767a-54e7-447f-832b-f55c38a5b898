import React from "react";
import { Button } from "@/components/ui/button";
import { Too<PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { HelpCircle, ExternalLink } from "lucide-react";
import { cn } from "@/lib/utils";

interface HelpIconProps {
  href: string;
  tooltip?: string;
  size?: "sm" | "md" | "lg";
  variant?: "default" | "ghost" | "outline";
  className?: string;
}

const HelpIcon: React.FC<HelpIconProps> = ({
  href,
  tooltip = "View help guide",
  size = "sm",
  variant = "ghost",
  className,
}) => {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-5 h-5", 
    lg: "w-6 h-6",
  };

  const buttonSizeClasses = {
    sm: "h-6 w-6 p-0",
    md: "h-8 w-8 p-0",
    lg: "h-10 w-10 p-0",
  };

  const handleClick = () => {
    window.open(href, '_blank', 'noopener,noreferrer');
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant={variant}
            size="sm"
            onClick={handleClick}
            className={cn(
              buttonSizeClasses[size],
              "text-muted-foreground hover:text-foreground transition-colors",
              className
            )}
          >
            <HelpCircle className={sizeClasses[size]} />
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <div className="flex items-center gap-2">
            <span>{tooltip}</span>
            <ExternalLink className="w-3 h-3" />
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default HelpIcon;
