import { Skeleton } from "./skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "./table";
const TABLE_COLS = 8;
const ROWS = 4;

const TableSkeleton = ({
  cols = TABLE_COLS,
  rows = ROWS,
}: {
  cols?: number;
  rows?: number;
}) => {
  return (
    <div className="w-full overflow-x-auto animate-fade-up animate-delay-100">
      <Table className="min-w-full border-separate border-spacing-y-2">
        <TableHeader>
          <TableRow className="h-[40px] ">
            {Array.from({ length: cols }).map((_, colIdx) => (
              <TableHead key={colIdx}>
                <Skeleton className="h-4 w-24 rounded" />
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {Array.from({ length: rows }).map((_, rowIdx) => (
            <TableRow key={rowIdx}>
              {Array.from({ length: cols }).map((_, colIdx) => (
                <TableCell key={colIdx}>
                  <Skeleton className="h-5 w-24 rounded" />
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default TableSkeleton;
