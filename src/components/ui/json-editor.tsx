import React from 'react';
import Editor from '@monaco-editor/react';
import { Label } from '@/components/ui/label';

interface JsonEditorProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  height?: string;
  readOnly?: boolean;
}

const JsonEditor: React.FC<JsonEditorProps> = ({
  label,
  value,
  onChange,
  placeholder = '{}',
  height = '150px',
  readOnly = false
}) => {
  const handleEditorChange = (value: string | undefined) => {
    onChange(value || '');
  };

  return (
    <div className="space-y-2">
      <Label>{label}</Label>
      <div className="border rounded-md overflow-hidden">
        <Editor
          height={height}
          defaultLanguage="json"
          value={value || placeholder}
          onChange={handleEditorChange}
          options={{
            minimap: { enabled: false },
            scrollBeyondLastLine: false,
            fontSize: 12,
            readOnly,
            automaticLayout: true,
            formatOnPaste: true,
            formatOnType: true,
            lineNumbers: 'off',
            folding: false,
            wordWrap: 'on',
            theme: 'vs-dark'
          }}
        />
      </div>
    </div>
  );
};

export default JsonEditor;