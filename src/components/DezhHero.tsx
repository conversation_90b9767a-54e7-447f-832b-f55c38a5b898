import React, { useState, useEffect } from "react";
import { ArrowR<PERSON>, Play, Code, Bot, Loader2 } from "lucide-react";
import { useNavigate } from "react-router-dom";

const DezhHero = () => {
  const navigate = useNavigate();
  const [animationStep, setAnimationStep] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  const scrollToContact = () => {
    navigate("/book-demo");
  };

  // Animation sequence - runs once on mount and restarts when manually triggered
  useEffect(() => {
    const runAnimation = () => {
      // Reset state
      setAnimationStep(0);
      setIsLoading(false);

      // Step 1: Show user message
      setTimeout(() => setAnimationStep(1), 1000);

      // Step 2: Show agent initial response
      setTimeout(() => setAnimationStep(2), 2500);

      // Step 3: Show loading
      setTimeout(() => {
        setAnimationStep(3);
        setIsLoading(true);
      }, 4000);

      // Step 4: Show final response
      setTimeout(() => {
        setIsLoading(false);
        setAnimationStep(4);
      }, 6000);

      // Reset after showing complete conversation
      setTimeout(() => {
        runAnimation();
      }, 10000);
    };

    runAnimation();
  }, []); // Empty dependency array - runs once on mount

  const resetAnimation = () => {
    setAnimationStep(0);
    setIsLoading(false);
    // Restart the animation sequence
    setTimeout(() => setAnimationStep(1), 500);
    setTimeout(() => setAnimationStep(2), 2000);
    setTimeout(() => {
      setAnimationStep(3);
      setIsLoading(true);
    }, 3500);
    setTimeout(() => {
      setIsLoading(false);
      setAnimationStep(4);
    }, 5500);
  };

  return (
    <section className="min-h-screen flex items-center justify-center relative bg-background">
      {/* Clean background with subtle gradient */}
      <div className="absolute inset-0 hero-gradient"></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 relative z-10">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left side - Content */}
          <div className="text-left space-y-8">
            {/* Small badge */}
            <div className="inline-flex items-center px-3 py-1 bg-primary/10 rounded-full border border-primary/20">
              <Bot className="w-3 h-3 mr-2 text-primary" />
              <span className="text-xs font-medium text-foreground">Future is here!</span>
            </div>

            {/* Main headline - Baseten style */}
            <h1 className="text-2xl md:text-5xl lg:text-6xl font-bold text-foreground leading-tight">
              <span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                NO</span> hunger,               <span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                NO</span> fatigue; Just work!
              <br />
            </h1>

            {/* Subtitle */}
            <p className="text-lg md:text-xl text-muted-foreground leading-relaxed max-w-lg">
              Define and deploy AI support agents that understand your business context and integrate seamlessly with your system.
            </p>

            {/* CTA buttons with urgency and value */}
            <div className="flex flex-col sm:flex-row gap-4 pt-6">
              <button
                onClick={() => navigate("/dashboard")}
                className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-4 rounded-xl font-semibold transition-all duration-200 flex items-center justify-center group hover:scale-105 hover:shadow-2xl relative overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 transform -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
                <span className="relative">Start Building Free</span>
                <ArrowRight className="ml-2 w-5 h-5 transition-transform group-hover:translate-x-1 relative" />
              </button>
              <button
                onClick={() => open("https://youtube.com")} // TODO:: set youtube demo link
                className="border-2 border-border hover:border-primary/50 px-8 py-4 rounded-xl font-medium hover:bg-primary/5 transition-all duration-200 flex items-center justify-center group hover:scale-105 backdrop-blur-sm"
              >
                <Play className="mr-3 w-5 h-5 group-hover:scale-110 transition-transform" />
                <span>Watch Demo</span>
              </button>
            </div>

            {/* Social proof & trust signals */}
            <div className="flex items-center gap-6 pt-8 flex-wrap">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span>Free tier available</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span>Setup in under 5 minutes</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <div className="w-2 h-2 bg-accent rounded-full"></div>
                <span>No credit card required</span>
              </div>
            </div>

            {/* Enhanced Stats with metrics */}
            <div className="grid grid-cols-3 gap-6 pt-12">
              <div className="text-center group">
                <div className="text-2xl font-bold text-primary mb-1 group-hover:scale-110 transition-transform">~5min</div>
                <div className="text-sm text-muted-foreground">Setup Time</div>
              </div>
              <div className="text-center group">
                <div className="text-2xl font-bold text-primary mb-1 group-hover:scale-110 transition-transform">80%</div>
                <div className="text-sm text-muted-foreground">Cost Reduction</div>
              </div>
              <div className="text-center group">
                <div className="text-2xl font-bold text-primary mb-1 group-hover:scale-110 transition-transform">24/7</div>
                <div className="text-sm text-muted-foreground">Availability</div>
              </div>
            </div>
          </div>

          {/* Right side - Product preview */}
          <div className="relative">
            {/* Main product window */}
            <div className="bg-card border border-border rounded-lg overflow-hidden shadow-2xl">
              {/* Window header */}
              <div className="bg-muted/50 px-4 py-3 border-b border-border flex items-center gap-2">
                <div className="flex gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                </div>
              </div>

              {/* Chat interface mockup with animation */}
              <div className="p-6 space-y-4 h-80 overflow-hidden">
                {/* User message - step 1 */}
                <div className={`flex items-start gap-3 transition-all duration-500 ${animationStep >= 1 ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}`}>
                  <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center">
                    <div className="text-xs">👤</div>
                  </div>
                  <div className="bg-muted/50 rounded-lg px-3 py-2 max-w-xs">
                    <div className="text-sm">Hi, I need help with my billing</div>
                  </div>
                </div>

                {/* Agent initial response - step 2 */}
                {animationStep >= 2 && (
                  <div className={`flex items-start gap-3 transition-all duration-500 ${animationStep >= 2 ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}`}>
                    <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                      <Code className="w-4 h-4 text-primary-foreground" />
                    </div>
                    <div className="bg-primary/10 border border-primary/20 rounded-lg px-3 py-2 max-w-xs">
                      <div className="text-sm">I can help you with that! Let me check your account status and recent billing activity.</div>
                    </div>
                  </div>
                )}

                {/* Loading state - step 3 */}
                {animationStep >= 3 && (
                  <div className={`flex items-start gap-3 transition-all duration-500 ${animationStep >= 3 ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}`}>
                    <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                      <Code className="w-4 h-4 text-primary-foreground" />
                    </div>
                    <div className="bg-primary/10 border border-primary/20 rounded-lg px-3 py-2 max-w-xs">
                      <div className="flex items-center gap-2">
                        {isLoading ? (
                          <Loader2 className="w-4 h-4 animate-spin text-primary" />
                        ) : (
                          <Code className="w-4 h-4 text-primary" />
                        )}
                        <div className="text-xs text-muted-foreground font-mono">
                          {isLoading ? "→ Calling getBillingInfo()" : "→ Function completed"}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Final response - step 4 */}
                {animationStep >= 4 && (
                  <div className={`flex items-start gap-3 transition-all duration-500 ${animationStep >= 4 ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}`}>
                    <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                      <Code className="w-4 h-4 text-primary-foreground" />
                    </div>
                    <div className="bg-primary/10 border border-primary/20 rounded-lg px-3 py-2 max-w-sm">
                      <div className="text-sm">Your last payment was processed successfully on Jan 15th. Your next billing cycle starts Feb 1st. Would you like me to send you a detailed invoice?</div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Floating elements with click to replay */}
            <div
              className="absolute -top-4 -right-4 bg-primary/20 backdrop-blur-sm border border-primary/30 rounded-lg px-3 py-2 animate-float cursor-pointer hover:bg-primary/30 transition-colors"
              onClick={resetAnimation}
            >
              <div className="text-xs font-medium text-primary">Ready to reply in seconds!</div>
            </div>

            <div className="absolute -bottom-4 -left-4 bg-green-500/20 backdrop-blur-sm border border-green-500/30 rounded-lg px-3 py-2 animate-float" style={{ animationDelay: '1s' }}>
              <div className="text-xs font-medium text-green-400">~80% resolved</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DezhHero;
