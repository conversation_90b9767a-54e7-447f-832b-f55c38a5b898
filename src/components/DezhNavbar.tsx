import React, { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { Menu, X } from "lucide-react";
import { useNavigate } from "react-router-dom";
import {
  SignedIn,
  SignedOut,
  SignInButton,
  UserButton,
} from "@clerk/clerk-react";

const DezhNavbar = () => {
  const navigate = useNavigate();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
    document.body.style.overflow = !isMenuOpen ? "hidden" : "";
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
      setIsMenuOpen(false);
      document.body.style.overflow = "";
    }
  };

  const handleGetStarted = () => {
    navigate("/book-demo");
    setIsMenuOpen(false);
    document.body.style.overflow = "";
  };

  return (
    <header
      className={cn(
        "fixed top-0 left-0 right-0 z-50 py-4 transition-all duration-300",
        isScrolled
          ? "glassmorphism backdrop-blur-md border-b border-white/10"
          : "bg-transparent"
      )}
    >
      <div className="container flex items-center justify-between px-4 sm:px-6 lg:px-8">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 rounded-lg flex items-center justify-center">
            <img src="/logo.svg" />
          </div>
          <span className="text-xl font-semibold text-foreground">Rokovo</span>
        </div>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-6">
          <button
            onClick={() => scrollToSection("hero")}
            className="relative text-muted-foreground hover:text-foreground transition-colors duration-200 px-2 py-1 after:content-[''] after:absolute after:w-full after:scale-x-0 after:h-0.5 after:bottom-0 after:left-0 after:bg-primary after:origin-bottom-right after:transition-transform after:duration-300 hover:after:scale-x-100 hover:after:origin-bottom-left"
          >
            Home
          </button>
          <button
            onClick={() => scrollToSection("features")}
            className="relative text-muted-foreground hover:text-foreground transition-colors duration-200 px-2 py-1 after:content-[''] after:absolute after:w-full after:scale-x-0 after:h-0.5 after:bottom-0 after:left-0 after:bg-primary after:origin-bottom-right after:transition-transform after:duration-300 hover:after:scale-x-100 hover:after:origin-bottom-left"
          >
            Features
          </button>
          <button
            onClick={() => scrollToSection("how-it-works")}
            className="relative text-muted-foreground hover:text-foreground transition-colors duration-200 px-2 py-1 after:content-[''] after:absolute after:w-full after:scale-x-0 after:h-0.5 after:bottom-0 after:left-0 after:bg-primary after:origin-bottom-right after:transition-transform after:duration-300 hover:after:scale-x-100 hover:after:origin-bottom-left"
          >
            How It Works
          </button>
          <button
            onClick={() => navigate("/about-us")}
            className="relative text-muted-foreground hover:text-foreground transition-colors duration-200 px-2 py-1 after:content-[''] after:absolute after:w-full after:scale-x-0 after:h-0.5 after:bottom-0 after:left-0 after:bg-primary after:origin-bottom-right after:transition-transform after:duration-300 hover:after:scale-x-100 hover:after:origin-bottom-left"
          >
            About Us
          </button>
        </nav>

        <div className="hidden md:flex items-center space-x-3">
          <SignedOut>
            <div className="flex items-center space-x-3">
              <SignInButton>
                <button className="text-muted-foreground hover:text-foreground transition-colors duration-200 px-4 py-2 rounded-lg hover:bg-muted/20">
                  Sign In
                </button>
              </SignInButton>
              <button
                onClick={handleGetStarted}
                className="bg-primary hover:bg-primary/90 text-primary-foreground px-5 py-2.5 rounded-lg font-medium transition-all duration-200 hover:scale-105 hover:shadow-lg"
              >
                Get Started
              </button>
            </div>
          </SignedOut>
          <SignedIn>
            <button
              onClick={() => navigate("/dashboard")}
              className="bg-primary/10 hover:bg-primary/20 text-primary border border-primary/20 px-4 py-2.5 rounded-lg font-medium transition-all duration-200 hover:scale-105"
            >
              Dashboard
            </button>
          </SignedIn>
        </div>

        {/* Mobile menu button */}
        <button
          className="md:hidden text-foreground p-2 z-50"
          onClick={toggleMenu}
          aria-label={isMenuOpen ? "Close menu" : "Open menu"}
        >
          {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      </div>

      {/* Mobile Navigation */}
      <div
        className={cn(
          "fixed inset-0 z-40 glassmorphism backdrop-blur-md flex flex-col pt-20 px-6 md:hidden transition-all duration-300 ease-in-out",
          isMenuOpen
            ? "opacity-100 translate-x-0"
            : "opacity-0 translate-x-full pointer-events-none"
        )}
      >
        <nav className="flex flex-col space-y-6 items-center mt-8">
          <button
            onClick={() => scrollToSection("hero")}
            className="text-xl text-foreground hover:text-primary py-3 transition-colors duration-200"
          >
            Home
          </button>
          <button
            onClick={() => scrollToSection("features")}
            className="text-xl text-foreground hover:text-primary py-3 transition-colors duration-200"
          >
            Features
          </button>
          <button
            onClick={() => scrollToSection("how-it-works")}
            className="text-xl text-foreground hover:text-primary py-3 transition-colors duration-200"
          >
            How It Works
          </button>
          <button
            onClick={() => scrollToSection("benefits")}
            className="text-xl text-foreground hover:text-primary py-3 transition-colors duration-200"
          >
            Benefits
          </button>
          <button
            onClick={() => navigate("/about-us")}
            className="text-xl text-foreground hover:text-primary py-3 transition-colors duration-200"
          >
            About Us
          </button>
          <div className="text-xl text-foreground hover:text-primary py-3 transition-colors duration-200">
            <SignedOut>
              <SignInButton />
            </SignedOut>
          </div>
          <SignedIn>
            <button
              onClick={() => navigate("/dashboard")}
              className="btn-secondary"
            >
              Dashboard
            </button>
          </SignedIn>
        </nav>
      </div>
    </header>
  );
};

export default DezhNavbar;
