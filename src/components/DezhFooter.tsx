import React from "react";

const DezhFooter = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-background border-t border-border py-16 relative overflow-hidden">
      <div className="section-container relative z-10">
        <div className="grid md:grid-cols-4 gap-8 mb-12">
          {/* Brand */}
          <div className="col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8  rounded-lg flex items-center justify-center">
                <img src="/logo.svg" />
              </div>
              <span className="text-xl font-semibold text-foreground">Rokovo</span>
            </div>
            <p className="text-muted-foreground mb-6 max-w-md text-sm leading-relaxed">
              The AI-powered support agent that makes business support effortless and automated.
              Always available, infinitely scalable, and ready to transform your customer experience.
            </p>
            
            {/* Newsletter signup */}
            <div className="max-w-md">
              <h5 className="text-foreground font-medium mb-3 text-sm">Stay updated</h5>
              <div className="flex gap-2">
                <input 
                  type="email" 
                  placeholder="Enter your email"
                  className="flex-1 bg-muted/50 border border-border rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50"
                />
                <button className="bg-primary hover:bg-primary/90 text-primary-foreground px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:scale-105">
                  Subscribe
                </button>
              </div>
              <p className="text-xs text-muted-foreground/60 mt-2">Get product updates and AI insights</p>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="font-semibold text-foreground mb-4">Product</h4>
            <ul className="space-y-3 text-muted-foreground text-sm">
              <li><a href="#features" className="hover:text-foreground transition-colors duration-200">Features</a></li>
              <li><a href="#how-it-works" className="hover:text-foreground transition-colors duration-200">How It Works</a></li>
              <li><a href="#benefits" className="hover:text-foreground transition-colors duration-200">Benefits</a></li>
              <li><a href="#" className="hover:text-foreground transition-colors duration-200">Pricing</a></li>
            </ul>
          </div>

          {/* Company */}
          <div>
            <h4 className="font-semibold text-foreground mb-4">Company</h4>
            <ul className="space-y-3 text-muted-foreground text-sm">
              <li><a href="/about-us" className="hover:text-foreground transition-colors duration-200">About Us</a></li>
              <li><a href="/privacy-policy" className="hover:text-foreground transition-colors duration-200">Privacy Policy</a></li>
              <li><a href="/terms-of-service" className="hover:text-foreground transition-colors duration-200">Terms of Service</a></li>
            </ul>
          </div>
        </div>

        {/* Bottom */}
        <div className="border-t border-border pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-muted-foreground text-sm">
            © {currentYear} Rokovo. All rights reserved.
          </p>
          <div className="flex items-center gap-4 mt-4 md:mt-0">
            <div className="flex items-center gap-2 text-xs text-muted-foreground/60">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span>All systems operational</span>
            </div>
            <div className="text-xs text-muted-foreground/60">
              Made with ❤️ for better customer support
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default DezhFooter;
