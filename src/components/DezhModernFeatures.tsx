import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, PocketKnife, Cable, Users, CheckCircle } from "lucide-react";
import { useNavigate } from "react-router-dom";
const DezhModernFeatures = () => {
  const navigate = useNavigate();
  const features = [{
    icon: Brain,
    title: "Smart AI Understanding",
    description: "Natural language processing that understands context, intent, and business logic from your documentation.",
    visual: <div className="bg-muted/30 rounded-lg p-4 space-y-3">
          <div className="text-xs text-muted-foreground mb-2">Customer Intent Analysis</div>
          <div className="space-y-2">
            <div className="flex justify-between text-xs">
              <span>Billing Issue</span>
              <span className="text-green-500 font-medium">97%</span>
            </div>
            <div className="w-full bg-muted rounded-full h-1">
              <div className="bg-green-500 h-1 rounded-full" style={{
            width: '97%'
          }}></div>
            </div>
            <div className="flex justify-between text-xs">
              <span>Refund Request</span>
              <span className="text-green-500 font-medium">94%</span>
            </div>
            <div className="w-full bg-muted rounded-full h-1">
              <div className="bg-green-500 h-1 rounded-full" style={{
            width: '94%'
          }}></div>
            </div>
          </div>
        </div>
  }, {
    icon: PocketKnife,
    title: "Auto Function Calling",
    description: "Direct integration with your systems. The AI agent performs actions autonomously to resolve customer issues.",
    visual: <div className="bg-muted/30 rounded-lg p-4">
          <div className="text-xs text-muted-foreground mb-3">Actions Performed Today</div>
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-xs">
              <CheckCircle className="w-3 h-3 text-green-500" />
              <span>247 Billing Lookups</span>
            </div>
            <div className="flex items-center gap-2 text-xs">
              <CheckCircle className="w-3 h-3 text-green-500" />
              <span>83 Refunds Processed</span>
            </div>
            <div className="flex items-center gap-2 text-xs">
              <CheckCircle className="w-3 h-3 text-green-500" />
              <span>156 Account Updates</span>
            </div>
            <div className="flex items-center gap-2 text-xs">
              <CheckCircle className="w-3 h-3 text-green-500" />
              <span>92 Order Modifications</span>
            </div>
          </div>
        </div>
  }, {
    icon: Cable,
    title: "Multi-Channel Deployment",
    description: "Deploy once, run everywhere. Email, chat, API, Telegram, and custom integrations with consistent behavior.",
    visual: <div className="bg-muted/30 rounded-lg p-4">
          <div className="text-xs text-muted-foreground mb-3">Active Channels</div>
          <div className="grid grid-cols-2 gap-2">
            <div className="bg-primary/20 rounded px-2 py-1 text-xs text-center">WebChat</div>
            <div className="bg-primary/20 rounded px-2 py-1 text-xs text-center">Telegram</div>
            <div className="bg-primary/20 rounded px-2 py-1 text-xs text-center">Discord</div>
            <div className="bg-primary/20 rounded px-2 py-1 text-xs text-center">API</div>
          </div>
          <div className="mt-3 text-xs text-center">
            <span className="text-green-500">●</span> All channels synchronized
          </div>
        </div>
  }, {
    icon: Users,
    title: "Seamless Human Handoff",
    description: "Smart escalation when human intervention is needed, with full context transfer and conversation history.",
    visual: <div className="bg-muted/30 rounded-lg p-4">
          <div className="text-xs text-muted-foreground mb-3">Escalation Analytics</div>
          <div className="space-y-2">
            <div className="flex justify-between text-xs">
              <span>Auto-Resolved</span>
              <span className="font-medium text-green-500">87%</span>
            </div>
            <div className="flex justify-between text-xs">
              <span>Human Handoff</span>
              <span className="font-medium text-yellow-500">13%</span>
            </div>
            <div className="mt-2 text-xs text-muted-foreground">
              Avg. handoff time: <span className="text-foreground">1.2s</span>
            </div>
          </div>
        </div>
  }];
  return <section id="features" className="py-24 bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-20">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-6">
            <span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">Everything </span>
            you need for user support
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
             Features that make AI support feel natural and effective
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid lg:grid-cols-2 gap-8">
          {features.map((feature, index) => <div key={index} className="group">
              <div className="bg-card/50 border border-border rounded-xl p-8 hover:bg-card/70 transition-all duration-300">
                <div className="flex items-start gap-6">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                      <feature.icon className="w-6 h-6 text-primary" />
                    </div>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-semibold text-foreground mb-3">{feature.title}</h3>
                    <p className="text-muted-foreground leading-relaxed mb-6">{feature.description}</p>
                    
                    {/* Visual mockup */}
                    <div className="group-hover:scale-105 transition-transform duration-300">
                      {feature.visual}
                    </div>
                  </div>
                </div>
              </div>
            </div>)}
        </div>

        {/* Stats section */}
        

        {/* Enhanced CTA with urgency */}
        <div className="text-center mt-20">
          <div className="bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10 rounded-2xl p-8 border border-primary/20">
            <h3 className="text-2xl font-bold text-foreground mb-4">Ready to transform your support?</h3>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              Join hundreds of companies already using AI to deliver better customer experiences
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <button 
                onClick={() => navigate("/dashboard")} 
                className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-4 rounded-xl font-semibold transition-all duration-200 inline-flex items-center group hover:scale-105 hover:shadow-xl relative overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 transform -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
                <span className="relative">Start Building Free</span>
                <ArrowRight className="ml-2 w-4 h-4 transition-transform group-hover:translate-x-1 relative" />
              </button>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span>No credit card • 5 min setup</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>;
};
export default DezhModernFeatures;