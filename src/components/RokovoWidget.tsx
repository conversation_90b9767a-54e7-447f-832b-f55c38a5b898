import { useEffect } from 'react';
const WIDGET_PUBLISHABLE_KEY = import.meta.env.VITE_ROKOVO_WIDGET_PUBLISHABLE_KEY;
const WIDGET_URL = import.meta.env.VITE_ROKOVO_WIDGET_URL;
const WIDGET_ENDPOINT = import.meta.env.VITE_ROKOVO_WIDGET_ENDPOINT;


const RokovoWidgetLoader = () => {
  useEffect(() => {
    const script = document.createElement('script');

    console.log(WIDGET_URL)

    script.src = WIDGET_URL;
    script.async = true;

    script.setAttribute('data-publishable-key', WIDGET_PUBLISHABLE_KEY);
    script.setAttribute('data-business-name', 'Rokovo');
    script.setAttribute('data-agent-name', 'Rokovo AI');
    script.setAttribute('data-primary-color', '#FF4800');
    script.setAttribute('data-secondary-color', '#E2DDD6');
    script.setAttribute('data-chat-bubble-style', 'rounded');
    script.setAttribute('data-api-base-url', WIDGET_ENDPOINT)

    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
    };
  }, []);

  return null;
};

export default RokovoWidgetLoader;