import React, { useState, useEffect } from "react";
import { ArrowRight, Zap, Users, TrendingUp, Sparkles, Play, Shield, Globe } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

const DezhTransformSection = () => {
  const [counters, setCounters] = useState({
    tickets: 0,
    satisfaction: 0,
    response: 0,
    savings: 0
  });

  const finalValues = {
    tickets: 10000,
    satisfaction: 94,
    response: 2.3,
    savings: 60
  };

  // Animate counters
  useEffect(() => {
    const duration = 2000;
    const interval = 50;
    const steps = duration / interval;
    
    let currentStep = 0;
    const timer = setInterval(() => {
      currentStep++;
      const progress = currentStep / steps;
      const easeOut = 1 - Math.pow(1 - progress, 3);
      
      setCounters({
        tickets: Math.floor(finalValues.tickets * easeOut),
        satisfaction: Math.floor(finalValues.satisfaction * easeOut),
        response: parseFloat((finalValues.response * easeOut).toFixed(1)),
        savings: Math.floor(finalValues.savings * easeOut)
      });
      
      if (currentStep >= steps) {
        clearInterval(timer);
        setCounters({
          tickets: finalValues.tickets,
          satisfaction: finalValues.satisfaction,
          response: finalValues.response,
          savings: finalValues.savings
        });
      }
    }, interval);
    
    return () => clearInterval(timer);
  }, []);

  const techStack = [
    { name: "Natural Language Processing", icon: Sparkles },
    { name: "Machine Learning", icon: TrendingUp },
    { name: "API Integration", icon: Globe },
    { name: "Enterprise Security", icon: Shield }
  ];

  const actions = [
    {
      title: "Start Free Trial",
      description: "30 days, full access",
      icon: Play,
      variant: "default" as const,
      primary: true
    },
    {
      title: "Book Demo",
      description: "See it in action",
      icon: Users,
      variant: "outline" as const,
      primary: false
    },
    {
      title: "View Pricing",
      description: "Flexible plans",
      icon: TrendingUp,
      variant: "outline" as const,
      primary: false
    }
  ];

  return (
    <section className="py-24 relative overflow-hidden bg-gradient-to-br from-background via-muted/20 to-background">
      {/* Floating background elements */}
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary/5 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-accent/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
      <div className="absolute top-1/2 right-1/3 w-64 h-64 bg-primary/3 rounded-full blur-2xl"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header */}
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-foreground mb-6">
            Ready to <span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">Transform</span>
            <br />Your Business?
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Join edge moving companies already using Rokovo to revolutionize the technology.
          </p>
        </div>



        {/* Business Transformation Results */}
        {/* <div className="mb-20">
          <div className="bg-gradient-to-br from-card/50 to-card/30 backdrop-blur-sm border border-border rounded-3xl p-8 md:p-12">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <h3 className="text-3xl font-bold text-foreground mb-6">
                  Transform Your Business Impact
                </h3>
                <p className="text-muted-foreground mb-8 leading-relaxed">
                  See how businesses are achieving remarkable results with Dezh AI - 
                  from cost savings to customer satisfaction improvements.
                </p>
                <div className="grid grid-cols-2 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary mb-2">{counters.tickets.toLocaleString()}+</div>
                    <div className="text-sm text-muted-foreground">Tickets Resolved</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary mb-2">{counters.satisfaction}%</div>
                    <div className="text-sm text-muted-foreground">Satisfaction Rate</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary mb-2">{counters.response}s</div>
                    <div className="text-sm text-muted-foreground">Response Time</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary mb-2">{counters.savings}%</div>
                    <div className="text-sm text-muted-foreground">Cost Reduction</div>
                  </div>
                </div>
              </div>
              <div className="relative">
                <div className="bg-gradient-to-br from-muted/20 to-muted/10 rounded-2xl p-8 border border-border/50">
                  <div className="space-y-6">
                    <div className="text-center">
                      <h4 className="text-xl font-semibold text-foreground mb-4">
                        Business Impact Metrics
                      </h4>
                    </div>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-foreground">Customer Satisfaction</span>
                        <div className="flex items-center gap-2">
                          <div className="w-24 h-2 bg-muted rounded-full">
                            <div className="h-2 bg-gradient-to-r from-green-500 to-green-400 rounded-full" style={{ width: `${counters.satisfaction}%` }}></div>
                          </div>
                          <span className="text-sm font-medium text-foreground">{counters.satisfaction}%</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-foreground">Cost Efficiency</span>
                        <div className="flex items-center gap-2">
                          <div className="w-24 h-2 bg-muted rounded-full">
                            <div className="h-2 bg-gradient-to-r from-primary to-accent rounded-full" style={{ width: `${counters.savings}%` }}></div>
                          </div>
                          <span className="text-sm font-medium text-foreground">{counters.savings}%</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-foreground">Resolution Speed</span>
                        <div className="flex items-center gap-2">
                          <div className="w-24 h-2 bg-muted rounded-full">
                            <div className="h-2 bg-gradient-to-r from-blue-500 to-blue-400 rounded-full" style={{ width: "95%" }}></div>
                          </div>
                          <span className="text-sm font-medium text-foreground">95%</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div> */}

        {/* Call to Action */}
        <div className="text-center">
          <div className="max-w-sm mx-auto">
            <Button
              variant="default"
              size="lg"
              className="w-full h-14 px-8 flex items-center justify-center gap-3 hover:scale-105 transition-all duration-300 bg-gradient-to-r from-primary to-primary/90 text-primary-foreground hover:from-primary/90 hover:to-primary/80 shadow-lg hover:shadow-primary/25 border-0 rounded-xl group"
            >
              <Users className="w-5 h-5" />
              <span className="font-semibold">Book Demo</span>
              <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
            </Button>
          </div>
          
          <div className="mt-12 text-muted-foreground">
            <p className="text-sm">
              ✨ No credit card required for trial • 🔒 Enterprise-grade performance • 🚀 Setup in &lt;1 hour
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DezhTransformSection;