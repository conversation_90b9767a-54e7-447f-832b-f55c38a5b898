import React from "react";
import { cn } from "@/lib/utils";
import {
  Bot,
  CreditCard,
  Building2,
  Database,
  Wrench,
  ChevronLeft,
  ChevronRight,
  Home,
  Settings,
  Activity,
  HelpCircle,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { useNavigate, useLocation } from "react-router-dom";
import HelpIcon from "@/components/ui/help-icon";
interface DashboardSidebarProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
}
const menuItems = [
  {
    icon: Home,
    label: "Overview",
    path: "/dashboard",
  },
  {
    icon: CreditCard,
    label: "Billing & Credits",
    path: "/dashboard/subscription",
  },
  {
    icon: Building2,
    label: "Business Profile",
    path: "/dashboard/profile",
  },
  {
    icon: Database,
    label: "Knowledge Base",
    path: "/dashboard/knowledge-base",
  },
  {
    icon: Wrench,
    label: "Tools Management",
    path: "/dashboard/tools",
  },
  {
    icon: Settings,
    label: "Transports",
    path: "/dashboard/transports",
  },
  {
    icon: Activity,
    label: "Session Monitor",
    path: "/dashboard/sessions",
  },
  {
    icon: Bot,
    label: "Test Your Agent",
    path: "/dashboard/test-agent",
  },
  {
    icon: HelpCircle,
    label: "Help Center",
    path: "/guides",
    external: true,
  },
];
const DashboardSidebar = ({ isOpen, setIsOpen }: DashboardSidebarProps) => {
  const navigate = useNavigate();
  const location = useLocation();
  return (
    <div
      className={cn(
        "fixed left-0 top-0 h-full glassmorphism border-r border-white/10 transition-all duration-300 z-40",
        isOpen ? "w-64" : "w-16"
      )}
    >
      <div className="p-4 border-b border-white/10">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 rounded-lg flex items-center justify-center">
            <img src="/logo.svg" />
          </div>
          {isOpen && (
            <span className="text-xl font-semibold text-foreground">
              Rokovo
            </span>
          )}
        </div>
      </div>

      <nav className="p-4 space-y-2">
        {menuItems.map((item) => {
          const Icon = item.icon;
          const isActive = location.pathname === item.path;
          return (
            <Button
              key={item.path}
              variant="ghost"
              className={cn(
                "w-full justify-start gap-3 text-muted-foreground hover:text-foreground hover:bg-white/10",
                isActive && "bg-white/10 text-foreground",
                !isOpen && "px-3"
              )}
              onClick={() => {
                if (item.external) {
                  window.open(item.path, '_blank', 'noopener,noreferrer');
                } else {
                  navigate(item.path);
                }
              }}
            >
              <Icon className="w-5 h-5" />
              {isOpen && <span>{item.label}</span>}
            </Button>
          );
        })}
      </nav>

      <Button
        variant="ghost"
        size="sm"
        className="absolute bottom-4 right-4 text-muted-foreground hover:text-foreground"
        onClick={() => setIsOpen(!isOpen)}
      >
        {isOpen ? (
          <ChevronLeft className="w-4 h-4" />
        ) : (
          <ChevronRight className="w-4 h-4" />
        )}
      </Button>
    </div>
  );
};
export default DashboardSidebar;
