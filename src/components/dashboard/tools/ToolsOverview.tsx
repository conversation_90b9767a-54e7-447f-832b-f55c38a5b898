import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import PrePreparedTools from "./PrePreparedTools";
import UserDefinedTools from "./UserDefinedTools";
import { Tool } from "@/types/api";

const ToolsOverview = () => {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");

  // Mock tools data - existing user tools
  const [tools, setTools] = useState<Tool[]>([]);

  const handleActivateTool = (tool: Tool) => {
    // Check if tool already exists
    const existingTool = tools.find((t) => t.id === tool.id);
    if (!existingTool) {
      setTools((prev) => [...prev, { ...tool, status: "active" }]);
    }
  };

  const handleUpdateTool = (updatedTool: Tool) => {
    setTools((prev) =>
      prev.map((tool) => (tool.id === updatedTool.id ? updatedTool : tool))
    );
  };

  const handleDeleteTool = (toolId: string, toolName: string) => {
    setTools((prev) => prev.filter((tool) => tool.id !== toolId));
    toast({
      title: "Tool deleted",
      description: `"${toolName}" has been removed successfully.`,
    });
  };

  const handleToggleEnabled = (toolId: string, enabled: boolean) => {
    setTools((prev) =>
      prev.map((tool) =>
        tool.id === toolId
          ? { ...tool, status: enabled ? "active" : "inactive" }
          : tool
      )
    );
    toast({
      title: enabled ? "Tool enabled" : "Tool disabled",
      description: `The tool is now ${enabled ? "active" : "inactive"}.`,
    });
  };

  const filteredTools = tools.filter(
    (tool) =>
      tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      tool.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const activatedToolIds = tools.map((tool) => tool.id);

  return (
    <div className="space-y-8">
      {/* Search */}
      <div className="relative max-w-md">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
        <Input
          placeholder="Search tools..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Pre-prepared Tools Section */}
      <PrePreparedTools
        activatedToolIds={activatedToolIds}
        onActivateTool={handleActivateTool}
      />

      {/* User-defined Tools Section */}
      <UserDefinedTools
        tools={filteredTools}
        onUpdateTool={handleUpdateTool}
        onDeleteTool={handleDeleteTool}
        onToggleEnabled={handleToggleEnabled}
      />
    </div>
  );
};

export default ToolsOverview;
