import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ShoppingCart, DollarSign, Globe, Settings, CheckCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { getDefaultToolsWithIds } from "./defaultTools";
import ToolCustomizationModal from "./ToolCustomizationModal";

interface PrePreparedToolsTabProps {
  activatedToolIds: string[];
  onActivateTool: (tool: any) => void;
}

const PrePreparedToolsTab = ({ activatedToolIds, onActivateTool }: PrePreparedToolsTabProps) => {
  const { toast } = useToast();
  const defaultTools = getDefaultToolsWithIds();
  const [selectedTool, setSelectedTool] = useState<any>(null);
  const [isModalO<PERSON>, setIsModalOpen] = useState(false);

  const getToolIcon = (toolName: string) => {
    const name = toolName.toLowerCase();
    if (name.includes('woocommerce') || name.includes('order') || name.includes('product') || name.includes('shop')) {
      return ShoppingCart;
    }
    return DollarSign;
  };

  const handleActivateTool = (tool: any) => {
    setSelectedTool(tool);
    setIsModalOpen(true);
  };

  const handleConfirmActivation = (customizedTool: any) => {
    onActivateTool(customizedTool);
    setIsModalOpen(false);
    setSelectedTool(null);
    toast({
      title: "Tool Activated",
      description: `${customizedTool.name} has been added to your tools.`,
    });
  };

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-semibold">Pre-prepared Tools</h3>
        <p className="text-muted-foreground">Ready-to-use tools for common integrations</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {defaultTools.map((tool) => {
          const isActivated = activatedToolIds.includes(tool.id);
          const ToolIcon = getToolIcon(tool.name);
          
          return (
            <Card key={tool.id} className="glassmorphism border-white/10 relative">
              {isActivated && (
                <div className="absolute top-2 right-2">
                  <Badge variant="secondary" className="bg-green-500/20 text-green-400 border-green-500/30">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Activated
                  </Badge>
                </div>
              )}
              
              <CardHeader className="pb-3">
                <div className="flex items-center gap-2">
                  <ToolIcon className="w-6 h-6 text-primary" />
                  <Badge variant="secondary" className="bg-blue-500/20 text-blue-400">
                    {tool.setting.method}
                  </Badge>
                </div>
                <CardTitle className="text-base">{tool.name}</CardTitle>
                <CardDescription className="text-sm">{tool.description}</CardDescription>
              </CardHeader>
              
              <CardContent className="pt-0">
                <div className="space-y-3">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Globe className="w-3 h-3" />
                      <span className="truncate">{tool.setting.endpoint}</span>
                    </div>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Settings className="w-3 h-3" />
                      <span>Auth: {tool.setting.auth.model}</span>
                    </div>
                  </div>
                  
                  <Button 
                    onClick={() => handleActivateTool(tool)}
                    disabled={isActivated}
                    size="sm" 
                    className="w-full"
                    variant={isActivated ? "outline" : "default"}
                  >
                    {isActivated ? "Already Activated" : "Activate Tool"}
                  </Button>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {selectedTool && (
        <ToolCustomizationModal
          tool={selectedTool}
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setSelectedTool(null);
          }}
          onConfirm={handleConfirmActivation}
        />
      )}
    </div>
  );
};

export default PrePreparedToolsTab;