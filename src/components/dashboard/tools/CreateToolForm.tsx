import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useQueryClient } from "@tanstack/react-query";
import { apiService } from "@/services/api";
import { HttpMethod, AuthModel, ApiKeyIn, ToolSettings } from "@/types/api";
import { Plus, Code2 } from "lucide-react";
import JsonEditor from "@/components/ui/json-editor";
import { toast } from "sonner";

const CreateToolForm = () => {
  const queryClient = useQueryClient();
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    endpoint: "",
    method: "GET" as HttpMethod,
    authModel: "NONE" as AuthModel,
    apiKeyName: "",
    apiKeyIn: "HEADER" as ApiKeyIn,
    token: "",
    username: "",
    password: "",
    queryParams: "{}",
    bodyParams: "{}",
    headerParams: "{}",
    pathParams: "{}",
    outputSchema: "{}",
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const settings: ToolSettings = {
        endpoint: formData.endpoint,
        method: formData.method,
        queryParams: JSON.parse(formData.queryParams || "{}"),
        headerParams: JSON.parse(formData.headerParams || "{}"),
        pathParams: JSON.parse(formData.pathParams || "{}"),
        bodyParams: JSON.parse(formData.bodyParams || "{}"),
        outputSchema: JSON.parse(formData.outputSchema || "{}"),
        auth: {
          model: formData.authModel,
          apiKeyIn: formData.apiKeyIn,
          ...(formData.authModel === "API_KEY" && {
            apiKeyName: formData.apiKeyName,
          }),
          ...(formData.authModel === "BEARER" && { token: formData.token }),
          ...(formData.authModel === "BASIC" && {
            username: formData.username,
            password: formData.password,
          }),
        },
      };

      await apiService.createTool({
        name: formData.name,
        description: formData.description,
        setting: settings,
      });

      queryClient.invalidateQueries({ queryKey: ["tools"] });

      setFormData({
        name: "",
        description: "",
        endpoint: "",
        method: "GET",
        authModel: "NONE",
        apiKeyName: "",
        apiKeyIn: "HEADER",
        token: "",
        username: "",
        password: "",
        queryParams: "{}",
        bodyParams: "{}",
        headerParams: "{}",
        pathParams: "{}",
        outputSchema: "{}",
      });
      toast.success("Tools created successfully!");
    } catch (error) {
      console.error("Error creating tool:", error);
    }
  };

  return (
    <Card className="glassmorphism border-white/10">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Code2 className="w-5 h-5 text-primary" />
          Create New Tool
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Tool Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) =>
                  setFormData({ ...formData, name: e.target.value })
                }
                required
              />
            </div>
            <div>
              <Label htmlFor="method">HTTP Method</Label>
              <Select
                value={formData.method}
                onValueChange={(value: HttpMethod) =>
                  setFormData({ ...formData, method: value })
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="GET">GET</SelectItem>
                  <SelectItem value="POST">POST</SelectItem>
                  <SelectItem value="PUT">PUT</SelectItem>
                  <SelectItem value="PATCH">PATCH</SelectItem>
                  <SelectItem value="DELETE">DELETE</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) =>
                setFormData({ ...formData, description: e.target.value })
              }
              required
            />
          </div>

          <div>
            <Label htmlFor="endpoint">API Endpoint</Label>
            <Input
              id="endpoint"
              placeholder="https://api.example.com/data"
              value={formData.endpoint}
              onChange={(e) =>
                setFormData({ ...formData, endpoint: e.target.value })
              }
              required
            />
          </div>

          <div>
            <Label htmlFor="auth">Authentication</Label>
            <Select
              value={formData.authModel}
              onValueChange={(value: AuthModel) =>
                setFormData({ ...formData, authModel: value })
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="NONE">No Authentication</SelectItem>
                <SelectItem value="API_KEY">API Key</SelectItem>
                <SelectItem value="BEARER">Bearer Token</SelectItem>
                <SelectItem value="BASIC">Basic Auth</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {formData.authModel === "API_KEY" && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="apiKeyName">API Key Name</Label>
                <Input
                  id="apiKeyName"
                  placeholder="X-API-Key"
                  value={formData.apiKeyName}
                  onChange={(e) =>
                    setFormData({ ...formData, apiKeyName: e.target.value })
                  }
                />
              </div>
              <div>
                <Label htmlFor="apiKeyIn">Location</Label>
                <Select
                  value={formData.apiKeyIn}
                  onValueChange={(value: ApiKeyIn) =>
                    setFormData({ ...formData, apiKeyIn: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="HEADER">Header</SelectItem>
                    <SelectItem value="QUERY">Query Parameter</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}

          {formData.authModel === "BEARER" && (
            <div>
              <Label htmlFor="token">Bearer Token</Label>
              <Input
                id="token"
                type="password"
                value={formData.token}
                onChange={(e) =>
                  setFormData({ ...formData, token: e.target.value })
                }
              />
            </div>
          )}

          {formData.authModel === "BASIC" && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  value={formData.username}
                  onChange={(e) =>
                    setFormData({ ...formData, username: e.target.value })
                  }
                />
              </div>
              <div>
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={formData.password}
                  onChange={(e) =>
                    setFormData({ ...formData, password: e.target.value })
                  }
                />
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 gap-4">
            <JsonEditor
              label="Query Parameters (JSON)"
              value={formData.queryParams}
              onChange={(value) =>
                setFormData({ ...formData, queryParams: value })
              }
              placeholder='{"page": 1, "limit": 10, "status": "active"}'
              height="120px"
            />
            <JsonEditor
              label="Body Parameters (JSON)"
              value={formData.bodyParams}
              onChange={(value) =>
                setFormData({ ...formData, bodyParams: value })
              }
              placeholder='{"name": "John Doe", "email": "<EMAIL>", "amount": 100}'
              height="120px"
            />

            <JsonEditor
              label="Header Parameters (JSON)"
              value={formData.headerParams}
              onChange={(value) =>
                setFormData({ ...formData, headerParams: value })
              }
              placeholder='{"name": "John Doe", "email": "<EMAIL>", "amount": 100}'
              height="120px"
            />
            <JsonEditor
              label="PATH Parameters (JSON)"
              value={formData.pathParams}
              onChange={(value) =>
                setFormData({ ...formData, pathParams: value })
              }
              placeholder='{"type": "object", "properties": {"id": {"type": "string"}, "status": {"type": "string"}, "data": {"type": "object"}}}'
              height="150px"
            />

            <JsonEditor
              label="Output Schema (JSON)"
              value={formData.outputSchema}
              onChange={(value) =>
                setFormData({ ...formData, outputSchema: value })
              }
              placeholder='{"name": "John Doe", "email": "<EMAIL>", "amount": 100}'
              height="120px"
            />
          </div>

          <div className="flex gap-2">
            <Button type="submit">Create Tool</Button>
            <Button type="button" variant="outline">
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default CreateToolForm;
