import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Wrench,
  Globe,
  Trash2,
  <PERSON><PERSON>s,
  Edit3,
  BarChart3,
  CheckCircle,
  XCircle,
  Activity,
  DollarSign,
  Mail,
  Calendar,
  FileText,
  ShoppingCart,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import JsonEditor from "@/components/ui/json-editor";
import { ApiKeyIn, AuthModel, HttpMethod, Tool } from "@/types/api";

interface UserDefinedToolsProps {
  tools: Tool[];
  onUpdateTool: (tool: Tool) => void;
  onDeleteTool: (toolId: string, toolName: string) => void;
  onToggleEnabled: (toolId: string, enabled: boolean) => void;
}

const UserDefinedTools = ({
  tools,
  onUpdateTool,
  onDeleteTool,
  onToggleEnabled,
}: UserDefinedToolsProps) => {
  const { toast } = useToast();
  const [editingTool, setEditingTool] = useState<Tool | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editForm, setEditForm] = useState({
    name: "",
    description: "",
    method: "",
    endpoint: "",
    authModel: "",
    authToken: "",
    apiKeyName: "",
    apiKeyIn: "HEADER" as ApiKeyIn,
    token: "",
    username: "",
    password: "",
    queryParams: "{}",
    bodyParams: "{}",
    outputSchema: "{}",
    headerParams: "{}",
    pathParams: "{}",
  });

  const getToolIcon = (toolName: string) => {
    const name = toolName.toLowerCase();
    if (name.includes("payment") || name.includes("invoice")) return DollarSign;
    if (name.includes("email") || name.includes("notification")) return Mail;
    if (name.includes("calendar") || name.includes("booking")) return Calendar;
    if (name.includes("document") || name.includes("pdf")) return FileText;
    if (
      name.includes("woocommerce") ||
      name.includes("order") ||
      name.includes("product") ||
      name.includes("shop")
    )
      return ShoppingCart;
    return Wrench;
  };

  const handleEditTool = (tool: Tool) => {
    setEditingTool(tool);
    setEditForm({
      name: tool.name,
      description: tool.description,
      method: tool.setting.method,
      endpoint: tool.setting.endpoint,
      authModel: tool.setting.auth.model,
      authToken: tool.setting.auth.token || "",
      apiKeyName: tool.setting.auth.apiKeyName || "",
      apiKeyIn: tool.setting.auth.apiKeyIn as ApiKeyIn,
      token: tool.setting.auth.token ?? null,
      username: tool.setting.auth.username,
      password: tool.setting.auth.password,
      queryParams: JSON.stringify(tool.setting.queryParams || {}, null, 2),
      bodyParams: JSON.stringify(tool.setting.bodyParams || {}, null, 2),
      headerParams: JSON.stringify(tool.setting.headerParams || {}, null, 2),
      pathParams: JSON.stringify(tool.setting.pathParams || {}, null, 2),
      outputSchema: JSON.stringify(tool.setting.outputSchema || {}, null, 2),
    });
  };

  const handleSaveEdit = async () => {
    if (!editingTool) return;

    const updatedTool: Tool = {
      ...editingTool,
      name: editForm.name,
      description: editForm.description,
      setting: {
        ...editingTool.setting,
        method: editForm.method as HttpMethod,
        endpoint: editForm.endpoint,
        auth: {
          model: editForm.authModel as AuthModel,
          token: editForm.authToken ?? null,
          ...editingTool?.setting?.auth,
        },

        headerParams: editForm.headerParams
          ? JSON.parse(editForm.headerParams)
          : undefined,
        outputSchema: editForm.outputSchema
          ? JSON.parse(editForm.outputSchema)
          : undefined,
        bodyParams: editForm.bodyParams
          ? JSON.parse(editForm.bodyParams)
          : undefined,
        queryParams: editForm.queryParams
          ? JSON.parse(editForm.queryParams)
          : undefined,
        pathParams: editForm.pathParams
          ? JSON.parse(editForm.pathParams)
          : undefined,
      },
    };

    await onUpdateTool(updatedTool);
    toast({
      title: "Tool Updated",
      description: "The tool has been successfully updated.",
    });
    setDialogOpen(false);
    setEditingTool(null);
  };

  // Mock metrics data for demonstration
  const getToolMetrics = (toolId: string) => ({
    successRate: Math.floor(Math.random() * 20) + 80, // 80-100%
    avgLatency: Math.floor(Math.random() * 200) + 50, // 50-250ms
    requestsToday: Math.floor(Math.random() * 100) + 10, // 10-110 requests
  });

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold">Your Tools</h3>
          <p className="text-muted-foreground">
            Tools you've activated or created
          </p>
        </div>
        <Badge variant="secondary">{tools.length} tools</Badge>
      </div>

      {tools.length === 0 ? (
        <Card className="glassmorphism border-white/10 border-dashed">
          <CardContent className="pt-6">
            <div className="text-center space-y-2">
              <Wrench className="w-8 h-8 text-muted-foreground mx-auto" />
              <p className="text-muted-foreground">No tools created yet</p>
              <p className="text-sm text-muted-foreground">
                Activate pre-prepared tools or create your own
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {tools?.map((tool) => {
            const metrics = getToolMetrics(tool.id);
            // const isEnabled = tool.status === 'active';
            const ToolIcon = getToolIcon(tool.name);

            return (
              <Card key={tool.id} className="glassmorphism border-white/10">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <ToolIcon className="w-6 h-6 text-primary" />
                      <div className="flex items-center gap-1">
                        {/* {isEnabled ? (
                          <CheckCircle className="w-4 h-4 text-green-500" />
                        ) : (
                          <XCircle className="w-4 h-4 text-red-500" />
                        )} */}
                      </div>
                    </div>
                    <Badge
                      variant="secondary"
                      className="bg-blue-500/20 text-blue-400"
                    >
                      {tool.setting.method}
                    </Badge>
                  </div>
                  <CardTitle className="text-base">{tool.name}</CardTitle>
                  <CardDescription className="text-sm">
                    {tool.description}
                  </CardDescription>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Tool Details */}
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Globe className="w-3 h-3" />
                      <span className="truncate">{tool.setting.endpoint}</span>
                    </div>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Settings className="w-3 h-3" />
                      <span>Auth: {tool.setting?.auth?.model ?? "-"}</span>
                    </div>
                  </div>

                  {/* Tool Metrics */}
                  {/* <div className="bg-background/50 rounded-lg p-3 space-y-2">
                    <div className="flex items-center gap-2 text-xs font-medium text-muted-foreground">
                      <BarChart3 className="w-3 h-3" />
                      Metrics
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div className="flex items-center gap-1">
                        <Activity className="w-3 h-3 text-green-500" />
                        <span>{metrics.successRate}% success</span>
                      </div>
                      <div className="text-muted-foreground">
                        {metrics.avgLatency}ms avg
                      </div>
                      <div className="text-muted-foreground col-span-2">
                        {metrics.requestsToday} requests today
                      </div>
                    </div>
                  </div> */}

                  {/* Tool Status Toggle */}
                  {/* <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Status</span>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-muted-foreground">
                        {isEnabled ? 'Active' : 'Inactive'}
                      </span>
                      <Switch
                        checked={isEnabled}
                        onCheckedChange={(checked) => onToggleEnabled(tool.id, checked)}
                      />
                    </div>
                  </div> */}

                  {/* Action Buttons */}
                  <div className="flex gap-2">
                    <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
                      <DialogTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex-1"
                          onClick={() => handleEditTool(tool)}
                        >
                          <Edit3 className="w-3 h-3 mr-1" />
                          Edit
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                        <DialogHeader>
                          <DialogTitle>Edit Tool - {tool.name}</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label htmlFor="edit-name">Tool Name</Label>
                              <Input
                                disabled
                                id="edit-name"
                                value={editForm.name}
                                onChange={(e) =>
                                  setEditForm((prev) => ({
                                    ...prev,
                                    name: e.target.value,
                                  }))
                                }
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="edit-method">HTTP Method</Label>
                              <Select
                                value={editForm.method}
                                onValueChange={(value) =>
                                  setEditForm((prev) => ({
                                    ...prev,
                                    method: value,
                                  }))
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="GET">GET</SelectItem>
                                  <SelectItem value="POST">POST</SelectItem>
                                  <SelectItem value="PUT">PUT</SelectItem>
                                  <SelectItem value="DELETE">DELETE</SelectItem>
                                  <SelectItem value="PATCH">PATCH</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="edit-description">
                              Description
                            </Label>
                            <Input
                              disabled
                              id="edit-description"
                              value={editForm.description}
                              onChange={(e) =>
                                setEditForm((prev) => ({
                                  ...prev,
                                  description: e.target.value,
                                }))
                              }
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="edit-endpoint">API Endpoint</Label>
                            <Input
                              id="edit-endpoint"
                              value={editForm.endpoint}
                              onChange={(e) =>
                                setEditForm((prev) => ({
                                  ...prev,
                                  endpoint: e.target.value,
                                }))
                              }
                            />
                          </div>

                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label htmlFor="edit-auth-model">Auth Type</Label>
                              <Select
                                value={editForm.authModel}
                                onValueChange={(value) =>
                                  setEditForm((prev) => ({
                                    ...prev,
                                    authModel: value,
                                  }))
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="NONE">None</SelectItem>
                                  <SelectItem value="BEARER">
                                    Bearer Token
                                  </SelectItem>
                                  <SelectItem value="API_KEY">
                                    API Key
                                  </SelectItem>
                                  <SelectItem value="BASIC">
                                    Basic Auth
                                  </SelectItem>
                                  <SelectItem value="OAUTH2">
                                    OAuth 2.0
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="edit-auth-token">
                                Auth Token
                              </Label>
                              <Input
                                id="edit-auth-token"
                                type="password"
                                value={editForm.authToken}
                                onChange={(e) =>
                                  setEditForm((prev) => ({
                                    ...prev,
                                    authToken: e.target.value,
                                  }))
                                }
                              />
                            </div>
                          </div>

                          <JsonEditor
                            label="Query Parameters (JSON)"
                            value={editForm.queryParams}
                            onChange={(value) =>
                              setEditForm({ ...editForm, queryParams: value })
                            }
                            placeholder='{"page": 1, "limit": 10, "status": "active"}'
                            height="120px"
                          />
                          <JsonEditor
                            label="Body Parameters (JSON)"
                            value={editForm.bodyParams}
                            onChange={(value) =>
                              setEditForm({ ...editForm, bodyParams: value })
                            }
                            placeholder='{"name": "John Doe", "email": "<EMAIL>", "amount": 100}'
                            height="120px"
                          />

                          <JsonEditor
                            label="Header Parameters (JSON)"
                            value={editForm.headerParams}
                            onChange={(value) =>
                              setEditForm({ ...editForm, headerParams: value })
                            }
                            placeholder='{"name": "John Doe", "email": "<EMAIL>", "amount": 100}'
                            height="120px"
                          />
                          <JsonEditor
                            label="PATH Parameters (JSON)"
                            value={editForm.pathParams}
                            onChange={(value) =>
                              setEditForm({ ...editForm, pathParams: value })
                            }
                            placeholder='{"type": "object", "properties": {"id": {"type": "string"}, "status": {"type": "string"}, "data": {"type": "object"}}}'
                            height="150px"
                          />

                          <JsonEditor
                            label="Output Schema (JSON)"
                            value={editForm.outputSchema}
                            onChange={(value) =>
                              setEditForm({ ...editForm, outputSchema: value })
                            }
                            placeholder='{"name": "John Doe", "email": "<EMAIL>", "amount": 100}'
                            height="120px"
                          />
                          <div className="flex gap-2">
                            <Button onClick={handleSaveEdit}>
                              Save Changes
                            </Button>
                            <DialogClose asChild>
                              <Button
                                variant="outline"
                                onClick={() => setEditingTool(null)}
                              >
                                Cancel
                              </Button>
                            </DialogClose>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>

                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="outline" size="sm">
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Delete Tool</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to delete "{tool.name}"? This
                            action cannot be undone.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => onDeleteTool(tool.id, tool.name)}
                          >
                            Delete
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default UserDefinedTools;
