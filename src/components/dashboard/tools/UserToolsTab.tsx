import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import UserDefinedTools from "./UserDefinedTools";
import { Tool } from "@/types/api";

interface UserToolsTabProps {
  tools: Tool[];
  onUpdateTool: (tool: Tool) => void;
  onDeleteTool: (toolId: string, toolName: string) => void;
  onToggleEnabled?: (toolId: string, enabled: boolean) => void;
}

const UserToolsTab = ({
  tools,
  onUpdateTool,
  onDeleteTool,
  onToggleEnabled,
}: UserToolsTabProps) => {
  const [searchTerm, setSearchTerm] = useState("");

  const filteredTools = tools.filter(
    (tool) =>
      tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      tool.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold">My Tools</h3>
        <p className="text-muted-foreground">
          Manage your activated and custom tools
        </p>
      </div>

      {/* Search */}
      <div className="relative max-w-md">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
        <Input
          placeholder="Search tools..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* User-defined Tools Section */}
      <UserDefinedTools
        tools={filteredTools}
        onUpdateTool={onUpdateTool}
        onDeleteTool={onDeleteTool}
        onToggleEnabled={onToggleEnabled}
      />
    </div>
  );
};

export default UserToolsTab;
