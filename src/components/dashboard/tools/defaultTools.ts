export interface Tool {
  id: string;
  name: string;
  description: string;
  setting: {
    method: string;
    endpoint: string;
    auth: {
      model: string;
      token?: string;
    };
    headers?: Record<string, string>;
    parameters?: Record<string, any>;
  }; 
  createdAt: string;
}

export const defaultWooCommerceTools: Omit<Tool, 'id' | 'createdAt'>[] = [
  {
    name: "Get WooCommerce Orders",
    description: "Retrieve customer orders from WooCommerce store",
    setting: {
      method: "GET",
      endpoint: "https://yourstore.com/wp-json/wc/v3/orders",
      auth: {
        model: "Basic Auth",
        token: "" // Will be filled by user
      },
      headers: {
        "Content-Type": "application/json"
      },
      parameters: {
        "status": "any",
        "per_page": 10,
        "page": 1
      }
    },
    
  },
  {
    name: "Get WooCommerce Products",
    description: "Retrieve products from WooCommerce catalog",
    setting: {
      method: "GET", 
      endpoint: "https://yourstore.com/wp-json/wc/v3/products",
      auth: {
        model: "Basic Auth",
        token: ""
      },
      headers: {
        "Content-Type": "application/json"
      },
      parameters: {
        "status": "publish",
        "per_page": 20,
        "page": 1
      }
    },
    
  },
  {
    name: "Get Customer Details",
    description: "Retrieve customer information by ID",
    setting: {
      method: "GET",
      endpoint: "https://yourstore.com/wp-json/wc/v3/customers/{id}",
      auth: {
        model: "Basic Auth",
        token: ""
      },
      headers: {
        "Content-Type": "application/json"
      }
    },
    
  },
  {
    name: "Update Order Status",
    description: "Update the status of a WooCommerce order",
    setting: {
      method: "PUT",
      endpoint: "https://yourstore.com/wp-json/wc/v3/orders/{id}",
      auth: {
        model: "Basic Auth",
        token: ""
      },
      headers: {
        "Content-Type": "application/json"
      },
      parameters: {
        "status": "completed"
      }
    },
    
  },
  {
    name: "Create WooCommerce Coupon",
    description: "Create a new discount coupon",
    setting: {
      method: "POST",
      endpoint: "https://yourstore.com/wp-json/wc/v3/coupons",
      auth: {
        model: "Basic Auth", 
        token: ""
      },
      headers: {
        "Content-Type": "application/json"
      },
      parameters: {
        "code": "DISCOUNT10",
        "discount_type": "percent",
        "amount": "10",
        "description": "10% discount coupon"
      }
    },
    
  },
  {
    name: "Get Product Stock",
    description: "Check stock levels for products",
    setting: {
      method: "GET",
      endpoint: "https://yourstore.com/wp-json/wc/v3/products",
      auth: {
        model: "Basic Auth",
        token: ""
      },
      headers: {
        "Content-Type": "application/json"
      },
      parameters: {
        "stock_status": "instock",
        "manage_stock": true
      }
    },
    
  }
];

export const getDefaultToolsWithIds = (): Tool[] => {
  return defaultWooCommerceTools.map((tool, index) => ({
    ...tool,
    id: `woo_${index + 1}`,
    createdAt: new Date().toISOString()
  }));
};