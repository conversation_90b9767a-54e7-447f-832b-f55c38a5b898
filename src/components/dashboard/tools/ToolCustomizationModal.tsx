import React, { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, Dialog<PERSON>ooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import JsonEditor from "@/components/ui/json-editor";
import { ShoppingCart, DollarSign, Globe, Settings } from "lucide-react";

interface ToolCustomizationModalProps {
  tool: any;
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (customizedTool: any) => void;
}

const ToolCustomizationModal = ({ tool, isOpen, onClose, onConfirm }: ToolCustomizationModalProps) => {
  const [name, setName] = useState(tool?.name || "");
  const [description, setDescription] = useState(tool?.description || "");
  const [method, setMethod] = useState(tool?.setting?.method || "GET");
  const [endpoint, setEndpoint] = useState(tool?.setting?.endpoint || "");
  const [authModel, setAuthModel] = useState(tool?.setting?.auth?.model || "Bearer Token");
  const [authToken, setAuthToken] = useState("");
  const [headers, setHeaders] = useState(JSON.stringify(tool?.setting?.headers || {}, null, 2));
  const [body, setBody] = useState(JSON.stringify(tool?.setting?.body || {}, null, 2));
  const [params, setParams] = useState(JSON.stringify(tool?.setting?.params || {}, null, 2));

  const getToolIcon = (toolName: string) => {
    const name = toolName.toLowerCase();
    if (name.includes('woocommerce') || name.includes('order') || name.includes('product') || name.includes('shop')) {
      return ShoppingCart;
    }
    return DollarSign;
  };

  const handleConfirm = () => {
    try {
      const customizedTool = {
        ...tool,
        name,
        description,
        setting: {
          ...tool.setting,
          method,
          endpoint,
          headers: JSON.parse(headers || "{}"),
          body: JSON.parse(body || "{}"),
          params: JSON.parse(params || "{}"),
          auth: {
            model: authModel,
            token: authToken || tool.setting.auth.token
          }
        }
      };
      onConfirm(customizedTool);
    } catch (error) {
      console.error("Invalid JSON format", error);
    }
  };

  if (!tool) return null;

  const ToolIcon = getToolIcon(tool.name);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto w-[95vw] sm:w-full">
        <DialogHeader>
          <div className="flex items-center gap-3">
            <ToolIcon className="w-8 h-8 text-primary" />
            <div>
              <DialogTitle>Customize Tool</DialogTitle>
              <DialogDescription>
                Configure this pre-prepared tool before adding it to your collection
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Preview Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Preview</h3>
            <div className="bg-muted/50 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Badge variant="secondary" className="bg-blue-500/20 text-blue-400">
                  {method}
                </Badge>
                <Badge variant="outline">{authModel}</Badge>
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Globe className="w-4 h-4" />
                <span className="truncate">{endpoint || "API endpoint URL"}</span>
              </div>
              <div className="mt-3">
                <h4 className="font-medium text-sm">{name || "Tool Name"}</h4>
                <p className="text-xs text-muted-foreground mt-1">{description || "Tool description"}</p>
              </div>
            </div>
          </div>

          {/* Customization Fields */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Configuration</h3>
            <div className="space-y-2">
              <Label htmlFor="tool-name">Tool Name</Label>
              <Input
                id="tool-name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Enter a custom name for this tool"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="tool-description">Description</Label>
              <Textarea
                id="tool-description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Describe what this tool does"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="tool-method">HTTP Method</Label>
                <Select value={method} onValueChange={setMethod}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="GET">GET</SelectItem>
                    <SelectItem value="POST">POST</SelectItem>
                    <SelectItem value="PUT">PUT</SelectItem>
                    <SelectItem value="PATCH">PATCH</SelectItem>
                    <SelectItem value="DELETE">DELETE</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="auth-model">Authentication Model</Label>
                <Select value={authModel} onValueChange={setAuthModel}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select auth model" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Bearer Token">Bearer Token</SelectItem>
                    <SelectItem value="API Key">API Key</SelectItem>
                    <SelectItem value="OAuth 2.0">OAuth 2.0</SelectItem>
                    <SelectItem value="Basic Auth">Basic Auth</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="tool-endpoint">API Endpoint</Label>
              <Input
                id="tool-endpoint"
                value={endpoint}
                onChange={(e) => setEndpoint(e.target.value)}
                placeholder="API endpoint URL"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="auth-token">Authentication Token</Label>
              <Input
                id="auth-token"
                type="password"
                value={authToken}
                onChange={(e) => setAuthToken(e.target.value)}
                placeholder="Enter your API key or token"
              />
              <p className="text-xs text-muted-foreground">
                Leave empty to use the default configuration
              </p>
            </div>

            <JsonEditor
              label="Headers"
              value={headers}
              onChange={setHeaders}
              placeholder='{"Content-Type": "application/json"}'
              height="120px"
            />

            <JsonEditor
              label="Parameters"
              value={params}
              onChange={setParams}
              placeholder='{"param1": "value1"}'
              height="120px"
            />

            <JsonEditor
              label="Request Body"
              value={body}
              onChange={setBody}
              placeholder='{"key": "value"}'
              height="120px"
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleConfirm} disabled={!name.trim()}>
            Activate Tool
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ToolCustomizationModal;