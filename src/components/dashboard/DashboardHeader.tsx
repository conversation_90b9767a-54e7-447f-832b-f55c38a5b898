import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import {
  Bell,
  User,
  LogOut,
  Zap,
  CreditCard,
  AlertTriangle,
  CheckCircle,
  Settings,
  UserCircle,
} from "lucide-react";
import UserProfile from "@/components/dashboard/profile/UserProfile";

const DashboardHeader = () => {
  const [isNotificationOpen, setIsNotificationOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);

  const notifications = [
    {
      id: 1,
      title: "Credit Balance Low",
      message:
        "Your credit balance is running low. Consider purchasing more credits.",
      type: "warning",
      icon: CreditCard,
      time: "2 hours ago",
      read: false,
    },
    {
      id: 2,
      title: "API Usage Spike",
      message: "Your API usage has increased by 45% this month.",
      type: "info",
      icon: Zap,
      time: "5 hours ago",
      read: false,
    },
    {
      id: 3,
      title: "Tool Call Failed",
      message:
        "A tool call to your payment API has failed. Check your webhook configuration.",
      type: "error",
      icon: AlertTriangle,
      time: "1 day ago",
      read: true,
    },
    {
      id: 4,
      title: "Session Completed",
      message:
        "Customer support session #4521 has been completed successfully.",
      type: "success",
      icon: CheckCircle,
      time: "2 days ago",
      read: true,
    },
  ];

  const unreadCount = notifications.filter((n) => !n.read).length;

  const getNotificationColor = (type: string) => {
    switch (type) {
      case "warning":
        return "text-orange-400";
      case "error":
        return "text-red-400";
      case "success":
        return "text-green-400";
      default:
        return "text-blue-400";
    }
  };

  return (
    <header className="h-16 border-b border-white/10 glassmorphism flex items-center justify-between px-6">
      <div>
        <h1 className="text-2xl font-semibold text-foreground">Dashboard</h1>
      </div>

      <div className="flex items-center gap-4">
        <Popover open={isNotificationOpen} onOpenChange={setIsNotificationOpen}>
          <PopoverTrigger asChild>
            <Button variant="ghost" size="sm" className="relative">
              <Bell className="w-4 h-4" />
              {unreadCount > 0 && (
                <Badge
                  variant="destructive"
                  className="absolute -top-1 -right-1 h-5 w-5 text-xs p-0 flex items-center justify-center"
                >
                  {unreadCount}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80 p-0" align="end">
            <div className="p-4 border-b">
              <h3 className="font-semibold text-foreground">Notifications</h3>
              <p className="text-sm text-muted-foreground">
                {unreadCount} unread notifications
              </p>
            </div>
            <ScrollArea className="h-80">
              <div className="p-0">
                {notifications.map((notification) => {
                  const IconComponent = notification.icon;
                  return (
                    <div
                      key={notification.id}
                      className={`p-4 border-b border-white/10 hover:bg-accent/50 cursor-pointer ${
                        !notification.read ? "bg-primary/5" : ""
                      }`}
                    >
                      <div className="flex items-start gap-3">
                        <IconComponent
                          className={`w-5 h-5 mt-0.5 ${getNotificationColor(
                            notification.type
                          )}`}
                        />
                        <div className="flex-1 space-y-1">
                          <div className="flex items-center justify-between">
                            <h4 className="text-sm font-medium text-foreground">
                              {notification.title}
                            </h4>
                            {!notification.read && (
                              <div className="w-2 h-2 bg-primary rounded-full"></div>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {notification.message}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {notification.time}
                          </p>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </ScrollArea>
            <div className="p-4 border-t">
              <Button variant="ghost" size="sm" className="w-full text-sm">
                Mark all as read
              </Button>
            </div>
          </PopoverContent>
        </Popover>
        <Popover open={isProfileOpen} onOpenChange={setIsProfileOpen}>
          <PopoverTrigger asChild>
            <Button variant="ghost" size="sm">
              <User className="w-4 h-4" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-96 p-0" align="end">
            <div className="p-6">
              <UserProfile />
            </div>
          </PopoverContent>
        </Popover>
      </div>
    </header>
  );
};

export default DashboardHeader;
