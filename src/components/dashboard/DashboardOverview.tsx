import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { useQuery } from "@tanstack/react-query";
import { apiService } from "@/services/api";
import {
  Users,
  Zap,
  Database,
  Wrench,
  TrendingUp,
  Activity,
  Plus,
  ArrowRight,
  CheckCircle,
  AlertCircle,
  Clock,
} from "lucide-react";
import { KnowledgeBaseStatus } from "@/types/api";
import { useNavigate } from "react-router-dom";

const DashboardOverview = () => {
  const navigate = useNavigate();

  const {
    data: userCredit,
    isLoading: creditLoading,
    error: creditError,
  } = useQuery({
    queryKey: ["user-credit"],
    queryFn: () => apiService.getUserCredit(),
    retry: 2,
  });

  const {
    data: tools,
    isLoading: toolsLoading,
    error: toolsError,
  } = useQuery({
    queryKey: ["tools"],
    queryFn: () => apiService.getAllTools(),
    retry: 2,
  });

  const {
    data: userTransportsResponse,
    isLoading: transportsLoading,
    error: transportsError,
  } = useQuery({
    queryKey: ["user-transports"],
    queryFn: () => apiService.getUserTransports(),
    retry: 2,
  });

  const userTransports = userTransportsResponse?.data || [];

  const {
    data: knowledgeBaseResponse,
    isLoading: knowledgeLoading,
    error: knowledgeError,
  } = useQuery({
    queryKey: ["knowledge-base"],
    queryFn: () => apiService.getKnowledgeBase(),
    retry: 2,
  });
  const knowledgeBase = knowledgeBaseResponse?.data || [];

  const isLoading = creditLoading || toolsLoading || transportsLoading || knowledgeLoading;
  const hasError = creditError || toolsError || transportsError || knowledgeError;

  // Calculate metrics from real data with better error handling
  const activeTransports = userTransports?.length || 0;
  const totalTools = tools?.data?.length || 0;
  const totalKnowledgeItems = knowledgeBase?.length || 0;
  const processedItems = knowledgeBase?.filter((item) => item.status === KnowledgeBaseStatus.FINISH).length || 0;
  const pendingItems = knowledgeBase?.filter((item) => item.status === KnowledgeBaseStatus.PENDING).length || 0;
  const failedItems = knowledgeBase?.filter((item) => item.status === KnowledgeBaseStatus.FAILED).length || 0;

  const successRate = totalKnowledgeItems > 0 ? ((processedItems / totalKnowledgeItems) * 100).toFixed(1) : "0";

  // Quick actions data
  const quickActions = [
    {
      title: "Add Knowledge",
      description: "Upload documents or add content",
      icon: Database,
      action: () => navigate("/dashboard/knowledge-base"),
      color: "text-blue-600",
      bgColor: "",
    },
    {
      title: "Create Tool",
      description: "Set up custom API integrations",
      icon: Wrench,
      action: () => navigate("/dashboard/tools"),
      color: "text-green-600",
      bgColor: "",
    },
    {
      title: "Add Transport",
      description: "Connect communication channels",
      icon: Users,
      action: () => navigate("/dashboard/transports"),
      color: "text-purple-600",
      bgColor: "",
    },
  ];
  // Loading skeleton component
  const MetricCardSkeleton = () => (
    <Card className="glassmorphism border-white/10">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <Skeleton className="h-4 w-24" />
        <Skeleton className="h-4 w-4 rounded" />
      </CardHeader>
      <CardContent>
        <Skeleton className="h-8 w-16 mb-2" />
        <Skeleton className="h-3 w-32" />
      </CardContent>
    </Card>
  );

  // Error state component
  const ErrorCard = ({ title, error }: { title: string; error: any }) => (
    <Card className="glassmorphism border-red-500/20">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-red-600">{title}</CardTitle>
        <AlertCircle className="h-4 w-4 text-red-500" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-red-600">Error</div>
        <p className="text-xs text-red-500">Failed to load data</p>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-8 animate-fade-in">
      {/* Enhanced Header */}
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="space-y-2">
            <h1 className="text-3xl sm:text-4xl font-bold tracking-tight text-foreground">Welcome back!</h1>
            <p className="text-lg text-muted-foreground max-w-2xl">
              Here's an overview of your AI workspace and recent activity
            </p>
          </div>
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2 bg-green-50 text-green-700 px-3 py-1.5 rounded-full text-sm font-medium">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              System Online
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Metrics Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
        {/* Credit Card */}
        {creditLoading ? (
          <MetricCardSkeleton />
        ) : creditError ? (
          <ErrorCard title="Available Credit" error={creditError} />
        ) : (
          <Card className="glassmorphism border-white/10 hover:border-white/20 transition-all duration-300 group">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-medium text-muted-foreground">Available Credit</CardTitle>
              <div className="p-2 bg-primary/10 rounded-lg group-hover:bg-primary/20 transition-colors">
                <Zap className="h-4 w-4 text-primary" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-baseline gap-2">
                <div className="text-3xl font-bold text-foreground">{userCredit?.toLocaleString() ?? 0}</div>
                <TrendingUp className="h-4 w-4 text-green-500" />
              </div>
              <p className="text-xs text-muted-foreground mt-1">Current balance</p>
            </CardContent>
          </Card>
        )}

        {/* Tools Card */}
        {toolsLoading ? (
          <MetricCardSkeleton />
        ) : toolsError ? (
          <ErrorCard title="Active Tools" error={toolsError} />
        ) : (
          <Card className="glassmorphism border-white/10 hover:border-white/20 transition-all duration-300 group">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-medium text-muted-foreground">Active Tools</CardTitle>
              <div className="p-2 bg-green-500/10 rounded-lg group-hover:bg-green-500/20 transition-colors">
                <Wrench className="h-4 w-4 text-green-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-baseline gap-2">
                <div className="text-3xl font-bold text-foreground">{totalTools}</div>
                <Activity className="h-4 w-4 text-green-500" />
              </div>
              <p className="text-xs text-muted-foreground mt-1">Custom API tools</p>
            </CardContent>
          </Card>
        )}

        {/* Transports Card */}
        {transportsLoading ? (
          <MetricCardSkeleton />
        ) : transportsError ? (
          <ErrorCard title="Active Transports" error={transportsError} />
        ) : (
          <Card className="glassmorphism border-white/10 hover:border-white/20 transition-all duration-300 group">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-medium text-muted-foreground">Active Transports</CardTitle>
              <div className="p-2 bg-purple-500/10 rounded-lg group-hover:bg-purple-500/20 transition-colors">
                <Users className="h-4 w-4 text-purple-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-baseline gap-2">
                <div className="text-3xl font-bold text-foreground">{activeTransports}</div>
                <CheckCircle className="h-4 w-4 text-green-500" />
              </div>
              <p className="text-xs text-muted-foreground mt-1">Communication channels</p>
            </CardContent>
          </Card>
        )}

        {/* Knowledge Base Card */}
        {knowledgeLoading ? (
          <MetricCardSkeleton />
        ) : knowledgeError ? (
          <ErrorCard title="Knowledge Items" error={knowledgeError} />
        ) : (
          <Card className="glassmorphism border-white/10 hover:border-white/20 transition-all duration-300 group">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-medium text-muted-foreground">Knowledge Items</CardTitle>
              <div className="p-2 bg-blue-500/10 rounded-lg group-hover:bg-blue-500/20 transition-colors">
                <Database className="h-4 w-4 text-blue-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-baseline gap-2">
                <div className="text-3xl font-bold text-foreground">{totalKnowledgeItems}</div>
                <div className="flex items-center gap-1">
                  {failedItems > 0 && <AlertCircle className="h-3 w-3 text-red-500" />}
                  {pendingItems > 0 && <Clock className="h-3 w-3 text-yellow-500" />}
                  {processedItems > 0 && <CheckCircle className="h-3 w-3 text-green-500" />}
                </div>
              </div>
              <div className="flex items-center justify-between mt-1">
                <p className="text-xs text-muted-foreground">
                  {processedItems} processed • {successRate}% success
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Quick Actions Section */}
      <div className="grid grid-cols-1 lg:grid-cols-1">
        {/* Quick Actions Card */}
        <Card className="glassmorphism border-white/10">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Plus className="w-5 h-5 text-primary" />
              Quick Actions
            </CardTitle>
            <CardDescription>Common tasks to get you started quickly</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {quickActions.map((action, index) => {
              const Icon = action.icon;
              return (
                <Button
                  key={index}
                  variant="ghost"
                  className={`w-full justify-start h-auto p-4 ${action.bgColor} border border-transparent hover:border-white/20 transition-all duration-200`}
                  onClick={action.action}
                >
                  <div className="flex items-center gap-3 w-full">
                    <div className={`p-2 rounded-lg bg-white/10`}>
                      <Icon className={`w-4 h-4 ${action.color}`} />
                    </div>
                    <div className="text-left flex-1">
                      <div className="font-medium text-sm text-foreground">{action.title}</div>
                      <div className="text-xs text-muted-foreground">{action.description}</div>
                    </div>
                    <ArrowRight className="w-4 h-4 text-muted-foreground group-hover:text-foreground transition-colors" />
                  </div>
                </Button>
              );
            })}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DashboardOverview;
