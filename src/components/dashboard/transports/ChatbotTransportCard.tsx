import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  MessageSquare,
  Copy,
  CheckCircle,
  AlertCircle,
  Loader2,
  Eye,
  Settings,
  Palette,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiService } from "@/services/api";
import { TransportUser, ChatbotConfig } from "@/types/api";
import ChatbotConfigurationPanel from "./ChatbotConfigurationPanel";
import HelpIcon from "@/components/ui/help-icon";

interface ChatbotTransportCardProps {
  transport?: TransportUser;
  onRefresh: () => void;
}

const DEFAULT_CONFIG = {
  businessName: "Your Business",
  agentName: "AI Assistant",
  primaryColor: "#ff6600",
  secondaryColor: "#f0f0f0",
  chatBubbleStyle: "rounded" as const,
};

const ChatbotTransportCard = ({ transport, onRefresh }: ChatbotTransportCardProps) => {
  const { toast } = useToast();
  const [isConfiguring, setIsConfiguring] = useState(false);
  const [isCustomizing, setIsCustomizing] = useState(false);
  const [showKeyGenerated, setShowKeyGenerated] = useState(false);
  const [generatedTransport, setGeneratedTransport] = useState<TransportUser | null>(null);
  const queryClient = useQueryClient();

  const isActive = !!transport;
  const config = transport?.setting?.chatbot;

  // Step 1: Generate publishable key with default config
  const generateKeyMutation = useMutation({
    mutationFn: () => apiService.createChatbotTransport(DEFAULT_CONFIG),
    onSuccess: (data) => {
      setGeneratedTransport(data);
      setShowKeyGenerated(true);
      toast({
        title: "Website Chatbot Activated",
        description: "Your publishable key has been generated and your chatbot is now ready to use.",
      });
      onRefresh();
    },
    onError: (error) => {
      console.error('Failed to generate publishable key:', error);
      toast({
        title: "Setup Failed",
        description: "Failed to generate publishable key. Please try again.",
        variant: "destructive",
      });
      // Reset states on error
      setShowKeyGenerated(false);
      setGeneratedTransport(null);
    },
  });

  // Step 2: Update configuration after key is generated
  const updateConfigMutation = useMutation({
    mutationFn: (config: Omit<ChatbotConfig, 'publishableKey' | 'isActive'>) => {
      if (!generatedTransport && !transport) {
        throw new Error('No transport available for update');
      }
      const transportToUpdate = generatedTransport || transport!;
      return apiService.updateChatbotTransport(transportToUpdate.id, config);
    },
    onSuccess: (data) => {
      toast({
        title: "Configuration Updated",
        description: "Your chatbot configuration has been saved successfully.",
      });
      setIsConfiguring(false);
      setShowKeyGenerated(false);
      setGeneratedTransport(null);
      onRefresh();
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to update chatbot configuration.",
        variant: "destructive",
      });
    },
  });

  const disableTransportMutation = useMutation({
    mutationFn: () => apiService.deleteTransport(transport!.id),
    onSuccess: () => {
      toast({
        title: "Website Chatbot Disabled",
        description: "Transport has been disabled successfully.",
      });
      onRefresh();
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to disable transport.",
        variant: "destructive",
      });
    },
  });

  const handleGenerateKey = () => {
    generateKeyMutation.mutate();
  };

  const handleSaveConfiguration = (configData: typeof DEFAULT_CONFIG) => {
    updateConfigMutation.mutate(configData);
  };

  const handleDisable = () => {
    if (transport) {
      disableTransportMutation.mutate();
    }
  };

  const handleCancelSetup = () => {
    setIsConfiguring(false);
    setShowKeyGenerated(false);
    setGeneratedTransport(null);
    // The transport is already created with the key, so we refresh to show it in the active state
    onRefresh();
  };

  const handleCopyPublishableKey = () => {
    if (config?.publishableKey) {
      navigator.clipboard.writeText(config.publishableKey);
      toast({
        title: "Publishable Key copied",
        description: "Publishable key has been copied to clipboard.",
      });
    }
  };

  const handleCopyWidgetScript = () => {
    if (!config?.publishableKey) return;

    const embedCode = `<script
  src="${window.location.origin}/Rokovo-widget.js"
  data-publishable-key="${config.publishableKey}"
  data-business-name="${config.businessName || 'Your Business'}"
  data-agent-name="${config.agentName || 'AI Assistant'}"
  data-primary-color="${config.primaryColor || '#ff6600'}"
  data-secondary-color="${config.secondaryColor || '#f0f0f0'}"
  data-chat-bubble-style="${config.chatBubbleStyle || 'rounded'}"${config.avatarUrl ? `
  data-avatar-url="${config.avatarUrl}"` : ''}
  async>
</script>`;

    navigator.clipboard.writeText(embedCode);
    toast({
      title: "Widget Script Copied",
      description: "The widget script has been copied to your clipboard.",
    });
  };

  const openPreviewWindow = () => {
    if (!config?.publishableKey) return;

    // Build preview URL with configuration parameters
    const params = new URLSearchParams({
      key: config.publishableKey,
      businessName: config.businessName || 'Your Business',
      agentName: config.agentName || 'AI Assistant',
      primaryColor: config.primaryColor || '#ff6600',
      secondaryColor: config.secondaryColor || '#f0f0f0',
      chatBubbleStyle: config.chatBubbleStyle || 'rounded'
    });

    if (config.avatarUrl) {
      params.set('avatarUrl', config.avatarUrl);
    }

    const previewUrl = `${window.location.origin}/widget/preview?${params.toString()}`;
    window.open(previewUrl, '_blank', 'width=900,height=700,scrollbars=yes,resizable=yes');
  };

  return (
    <Card className="glassmorphism border-white/10 hover:border-white/20 transition-colors">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
              <MessageSquare className="w-5 h-5 text-primary" />
            </div>
            <div>
              <CardTitle className="text-lg">Website Chatbot</CardTitle>
              <CardDescription className="text-sm">
                Embeddable chatbot for your website visitors
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <HelpIcon
              href="/guides/transports/chatbot"
              tooltip="View website chatbot setup guide"
              size="sm"
            />
            {isActive ? (
              <CheckCircle className="w-4 h-4 text-green-500" />
            ) : (
              <AlertCircle className="w-4 h-4 text-muted-foreground" />
            )}
            <Badge variant={isActive ? "default" : "secondary"}>
              {isActive ? "Active" : "Inactive"}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {!isActive ? (
          <div className="space-y-3">
            <p className="text-sm text-muted-foreground">
              Create a customizable chatbot that can be embedded on your website to handle customer inquiries.
            </p>

            {!showKeyGenerated ? (
              // Step 1: Generate Publishable Key
              <div className="space-y-3">
                <Button
                  className="w-full"
                  onClick={handleGenerateKey}
                  disabled={generateKeyMutation.isPending}
                >
                  {generateKeyMutation.isPending && (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  )}
                  {generateKeyMutation.isPending ? "Generating Key..." : "Setup Website Chatbot"}
                </Button>

                {generateKeyMutation.isPending && (
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Loader2 className="w-4 h-4 text-blue-600 animate-spin" />
                      <p className="text-sm text-blue-800">
                        Generating your unique publishable key...
                      </p>
                    </div>
                    <p className="text-xs text-blue-600 mt-1">
                      This key will allow you to embed the chatbot on your website securely.
                    </p>
                  </div>
                )}
              </div>
            ) : (
              // Step 2: Show Generated Key and Allow Customization
              <div className="space-y-4">
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    <h4 className="font-medium text-green-800">Website Chatbot Ready!</h4>
                  </div>
                  <p className="text-sm text-green-700 mb-3">
                    Your publishable key has been generated and your chatbot is now active. You can customize its appearance below or start embedding it on your website immediately.
                  </p>
                  <div className="flex items-start gap-2 text-xs text-green-600">
                    <div className="w-1 h-1 bg-green-600 rounded-full mt-1.5 flex-shrink-0"></div>
                    <span>This key is secure and can be safely used in your website's frontend code</span>
                  </div>
                  <div className="bg-white p-3 rounded border">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-xs font-medium text-gray-600">Publishable Key</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          if (generatedTransport?.setting?.chatbot?.publishableKey) {
                            navigator.clipboard.writeText(generatedTransport.setting.chatbot.publishableKey);
                            toast({
                              title: "Key Copied",
                              description: "Publishable key copied to clipboard.",
                            });
                          }
                        }}
                        className="h-6 px-2"
                      >
                        <Copy className="w-3 h-3" />
                      </Button>
                    </div>
                    <div className="font-mono text-sm text-gray-800 break-all">
                      {generatedTransport?.setting?.chatbot?.publishableKey}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-2">
                  <Dialog open={isConfiguring} onOpenChange={setIsConfiguring}>
                    <DialogTrigger asChild>
                      <Button variant="outline" className="flex items-center gap-2">
                        <Palette className="w-4 h-4" />
                        Customize
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-[95vw] sm:max-w-2xl lg:max-w-4xl xl:max-w-6xl max-h-[90vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>Customize Your Chatbot</DialogTitle>
                        <DialogDescription>
                          Configure your chatbot's appearance and behavior
                        </DialogDescription>
                      </DialogHeader>
                      <div className="mt-4">
                        <ChatbotConfigurationPanel
                          initialConfig={DEFAULT_CONFIG}
                          onSave={handleSaveConfiguration}
                          onCancel={() => setIsConfiguring(false)}
                          isLoading={updateConfigMutation.isPending}
                          showPreview={true}
                        />
                      </div>
                    </DialogContent>
                  </Dialog>

                  <Button
                    variant="outline"
                    onClick={() => {
                      if (generatedTransport?.setting?.chatbot?.publishableKey) {
                        const previewUrl = `${window.location.origin}/embed/chatbot?key=${generatedTransport.setting.chatbot.publishableKey}`;
                        window.open(previewUrl, '_blank', 'width=450,height=650,scrollbars=no,resizable=yes');
                      }
                    }}
                    className="flex items-center gap-2"
                  >
                    <Eye className="w-4 h-4" />
                    Preview
                  </Button>
                </div>

                <Button
                  variant="outline"
                  onClick={() => {
                    if (generatedTransport?.setting?.chatbot?.publishableKey) {
                      const chatbotConfig = generatedTransport.setting.chatbot;
                      const embedCode = `<script
  src="${window.location.origin}/Rokovo-widget.js"
  data-publishable-key="${chatbotConfig.publishableKey}"
  data-business-name="${chatbotConfig.businessName || 'Your Business'}"
  data-agent-name="${chatbotConfig.agentName || 'AI Assistant'}"
  data-primary-color="${chatbotConfig.primaryColor || '#ff6600'}"
  data-secondary-color="${chatbotConfig.secondaryColor || '#f0f0f0'}"
  data-chat-bubble-style="${chatbotConfig.chatBubbleStyle || 'rounded'}"${chatbotConfig.avatarUrl ? `
  data-avatar-url="${chatbotConfig.avatarUrl}"` : ''}
  async>
</script>`;
                      navigator.clipboard.writeText(embedCode);
                      toast({
                        title: "Widget Script Copied",
                        description: "The widget script has been copied to your clipboard.",
                      });
                    }
                  }}
                  className="w-full flex items-center gap-2"
                >
                  <Copy className="w-4 h-4" />
                  Copy Widget Script
                </Button>

                <Button
                  variant="ghost"
                  onClick={handleCancelSetup}
                  className="w-full text-sm"
                >
                  Done - I'll customize later
                </Button>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-3">
            {/* Configuration Summary */}
            <div className="p-3 bg-muted/50 rounded-lg space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Business:</span>
                <span className="text-sm text-muted-foreground">{config?.businessName}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Agent:</span>
                <span className="text-sm text-muted-foreground">{config?.agentName}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Style:</span>
                <div className="flex items-center gap-2">
                  <div 
                    className="w-4 h-4 rounded border"
                    style={{ backgroundColor: config?.primaryColor }}
                  />
                  <span className="text-sm text-muted-foreground capitalize">
                    {config?.chatBubbleStyle}
                  </span>
                </div>
              </div>
            </div>

            {/* Publishable Key */}
            <div className="p-3 bg-muted/50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <div className="text-xs text-muted-foreground">Publishable Key</div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCopyPublishableKey}
                  className="h-6 px-2"
                >
                  <Copy className="w-3 h-3" />
                </Button>
              </div>
              <div className="font-mono text-sm text-foreground break-all">
                {config?.publishableKey}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsCustomizing(true)}
                className="flex items-center gap-2"
              >
                <Palette className="w-4 h-4" />
                Customize
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={openPreviewWindow}
                className="flex items-center gap-2"
              >
                <Eye className="w-4 h-4" />
                Preview
              </Button>
            </div>

            <div className="grid grid-cols-1 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopyWidgetScript}
                className="flex items-center gap-2"
              >
                <Copy className="w-4 h-4" />
                Copy Widget Script
              </Button>
            </div>

            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive" className="w-full">
                  Disable Website Chatbot
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Disable Website Chatbot?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This will remove the chatbot configuration and visitors
                    will no longer be able to use the chatbot on your website.
                    This action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDisable}
                    disabled={disableTransportMutation.isPending}
                  >
                    {disableTransportMutation.isPending && (
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    )}
                    Disable
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        )}
      </CardContent>

      {/* Customization Dialog */}
      {isCustomizing && config && (
        <Dialog open={isCustomizing} onOpenChange={setIsCustomizing}>
          <DialogContent className="max-w-[95vw] sm:max-w-2xl lg:max-w-4xl xl:max-w-6xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Customize Chatbot</DialogTitle>
              <DialogDescription>
                Update your chatbot's appearance and settings
              </DialogDescription>
            </DialogHeader>
            <div className="mt-4">
              <ChatbotConfigurationPanel
              initialConfig={{
                businessName: config.businessName,
                agentName: config.agentName,
                primaryColor: config.primaryColor,
                secondaryColor: config.secondaryColor,
                chatBubbleStyle: config.chatBubbleStyle,
                avatarUrl: config.avatarUrl,
              }}
              onSave={(updatedConfig) => {
                // Update the transport configuration
                apiService.updateChatbotTransport(transport!.id, updatedConfig)
                  .then(() => {
                    toast({
                      title: "Configuration Updated",
                      description: "Your chatbot configuration has been saved.",
                    });
                    setIsCustomizing(false);
                    onRefresh();
                  })
                  .catch(() => {
                    toast({
                      title: "Error",
                      description: "Failed to update configuration.",
                      variant: "destructive",
                    });
                  });
              }}
              onCancel={() => setIsCustomizing(false)}
              isLoading={false}
              showPreview={true}
            />
            </div>
          </DialogContent>
        </Dialog>
      )}
    </Card>
  );
};

export default ChatbotTransportCard;
