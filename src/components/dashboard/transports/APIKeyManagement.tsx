import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiService } from "@/services/api";
import { Key, Copy, Trash2, Plus } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const APIKeyManagement = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: transportsResponse, isLoading } = useQuery({
    queryKey: ["user-transports"],
    queryFn: () => apiService.getUserTransports(),
  });

  const userTransports = transportsResponse?.data || [];

  const revokeMutation = useMutation({
    mutationFn: (userTransportId: string) =>
      apiService.deleteTransport(userTransportId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["user-transports"] });
      toast({
        title: "API key revoked",
        description: "The API key has been successfully revoked.",
      });
    },
  });

  const copyToClipboard = (key: string) => {
    navigator.clipboard.writeText(key);
    toast({
      title: "API key copied",
      description: "The API key has been copied to your clipboard.",
    });
  };

  if (isLoading) {
    return (
      <Card className="glassmorphism border-white/10">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="w-5 h-5" />
            API Keys
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            {[1, 2].map((i) => (
              <div key={i} className="h-12 bg-white/10 rounded"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="glassmorphism border-white/10">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Key className="w-5 h-5" />
          API Keys
        </CardTitle>
        <CardDescription>
          Manage your transport API keys for integrations
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex justify-between items-center mb-4">
          <div className="text-sm text-muted-foreground">
            Active API keys for your transports
          </div>
          <Button size="sm">
            <Plus className="w-4 h-4 mr-2" />
            Generate Key
          </Button>
        </div>

        <div className="space-y-3">
          {userTransports.map((userTransport) => (
            <div
              key={userTransport.id}
              className="flex items-center justify-between p-3 border border-white/10 rounded-lg"
            >
              <div className="flex items-center gap-3 flex-1">
                <Key className="w-4 h-4 text-primary" />
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <code className="text-sm bg-muted px-2 py-1 rounded">
                      {userTransport.setting?.pure?.apiKeyHash?.substring(
                        0,
                        20
                      ) || "N/A"}
                      ...
                    </code>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() =>
                        copyToClipboard(
                          userTransport.setting?.pure?.apiKeyHash || ""
                        )
                      }
                    >
                      <Copy className="w-4 h-4" />
                    </Button>
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">
                    Transport ID: {userTransport.id.slice(0, 8)}
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="bg-green-500 text-white">
                  Active
                </Badge>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => revokeMutation.mutate(userTransport.id)}
                  disabled={revokeMutation.isPending}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </div>
          ))}

          {userTransports.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              No API keys generated yet. Create your first key to get started.
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default APIKeyManagement;
