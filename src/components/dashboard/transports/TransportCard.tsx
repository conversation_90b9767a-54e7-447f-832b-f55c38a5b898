import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  MessageSquare,
  Zap,
  Copy,
  CheckCircle,
  AlertCircle,
  Loader2,
  Eye,
  EyeOff,
  Bot,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiService } from "@/services/api";
import { TransportUser } from "@/types/api";
import HelpIcon from "@/components/ui/help-icon";

interface TransportCardProps {
  type: "telegram" | "api" | "discord";
  transport?: TransportUser;
  onRefresh: () => void;
}

const TransportCard = ({ type, transport, onRefresh }: TransportCardProps) => {
  const { toast } = useToast();
  const [isConfiguring, setIsConfiguring] = useState(false);
  const [botToken, setBotToken] = useState("");
  const [showTelegramToken, setShowTelegramToken] = useState(false);
  const [showDiscordToken, setShowDiscordToken] = useState(false);
  const [showApiKey, setShowApiKey] = useState(false);
  const queryClient = useQueryClient();

  const isActive = !!transport;
  const config = transport?.setting;

  const enablePureApiMutation = useMutation({
    mutationFn: () => apiService.createPureApiTransport(),
    onSuccess: (data) => {
      toast({
        title: "Pure API Enabled",
        description: "Your API key has been generated successfully.",
      });
      onRefresh();
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to enable Pure API transport.",
        variant: "destructive",
      });
    },
  });

  const enableTelegramMutation = useMutation({
    mutationFn: (token: string) => apiService.createTelegramTransport(token),
    onSuccess: () => {
      toast({
        title: "Telegram Bot Enabled",
        description: "Your Telegram bot has been configured successfully.",
      });
      setIsConfiguring(false);
      setBotToken("");
      onRefresh();
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to enable Telegram transport.",
        variant: "destructive",
      });
    },
  });

  const disableTransportMutation = useMutation({
    mutationFn: () => apiService.deleteTransport(transport!.id),
    onSuccess: () => {
      toast({
        title: `${getTitle()} Disabled`,
        description: "Transport has been disabled successfully.",
      });
      onRefresh();
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to disable transport.",
        variant: "destructive",
      });
    },
  });

  const getIcon = () => {
    if (type === "telegram") return MessageSquare;
    if (type === "discord") return Bot;
    return Zap;
  };

  const getTitle = () => {
    if (type === "telegram") return "Telegram Bot";
    if (type === "discord") return "Discord Bot";
    return "Pure API";
  };

  const getDescription = () => {
    if (type === "telegram")
      return "Connect your Telegram bot to handle customer messages";
    if (type === "discord")
      return "Connect your Discord bot to handle customer messages";
    return "Direct API integration for custom applications";
  };

  const getGuideUrl = () => {
    if (type === "telegram") return "/guides/transports/telegram";
    if (type === "discord") return "/guides/transports/discord";
    return "/guides/transports/api";
  };

  const handleEnablePureApi = () => {
    enablePureApiMutation.mutate();
  };

  const handleEnableTelegram = () => {
    if (!botToken.trim()) return;
    enableTelegramMutation.mutate(botToken.trim());
  };

  const handleDisable = () => {
    if (transport) {
      disableTransportMutation.mutate();
    }
  };

  const handleCopyApiKey = () => {
    const apiKey = config?.pure?.apiKeyHash || config?.apiKeyHash;
    if (apiKey) {
      navigator.clipboard.writeText(apiKey);
      toast({
        title: "API Key copied",
        description: "API key has been copied to clipboard.",
      });
    }
  };

  const getStatusIcon = () => {
    if (!isActive)
      return <AlertCircle className="w-4 h-4 text-muted-foreground" />;
    return <CheckCircle className="w-4 h-4 text-green-500" />;
  };

  const getStatusText = () => {
    return isActive ? "Active" : "Inactive";
  };

  const Icon = getIcon();

  return (
    <Card className="glassmorphism border-white/10 hover:border-white/20 transition-colors">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
              <Icon className="w-5 h-5 text-primary" />
            </div>
            <div>
              <CardTitle className="text-lg">{getTitle()}</CardTitle>
              <CardDescription className="text-sm">
                {getDescription()}
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <HelpIcon
              href={getGuideUrl()}
              tooltip={`View ${getTitle()} setup guide`}
              size="sm"
            />
            {getStatusIcon()}
            <Badge variant={isActive ? "default" : "secondary"}>
              {isActive ? "Active" : "Inactive"}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {!isActive ? (
          <div className="space-y-3">
            <p className="text-sm text-muted-foreground">
              {type === "telegram"
                ? "Set up your Telegram bot to start receiving customer messages."
                : type === "discord"
                ? "Set up your Discord bot to start receiving customer messages."
                : "Generate an API key to integrate with your applications."}
            </p>

            {type === "telegram" || type === "discord" ? (
              <Dialog open={isConfiguring} onOpenChange={setIsConfiguring}>
                <DialogTrigger asChild>
                  <Button
                    className="w-full"
                    disabled={
                      type === "discord" || enableTelegramMutation.isPending
                    }
                  >
                    {enableTelegramMutation.isPending && (
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    )}
                    Setup {type === "telegram" ? "Telegram" : "Discord"} Bot
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>
                      Setup {type === "telegram" ? "Telegram" : "Discord"} Bot
                    </DialogTitle>
                    <DialogDescription>
                      Enter your bot token to connect your{" "}
                      {type === "telegram" ? "Telegram" : "Discord"} bot
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="botToken">Bot Token</Label>
                      <Input
                        id="botToken"
                        type="password"
                        placeholder={
                          type === "telegram"
                            ? "1234567890:ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefgh"
                            : "MTA5ODc4NjE0ODk2OTQ2NDg4Mg.GXyzAb.cdefghijklmnopqrstuvwxyzABCD"
                        }
                        value={botToken}
                        onChange={(e) => setBotToken(e.target.value)}
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        {type === "telegram"
                          ? "Get your bot token from @BotFather on Telegram"
                          : "Get your bot token from Discord Developer Portal"}
                      </p>
                    </div>
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="outline"
                        onClick={() => setIsConfiguring(false)}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={handleEnableTelegram}
                        disabled={
                          !botToken.trim() || enableTelegramMutation.isPending
                        }
                      >
                        {enableTelegramMutation.isPending && (
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        )}
                        Save & Enable
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            ) : (
              <Button
                className="w-full"
                onClick={handleEnablePureApi}
                disabled={enablePureApiMutation.isPending}
              >
                {enablePureApiMutation.isPending && (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                )}
                Enable Pure API
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-3">
            {type === "telegram" && config?.telegram?.token && (
              <div className="p-3 bg-muted/50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <div className="text-xs text-muted-foreground">Bot Token</div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowTelegramToken(!showTelegramToken)}
                    className="h-6 px-2"
                  >
                    {showTelegramToken ? (
                      <EyeOff className="w-3 h-3" />
                    ) : (
                      <Eye className="w-3 h-3" />
                    )}
                  </Button>
                </div>
                <div className="font-mono text-sm text-foreground break-all">
                  {showTelegramToken ? config.telegram.token : "•".repeat(40)}
                </div>
              </div>
            )}

            {type === "discord" && config?.discord?.token && (
              <div className="p-3 bg-muted/50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <div className="text-xs text-muted-foreground">Bot Token</div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowDiscordToken(!showDiscordToken)}
                    className="h-6 px-2"
                  >
                    {showDiscordToken ? (
                      <EyeOff className="w-3 h-3" />
                    ) : (
                      <Eye className="w-3 h-3" />
                    )}
                  </Button>
                </div>
                <div className="font-mono text-sm text-foreground break-all">
                  {showDiscordToken ? config.discord.token : "•".repeat(60)}
                </div>
              </div>
            )}

            {type === "api" && (
              <div className="space-y-3">
                <div className="p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <div className="text-xs text-muted-foreground">API Key</div>
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowApiKey(!showApiKey)}
                        className="h-6 px-2"
                      >
                        {showApiKey ? (
                          <EyeOff className="w-3 h-3" />
                        ) : (
                          <Eye className="w-3 h-3" />
                        )}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleCopyApiKey}
                        className="h-6 px-2"
                      >
                        <Copy className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                  <div className="font-mono text-sm text-foreground break-all">
                    {showApiKey
                      ? config?.pure?.apiKeyHash || config?.apiKeyHash
                      : "•".repeat(60)}
                  </div>
                </div>

                <div className="p-3 bg-muted/50 rounded-lg">
                  <div className="text-xs text-muted-foreground mb-1">
                    API Endpoint
                  </div>
                  <div className="font-mono text-sm text-foreground">
                    POST /api/v1/agent/message
                  </div>
                </div>
              </div>
            )}

            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive" className="w-full">
                  Disable {getTitle()}
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Disable {getTitle()}?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This will remove the transport configuration and customers
                    will no longer be able to reach your agent through{" "}
                    {type === "telegram"
                      ? "Telegram"
                      : type === "discord"
                      ? "Discord"
                      : "the API"}
                    . This action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDisable}
                    disabled={disableTransportMutation.isPending}
                  >
                    {disableTransportMutation.isPending && (
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    )}
                    Disable
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TransportCard;
