
import React, { useState } from "react";
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Settings, Zap, MessageSquare, Bot, Globe, Plus } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiService } from "@/services/api";

const TransportConfiguration = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [telegramToken, setTelegramToken] = useState('');

  const createPureApiMutation = useMutation({
    mutationFn: () => apiService.createPureApiTransport(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-transports'] });
      toast({
        title: "Pure API transport created",
        description: "Your API transport has been set up successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Creation failed",
        description: "There was an error creating the API transport.",
        variant: "destructive",
      });
    },
  });

  const createTelegramMutation = useMutation({
    mutationFn: (token: string) => apiService.createTelegramTransport(token),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-transports'] });
      toast({
        title: "Telegram bot configured",
        description: "Your Telegram bot has been set up successfully.",
      });
      setTelegramToken('');
    },
    onError: () => {
      toast({
        title: "Configuration failed",
        description: "There was an error configuring the Telegram bot.",
        variant: "destructive",
      });
    },
  });

  const handleCreatePureApi = () => {
    createPureApiMutation.mutate();
  };

  const handleCreateTelegram = () => {
    if (!telegramToken.trim()) {
      toast({
        title: "Token required",
        description: "Please enter a valid Telegram bot token.",
        variant: "destructive",
      });
      return;
    }
    createTelegramMutation.mutate(telegramToken);
  };

  return (
    <Card className="glassmorphism border-white/10">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="w-5 h-5" />
          Transport Configuration
        </CardTitle>
        <CardDescription>
          Configure new transport connections and integrations
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="telegram" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="telegram" className="flex items-center gap-2">
              <Bot className="w-4 h-4" />
              Telegram Bot
            </TabsTrigger>
            <TabsTrigger value="api" className="flex items-center gap-2">
              <Globe className="w-4 h-4" />
              Pure API
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="telegram" className="space-y-4">
            <div className="text-sm text-muted-foreground mb-4">
              Set up a Telegram bot to enable customer interactions through Telegram
            </div>
            
            <div className="space-y-4">
              <div>
                <Label htmlFor="telegramToken">Bot Token</Label>
                <Input
                  id="telegramToken"
                  type="password"
                  placeholder="Enter your Telegram bot token"
                  value={telegramToken}
                  onChange={(e) => setTelegramToken(e.target.value)}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Get your bot token from @BotFather on Telegram
                </p>
              </div>
              
              <Button 
                onClick={handleCreateTelegram}
                className="w-full"
                disabled={createTelegramMutation.isPending || !telegramToken.trim()}
              >
                <Bot className="w-4 h-4 mr-2" />
                {createTelegramMutation.isPending ? 'Setting up...' : 'Setup Telegram Bot'}
              </Button>
            </div>

            <div className="bg-background/50 rounded-lg p-4 text-sm">
              <h4 className="font-medium mb-2">Setup Instructions:</h4>
              <ol className="list-decimal list-inside space-y-1 text-muted-foreground">
                <li>Message @BotFather on Telegram</li>
                <li>Send /newbot command</li>
                <li>Follow the prompts to create your bot</li>
                <li>Copy the token and paste it above</li>
              </ol>
            </div>
          </TabsContent>
          
          <TabsContent value="api" className="space-y-4">
            <div className="text-sm text-muted-foreground mb-4">
              Create a Pure API transport for direct integration with your applications
            </div>
            
            <div className="bg-background/50 rounded-lg p-4 space-y-3">
              <h4 className="font-medium">Pure API Integration</h4>
              <p className="text-sm text-muted-foreground">
                This will generate an API key that you can use to send messages directly to your AI agent.
              </p>
              <div className="text-xs text-muted-foreground">
                <strong>Endpoint:</strong> POST /api/v1/chat
              </div>
            </div>
            
            <Button 
              onClick={handleCreatePureApi}
              className="w-full"
              disabled={createPureApiMutation.isPending}
            >
              <Plus className="w-4 h-4 mr-2" />
              {createPureApiMutation.isPending ? 'Creating...' : 'Create Pure API Transport'}
            </Button>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default TransportConfiguration;
