import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import TransportCard from "./TransportCard";
import ChatbotTransportCard from "./ChatbotTransportCard";
import { useQuery } from "@tanstack/react-query";
import { apiService } from "@/services/api";
import { TransportUser } from "@/types/api";
import { Loader2 } from "lucide-react";
import HelpIcon from "@/components/ui/help-icon";

const TransportsOverview = () => {
  const { data: userTransportsResponse, refetch, isLoading } = useQuery({
    queryKey: ["user-transports"],
    queryFn: () => apiService.getUserTransports(),
  });

  const transports = userTransportsResponse?.data || [];

  // Find existing transports by type - check if they have actual tokens/keys
  const telegramTransport = transports.find(t =>
    t.setting?.telegram?.token && t.setting.telegram.token.trim() !== ""
  );
  const apiTransport = transports.find(t =>
    t.setting?.pure?.apiKeyHash && t.setting.pure.apiKeyHash.trim() !== ""
  );
  const discordTransport = transports.find(t =>
    t.setting?.discord?.token && t.setting.discord.token.trim() !== ""
  );
  const chatbotTransport = transports.find(t =>
    t.setting?.chatbot?.publishableKey && t.setting.chatbot.publishableKey.trim() !== ""
  );

  const handleRefresh = () => {
    refetch();
  };
  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="w-6 h-6 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card className="glassmorphism border-white/10">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Transport Overview</CardTitle>
              <CardDescription>
                Manage your communication channels and configure how customers can
                interact with your AI agent
              </CardDescription>
            </div>
            <HelpIcon
              href="/guides/transports"
              tooltip="View transport setup guides"
              size="md"
            />
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <TransportCard
              type="telegram"
              transport={telegramTransport}
              onRefresh={handleRefresh}
            />
            <TransportCard
              type="discord"
              transport={discordTransport}
              onRefresh={handleRefresh}
            />
            <TransportCard
              type="api"
              transport={apiTransport}
              onRefresh={handleRefresh}
            />
            <ChatbotTransportCard
              transport={chatbotTransport}
              onRefresh={handleRefresh}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
export default TransportsOverview;
