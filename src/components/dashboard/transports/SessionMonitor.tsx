import React, { useEffect, useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  MessageSquare,
  Search,
  Eye,
  Clock,
  User,
  Bot,
  Filter,
  X,
  Send,
  AlertTriangle,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useMutation, useQuery } from "@tanstack/react-query";
import { apiService } from "@/services/api";
import { Session } from "@/types/api";
import { Skeleton } from "@/components/ui/skeleton";

const SessionMonitor = () => {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<
    "CLOSED" | "OPEN" | "HITL" | "all"
  >("all");

  const { data: sessionsResponse, isLoading } = useQuery({
    queryKey: ["sessions"],
    queryFn: () => apiService.getAllSessions(),
  });

  const [sessions, setSessions] = useState<Session[]>([]);

  useEffect(() => {
    const session = sessionsResponse?.data || [];
    setSessions(session as any);
  }, [sessionsResponse]);
  // // Mock session data for demonstration

  const getStatusColor = (status: string) => {
    switch (status) {
      case "OPEN":
        return "bg-green-500 text-white";
      case "CLOSED":
        return "bg-gray-500 text-white";
      case "HITL":
        return "bg-yellow-500 text-white";
      default:
        return "bg-gray-500 text-white";
    }
  };

  const getTransportIcon = (transport: string) => {
    switch (transport) {
      case "telegram":
        return <MessageSquare className="w-4 h-4" />;
      case "api":
        return <Bot className="w-4 h-4" />;
      default:
        return <MessageSquare className="w-4 h-4" />;
    }
  };

  const filteredSessions = sessions.filter((session) => {
    const matchesSearch =
      session.externalUserId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      session.externalUserId.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === "all" || session.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const formatTime = (isoString: string) => {
    return new Date(isoString).toLocaleString();
  };
  const deleteSessionMutation = useMutation({
    mutationFn: (sessionId: string) => apiService.deleteSessions(sessionId),
    onSuccess: () => {
      toast({
        title: "Session closed",
        description: "The session has been closed successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to disable transport.",
        variant: "destructive",
      });
    },
  });

  const handleCloseSession = (sessionId: string) => {
    deleteSessionMutation.mutate(sessionId);
    setSessions((prevSessions) =>
      prevSessions.map((session) =>
        session.id === sessionId
          ? { ...session, status: "CLOSED" as const }
          : session
      )
    );
  };

  // const handleSendMessage = (sessionId: string, message: string) => {
  //   // In a real app, this would send the message through the API
  //   toast({
  //     title: "Message sent",
  //     description: "Your message has been sent to the customer.",
  //   });
  // };

  return (
    <Card className="glassmorphism border-white/10">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="w-5 h-5 text-primary" />
          Session Monitor
        </CardTitle>
        <CardDescription>
          Monitor active and recent customer sessions across all transport
          channels
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              placeholder="Search by customer name or ID..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex gap-2">
            <Button
              variant={statusFilter === "all" ? "default" : "outline"}
              size="sm"
              onClick={() => setStatusFilter("all")}
            >
              All
            </Button>
            <Button
              variant={statusFilter === "OPEN" ? "default" : "outline"}
              size="sm"
              onClick={() => setStatusFilter("OPEN")}
            >
              OPEN
            </Button>
            <Button
              variant={statusFilter === "CLOSED" ? "default" : "outline"}
              size="sm"
              onClick={() => setStatusFilter("CLOSED")}
            >
              CLOSED
            </Button>
            <Button
              variant={statusFilter === "HITL" ? "default" : "outline"}
              size="sm"
              onClick={() => setStatusFilter("HITL")}
            >
              HITL
            </Button>
          </div>
        </div>

        {/* Sessions Table */}
        <div className="border border-white/10 rounded-lg overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow className="border-white/10">
                <TableHead>Customer</TableHead>
                <TableHead>Transport</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Messages</TableHead>

                <TableHead>Last Activity</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredSessions.map((session) => (
                <TableRow key={session.id} className="border-white/10">
                  <TableCell>
                    <div>
                      <div className="font-medium">
                        {session.externalUserId}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {session.externalUserId}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getTransportIcon(session?.transport?.name)}
                      <span className="capitalize">
                        {session?.transport?.name}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant="secondary"
                      className={getStatusColor(session.status)}
                    >
                      {session.status}
                    </Badge>
                  </TableCell>
                  <TableCell>{session?.messages?.length}</TableCell>

                  <TableCell>
                    <div className="text-sm">
                      {formatTime(session.updatedAt)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      {/* View Session Dialog */}
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            title="View conversation"
                          >
                            <Eye className="w-3 h-3" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                          <DialogHeader>
                            <DialogTitle>
                              Session Details - {session.externalUserId}
                            </DialogTitle>
                            <DialogDescription>
                              Session {session.id} • {session?.transport?.name}{" "}
                              • Started {formatTime(session.createdAt)}
                            </DialogDescription>
                          </DialogHeader>
                          <div className="max-h-96 overflow-y-auto space-y-3">
                            {session?.messages?.map((msg) => (
                              <div
                                key={msg.id}
                                className={`flex ${
                                  msg.role === "user"
                                    ? "justify-start"
                                    : "justify-end"
                                }`}
                              >
                                <div
                                  className={`max-w-xs p-3 rounded-lg ${
                                    msg.role === "user"
                                      ? "bg-muted text-foreground"
                                      : "bg-primary text-primary-foreground"
                                  }`}
                                >
                                  <div className="text-sm">{msg.content}</div>
                                  <div className="text-xs opacity-70 mt-1">
                                    {formatTime(msg.createdAt)}
                                  </div>
                                </div>
                              </div>
                            )) || (
                              <div className="text-center text-muted-foreground py-4">
                                No conversation history available
                              </div>
                            )}
                          </div>
                        </DialogContent>
                      </Dialog>

                      {/* Send Message Dialog */}
                      {/* {session.status === "OPEN" && (
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              title="Send message"
                            >
                              <Send className="w-3 h-3" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>
                                Send Message to {session.externalUserId}
                              </DialogTitle>
                              <DialogDescription>
                                Send a direct message to the customer in session{" "}
                                {session.id}
                              </DialogDescription>
                            </DialogHeader>
                            <form
                              onSubmit={(e) => {
                                e.preventDefault();
                                const formData = new FormData(e.currentTarget);
                                const message = formData.get(
                                  "message"
                                ) as string;
                                if (message.trim()) {
                                  handleSendMessage(session.id, message);
                                  e.currentTarget.reset();
                                }
                              }}
                            >
                              <div className="space-y-4">
                                <div>
                                  <Label htmlFor="message">Message</Label>
                                  <Textarea
                                    id="message"
                                    name="message"
                                    placeholder="Type your message here..."
                                    rows={4}
                                    required
                                  />
                                </div>
                                <div className="flex justify-end gap-2">
                                  <DialogTrigger asChild>
                                    <Button variant="outline" type="button">
                                      Cancel
                                    </Button>
                                  </DialogTrigger>
                                  <Button type="submit">
                                    <Send className="w-4 h-4 mr-2" />
                                    Send Message
                                  </Button>
                                </div>
                              </div>
                            </form>
                          </DialogContent>
                        </Dialog>
                      )} */}

                      {/* Close Session Button */}
                      {session.status === "OPEN" && (
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              title="Close session"
                            >
                              <X className="w-3 h-3" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Close Session</DialogTitle>
                              <DialogDescription>
                                Are you sure you want to close this session with{" "}
                                {session.externalUserId}?
                              </DialogDescription>
                            </DialogHeader>
                            <div className="flex items-center gap-3 p-4 bg-muted/50 rounded-lg">
                              <AlertTriangle className="w-5 h-5 text-yellow-500" />
                              <div className="text-sm">
                                Once closed, this session cannot be reopened.
                                The customer will need to start a new
                                conversation.
                              </div>
                            </div>
                            <div className="flex justify-end gap-2">
                              <DialogTrigger asChild>
                                <Button variant="outline">Cancel</Button>
                              </DialogTrigger>
                              <Button
                                variant="destructive"
                                onClick={() => handleCloseSession(session.id)}
                              >
                                Close Session
                              </Button>
                            </div>
                          </DialogContent>
                        </Dialog>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {isLoading ? (
          <div className="space-y-4">
            {/* Table skeleton */}
            <div className="border border-white/10 rounded-lg overflow-hidden">
              <div className="p-4 space-y-3">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="flex items-center gap-4">
                    <Skeleton className="h-10 w-24" />
                    <Skeleton className="h-10 w-20" />
                    <Skeleton className="h-6 w-16 rounded-full" />
                    <Skeleton className="h-10 w-12" />
                    <Skeleton className="h-10 w-32" />
                    <div className="flex gap-2">
                      <Skeleton className="h-8 w-8" />
                      <Skeleton className="h-8 w-8" />
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Stats skeleton */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-white/10">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="text-center space-y-2">
                  <Skeleton className="h-8 w-12 mx-auto" />
                  <Skeleton className="h-4 w-16 mx-auto" />
                </div>
              ))}
            </div>
          </div>
        ) : filteredSessions.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            {sessions.length === 0 ? (
              <>
                <MessageSquare className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <h3 className="text-lg font-medium mb-2">No sessions yet</h3>
                <p>
                  Customer sessions will appear here once they start interacting
                  with your agent
                </p>
              </>
            ) : (
              <>
                <Search className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <h3 className="text-lg font-medium mb-2">No sessions found</h3>
                <p>Try adjusting your search or filter criteria</p>
              </>
            )}
          </div>
        ) : null}

        {/* Summary Stats */}
        {!isLoading && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-white/10">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-500">
                {sessions.filter((s) => s.status === "OPEN").length}
              </div>
              <div className="text-sm text-muted-foreground">Active</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-500">
                {sessions.filter((s) => s.status === "CLOSED").length}
              </div>
              <div className="text-sm text-muted-foreground">Pending</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {sessions.length}
              </div>
              <div className="text-sm text-muted-foreground">Total Today</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-foreground">
                {Math.floor(
                  sessions.reduce((acc, s) => acc + s?.messages?.length, 0) /
                    sessions.length
                ) || 0}
              </div>
              <div className="text-sm text-muted-foreground">Avg Messages</div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SessionMonitor;
