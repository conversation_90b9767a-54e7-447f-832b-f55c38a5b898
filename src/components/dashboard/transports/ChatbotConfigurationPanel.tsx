import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Save, RotateCcw, User, Palette } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import AvatarUpload from "@/components/ui/avatar-upload";
import ColorPicker from "@/components/ui/color-picker";
import ChatbotPreview from "@/components/chatbot/ChatbotPreview";

interface ChatbotFormData {
  businessName: string;
  agentName: string;
  primaryColor: string;
  secondaryColor: string;
  chatBubbleStyle: 'rounded' | 'square' | 'pill';
  avatarUrl?: string;
}

interface ChatbotConfigurationPanelProps {
  initialConfig: ChatbotFormData;
  onSave: (config: ChatbotFormData) => void;
  onCancel: () => void;
  isLoading: boolean;
  showPreview?: boolean;
}

const DEFAULT_CONFIG: ChatbotFormData = {
  businessName: "Your Business",
  agentName: "AI Assistant",
  primaryColor: "#ff6600",
  secondaryColor: "#f0f0f0",
  chatBubbleStyle: "rounded",
  avatarUrl: undefined
};

const ChatbotConfigurationPanel: React.FC<ChatbotConfigurationPanelProps> = ({
  initialConfig,
  onSave,
  onCancel,
  isLoading,
  showPreview = false
}) => {
  const [previewConfig, setPreviewConfig] = useState<ChatbotFormData>(initialConfig);
  const [isResetDialogOpen, setIsResetDialogOpen] = useState(false);
  const [isResetting, setIsResetting] = useState(false);

  const { register, handleSubmit, setValue, watch, formState: { errors } } = useForm<ChatbotFormData>({
    defaultValues: initialConfig
  });

  // Watch form values for real-time preview
  const watchedValues = watch();
  
  useEffect(() => {
    setPreviewConfig(watchedValues);
  }, [watchedValues]);

  const onSubmit = (data: ChatbotFormData) => {
    onSave(data);
  };

  const handleResetToDefault = async () => {
    setIsResetting(true);
    
    try {
      // Reset form values
      Object.keys(DEFAULT_CONFIG).forEach((key) => {
        setValue(key as keyof ChatbotFormData, DEFAULT_CONFIG[key as keyof ChatbotFormData] as any);
      });
      
      // Reset preview config
      setPreviewConfig(DEFAULT_CONFIG);
      
      // Close dialog
      setIsResetDialogOpen(false);
      
    } catch (error) {
      console.error('Failed to reset theme:', error);
    } finally {
      setIsResetting(false);
    }
  };

  return (
    <div className={`grid ${showPreview ? 'grid-cols-1 lg:grid-cols-2' : 'grid-cols-1'} gap-4 lg:gap-6`}>
      {/* Configuration Form */}
      <div className={`space-y-4 lg:space-y-6 ${showPreview ? 'order-2 lg:order-1' : ''}`}>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Configuration */}
          <div className="space-y-4">
            <h4 className="font-medium flex items-center gap-2">
              <User className="w-4 h-4" />
              Basic Information
            </h4>
            
            <div className="space-y-2">
              <Label htmlFor="businessName">Business Name</Label>
              <Input
                id="businessName"
                {...register("businessName", { required: "Business name is required" })}
                placeholder="Enter your business name"
              />
              {errors.businessName && (
                <p className="text-sm text-destructive">{errors.businessName.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="agentName">AI Agent Name</Label>
              <Input
                id="agentName"
                {...register("agentName", { required: "Agent name is required" })}
                placeholder="Enter your AI agent name"
              />
              {errors.agentName && (
                <p className="text-sm text-destructive">{errors.agentName.message}</p>
              )}
            </div>
          </div>

          <Separator />

          {/* Avatar Upload */}
          <div className="space-y-4">
            <h4 className="font-medium flex items-center gap-2">
              <User className="w-4 h-4" />
              Agent Avatar
            </h4>
            
            <AvatarUpload
              currentAvatar={watchedValues.avatarUrl}
              onAvatarChange={(avatarUrl) => {
                setValue("avatarUrl", avatarUrl || "");
              }}
            />
          </div>

          <Separator />

          {/* Styling Options */}
          <div className="space-y-4">
            <h4 className="font-medium flex items-center gap-2">
              <Palette className="w-4 h-4" />
              Styling Options
            </h4>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <ColorPicker
                label="Primary Color"
                value={watchedValues.primaryColor}
                onChange={(color) => setValue("primaryColor", color)}
              />

              <ColorPicker
                label="Secondary Color"
                value={watchedValues.secondaryColor}
                onChange={(color) => setValue("secondaryColor", color)}
              />
            </div>

            <div className="space-y-2">
              <Label>Chat Bubble Style</Label>
              <div className="flex gap-2">
                {(['rounded', 'square', 'pill'] as const).map((style) => (
                  <Button
                    key={style}
                    type="button"
                    variant={watchedValues.chatBubbleStyle === style ? "default" : "outline"}
                    size="sm"
                    onClick={() => setValue("chatBubbleStyle", style)}
                    className="capitalize"
                  >
                    {style}
                  </Button>
                ))}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2 pt-4">
              <Dialog open={isResetDialogOpen} onOpenChange={setIsResetDialogOpen}>
                <DialogTrigger asChild>
                  <Button 
                    type="button" 
                    variant="outline" 
                    className="flex-1"
                    disabled={isLoading}
                  >
                    <RotateCcw className="w-4 h-4 mr-2" />
                    Reset to Default
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Reset to Default Theme</DialogTitle>
                    <DialogDescription>
                      This will reset all customization settings to their default values. This action cannot be undone.
                    </DialogDescription>
                  </DialogHeader>
                  <DialogFooter>
                    <Button
                      variant="outline"
                      onClick={() => setIsResetDialogOpen(false)}
                      disabled={isResetting}
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="destructive"
                      onClick={handleResetToDefault}
                      disabled={isResetting}
                    >
                      {isResetting ? "Resetting..." : "Reset Theme"}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>

              <Button 
                type="submit" 
                className="flex-1" 
                disabled={isLoading}
              >
                <Save className="w-4 h-4 mr-2" />
                {isLoading ? "Saving..." : "Save Configuration"}
              </Button>
            </div>
          </div>
        </form>

        {/* Cancel Button */}
        <div className="flex justify-end">
          <Button 
            variant="outline" 
            onClick={onCancel}
            disabled={isLoading}
          >
            Cancel
          </Button>
        </div>
      </div>

      {/* Live Preview */}
      {showPreview && (
        <div className={`space-y-4 lg:space-y-6 ${showPreview ? 'order-1 lg:order-2' : ''}`}>
          <div className="space-y-4">
            <h4 className="font-medium flex items-center gap-2">
              <Palette className="w-4 h-4" />
              Live Preview
            </h4>
            <div className="border rounded-lg p-2 sm:p-4 bg-background min-h-[300px] sm:min-h-[400px] lg:min-h-[500px] flex items-center justify-center">
              <div className="w-full max-w-xs sm:max-w-sm">
                <ChatbotPreview config={previewConfig} />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatbotConfigurationPanel;
