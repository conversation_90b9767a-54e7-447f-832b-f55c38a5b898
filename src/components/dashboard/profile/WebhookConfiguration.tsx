import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Webhook, TestTube, Save } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
const WebhookConfiguration = () => {
  const {
    toast
  } = useToast();
  const [webhookUrl, setWebhookUrl] = useState("https://api.yourcompany.com/webhooks/dezh");
  const [events, setEvents] = useState({
    subscriptionUpdated: true,
    usageLimitReached: true,
    paymentFailed: false,
    agentCreated: true
  });
  const testWebhook = () => {
    toast({
      title: "Testing webhook",
      description: "Sending test payload to your endpoint..."
    });
  };
  const saveConfiguration = () => {
    toast({
      title: "Webhook configuration saved",
      description: "Your webhook settings have been updated."
    });
  };
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Webhook className="h-5 w-5" />
          Webhook Configuration
        </CardTitle>
        <CardDescription>
          Configure webhooks to receive real-time notifications
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="webhook-url">Webhook URL</Label>
          <div className="flex gap-2">
            <Input
              id="webhook-url"
              value={webhookUrl}
              onChange={(e) => setWebhookUrl(e.target.value)}
              placeholder="https://api.yourcompany.com/webhooks/dezh"
            />
            <Button variant="outline" onClick={testWebhook}>
              <TestTube className="h-4 w-4 mr-2" />
              Test
            </Button>
          </div>
        </div>

        <div className="space-y-4">
          <Label>Event Subscriptions</Label>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Subscription Updated</Label>
                <p className="text-sm text-muted-foreground">
                  When billing or subscription changes
                </p>
              </div>
              <Switch
                checked={events.subscriptionUpdated}
                onCheckedChange={(checked) =>
                  setEvents({ ...events, subscriptionUpdated: checked })
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Usage Limit Reached</Label>
                <p className="text-sm text-muted-foreground">
                  When credit usage approaches limits
                </p>
              </div>
              <Switch
                checked={events.usageLimitReached}
                onCheckedChange={(checked) =>
                  setEvents({ ...events, usageLimitReached: checked })
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Payment Failed</Label>
                <p className="text-sm text-muted-foreground">
                  When automatic payments fail
                </p>
              </div>
              <Switch
                checked={events.paymentFailed}
                onCheckedChange={(checked) =>
                  setEvents({ ...events, paymentFailed: checked })
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Agent Created</Label>
                <p className="text-sm text-muted-foreground">
                  When new AI agents are deployed
                </p>
              </div>
              <Switch
                checked={events.agentCreated}
                onCheckedChange={(checked) =>
                  setEvents({ ...events, agentCreated: checked })
                }
              />
            </div>
          </div>
        </div>

        <Button onClick={saveConfiguration} className="w-full">
          <Save className="h-4 w-4 mr-2" />
          Save Configuration
        </Button>
      </CardContent>
    </Card>
  );
};
export default WebhookConfiguration;