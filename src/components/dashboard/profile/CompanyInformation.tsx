import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiService } from "@/services/api";
import { Building2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Textarea } from "@/components/ui/textarea";

const CompanyInformation = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: userSettings, isLoading } = useQuery({
    queryKey: ["user-settings"],
    queryFn: () => apiService.getUserSettings(),
  });
  const [formData, setFormData] = useState({
    businessName: "",
    businessType: "",
    agentName: "",
    businessDescription:""
  });

  React.useEffect(() => {
    if (userSettings) {
      const settings = userSettings;
      setFormData({
        businessName: settings.businessName || "",
        businessType: settings.businessType || "",
        agentName: settings.agentName || "",
        businessDescription: settings.businessDescription || "",
      });
    }
  }, [userSettings]);

  const updateMutation = useMutation({
    mutationFn: (data: typeof formData) => apiService.updateUserSettings(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["user-settings"] });
      toast({
        title: "Settings updated",
        description: "Your company information has been saved successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Update failed",
        description: "There was an error updating your settings.",
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateMutation.mutate(formData);
  };

  if (isLoading) {
    return (
      <Card className="glassmorphism border-white/10">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="w-5 h-5" />
            Company Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-10 bg-white/10 rounded"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="glassmorphism border-white/10">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building2 className="w-5 h-5" />
          Company Information
        </CardTitle>
        <CardDescription>
          Update your business details and agent configuration
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="businessName">Business Name</Label>
            <Input
              id="businessName"
              value={formData.businessName}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  businessName: e.target.value,
                }))
              }
              placeholder="Enter your business name"
            />
          </div>

          <div>
            <Label htmlFor="businessType">Business Type</Label>
            <Input
              id="businessType"
              value={formData.businessType}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  businessType: e.target.value,
                }))
              }
              placeholder="e.g., E-commerce, SaaS, Consulting"
            />
          </div>

          <div>
            <Label htmlFor="agentName">Agent Name</Label>
            <Input
              id="agentName"
              value={formData.agentName}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, agentName: e.target.value }))
              }
              placeholder="Enter your AI agent's name"
            />
          </div>
          <div>
            <Label htmlFor="business-description">Business Description</Label>
            <Textarea
              id="business-description"
              value={formData.businessDescription}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  businessDescription: e.target.value,
                }))
              }
              placeholder="Enter your AI agent's name"
            />
          </div>

          <Button
            type="submit"
            className="w-full"
            disabled={updateMutation.isPending}
          >
            {updateMutation.isPending ? "Saving..." : "Save Changes"}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export default CompanyInformation;
