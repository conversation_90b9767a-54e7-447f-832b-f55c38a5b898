import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { UserCircle, Camera, Save, LogOut } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { SignOutButton, useUser } from "@clerk/clerk-react";
import { useNavigate } from "react-router-dom";

const UserProfile = () => {
  const navigate = useNavigate();

  const { toast } = useToast();
  const { user } = useUser();
  const [userInfo, setUserInfo] = useState({
    name: user?.fullName,
    email: user?.emailAddresses[0].emailAddress,
    avatar: user?.imageUrl,
  });

  const handleSave = () => {
    toast({
      title: "Profile updated",
      description: "Your profile information has been saved successfully.",
    });
  };

  const handleAvatarChange = () => {
    // In a real app, this would open file picker
    toast({
      title: "Avatar update",
      description: "Photo upload functionality would be implemented here.",
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <UserCircle className="h-5 w-5" />
          User Profile
        </CardTitle>
        <CardDescription>
          Manage your personal account information
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center gap-6">
          <div className="relative">
            <Avatar className="h-20 w-20">
              <AvatarImage src={userInfo.avatar} alt={userInfo.name} />
              <AvatarFallback className="text-lg">
                {userInfo.name
                  .split(" ")
                  .map((n) => n[0])
                  .join("")}
              </AvatarFallback>
            </Avatar>
          </div>
          <div className="flex-1 space-y-1">
            <h3 className="text-lg font-medium text-foreground">
              {userInfo.name}
            </h3>
            <p className="text-sm text-muted-foreground">{userInfo.email}</p>
          </div>
        </div>

        <div className="grid gap-4">
          <div className="space-y-2">
            <Label htmlFor="user-name">Full Name</Label>
            <Input
              disabled
              id="user-name"
              value={userInfo.name}
              onChange={(e) =>
                setUserInfo({ ...userInfo, name: e.target.value })
              }
              placeholder="Enter your full name"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="user-email">Email Address</Label>
            <Input
              disabled
              id="user-email"
              type="email"
              value={userInfo.email}
              onChange={(e) =>
                setUserInfo({ ...userInfo, email: e.target.value })
              }
              placeholder="Enter your email address"
            />
          </div>
        </div>
        <SignOutButton>
          <Button className="w-full">
            Log out
            <LogOut className="w-4 h-4" />
          </Button>
        </SignOutButton>
      </CardContent>
    </Card>
  );
};

export default UserProfile;
