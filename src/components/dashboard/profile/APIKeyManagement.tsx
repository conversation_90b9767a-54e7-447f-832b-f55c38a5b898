import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Key, Copy, RotateCcw, Trash2, Plus } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
const APIKeyManagement = () => {
  const {
    toast
  } = useToast();
  const [apiKeys] = useState([{
    id: 1,
    name: "Production API",
    key: "sk-dezh-prod-abc123...",
    created: "2024-01-15",
    lastUsed: "2024-01-26",
    usage: 15420
  }, {
    id: 2,
    name: "Development API",
    key: "sk-dezh-dev-xyz789...",
    created: "2024-01-10",
    lastUsed: "2024-01-26",
    usage: 3240
  }]);
  const copyToClipboard = (key: string) => {
    navigator.clipboard.writeText(key);
    toast({
      title: "API key copied",
      description: "The API key has been copied to your clipboard."
    });
  };
  const regenerateKey = (name: string) => {
    toast({
      title: "API key regenerated",
      description: `A new key has been generated for ${name}.`
    });
  };
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Key className="h-5 w-5" />
          API Key Management
        </CardTitle>
        <CardDescription>
          Manage your API keys for programmatic access
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">Active API Keys</h3>
          <Button size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Generate New Key
          </Button>
        </div>

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Key</TableHead>
              <TableHead>Created</TableHead>
              <TableHead>Last Used</TableHead>
              <TableHead>Usage</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {apiKeys.map((key) => (
              <TableRow key={key.id}>
                <TableCell className="font-medium">{key.name}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <code className="bg-muted px-2 py-1 rounded text-sm">
                      {key.key}
                    </code>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => copyToClipboard(key.key)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
                <TableCell>{key.created}</TableCell>
                <TableCell>{key.lastUsed}</TableCell>
                <TableCell>
                  <Badge variant="secondary">{key.usage.toLocaleString()} calls</Badge>
                </TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => regenerateKey(key.name)}
                    >
                      <RotateCcw className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="destructive">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};
export default APIKeyManagement;