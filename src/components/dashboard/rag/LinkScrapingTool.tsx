import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Globe, Plus } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useMutation } from "@tanstack/react-query";
import { apiService } from "@/services/api";

const LinkScrapingTool = () => {
  const { toast } = useToast();
  const [url, setUrl] = useState("");
  const [schedule, setSchedule] = useState("manual");

  const addLinkMutation = useMutation({
    mutationFn: (link: string) => apiService.addLinkToKnowledgeBase(link),
    onMutate: () => {
      toast({
        title: "Scraping started",
        description: `Processing content...`,
      });
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Link has been added successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to add link.",
        variant: "destructive",
      });
    },
  });

  const handleScrape = () => {
    if (!url) return;
    addLinkMutation.mutate(url);
    setUrl("");
  };

  return (
    <Card className="glassmorphism border-white/10">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Globe className="w-5 h-5" />
          Link Scraping Tool
        </CardTitle>
        <CardDescription>
          Extract content from web pages and add to knowledge base
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="url">Website URL</Label>
            <div className="flex gap-2">
              <Input
                id="url"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                placeholder="https://example.com"
              />
              <Button onClick={handleScrape} disabled={!url}>
                <Plus className="w-4 h-4 mr-2" />
                Scrape
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="schedule">Scraping Schedule</Label>
            <Select value={schedule} onValueChange={setSchedule}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="manual">Manual only</SelectItem>
                <SelectItem value="daily">Daily</SelectItem>
                <SelectItem value="weekly">Weekly</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default LinkScrapingTool;
