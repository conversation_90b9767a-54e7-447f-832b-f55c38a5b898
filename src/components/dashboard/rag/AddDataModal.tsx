import React from "react";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Card, CardContent } from "@/components/ui/card";
import { Globe, FileText, MessageSquare, Type } from "lucide-react";

interface AddDataModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectOption: (option: string) => void;
}

const AddDataModal = ({ isOpen, onClose, onSelectOption }: AddDataModalProps) => {
  const options = [
    {
      id: "link",
      title: "Link Scraping",
      description: "Extract content from web pages",
      icon: Globe,
    },
    {
      id: "document",
      title: "Document Upload",
      description: "Upload PDF and other documents",
      icon: FileText,
    },
    {
      id: "text",
      title: "Plain Text",
      description: "Add text content directly",
      icon: Type,
    },
    {
      id: "qa",
      title: "Q&A Upload",
      description: "Add question and answer pairs",
      icon: MessageSquare,
    },
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Add Data Source</DialogTitle>
          <DialogDescription>
            Choose how you want to add data to your knowledge base
          </DialogDescription>
        </DialogHeader>
        <div className="grid grid-cols-2 gap-4 p-4">
          {options.map((option) => {
            const Icon = option.icon;
            return (
              <Card
                key={option.id}
                className="cursor-pointer transition-all hover:border-primary hover:shadow-md"
                onClick={() => onSelectOption(option.id)}
              >
                <CardContent className="p-6 text-center">
                  <Icon className="w-12 h-12 text-primary mx-auto mb-3" />
                  <h3 className="font-semibold mb-2">{option.title}</h3>
                  <p className="text-sm text-muted-foreground">{option.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AddDataModal;