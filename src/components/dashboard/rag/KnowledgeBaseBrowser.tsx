import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis,
} from "@/components/ui/pagination";
import {
  Search,
  FileText,
  Link,
  MessageSquare,
  Type,
  Edit,
  Trash2,
  CheckCircle,
  Clock,
  AlertCircle,
  Plus,
  ArrowLeft,
  Eye,
  Copy,
  ChevronRight,
  Home,
  Database,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import AddDataModal from "./AddDataModal";
import LinkScrapingTool from "./LinkScrapingTool";
import DocumentUpload from "./DocumentUpload";
import PlainTextUpload from "./PlainTextUpload";
import QAUpload from "./QAUpload";
import RecentResourcesTable from "./RecentResourcesTable";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiService } from "@/services/api";
import Searchfield from "@/components/ui/Searchfield";
import { useSearchParams } from "react-router-dom";
import TableSkeleton from "@/components/ui/TableSkeleton";
import DataSourcesOverview from "./DataSourcesOverview";
import HelpIcon from "@/components/ui/help-icon";

export interface KnowledgeItem {
  id: string;
  payload: {
    content: string;
    created_at: string;
    metadata: {
      source_type: "Html" | string;
      source_hash: string;
    };
  };
}
const KnowledgeBaseBrowser = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const [searchParams] = useSearchParams();
  const [currentPage, setCurrentPage] = useState(1);
  const [editingItem, setEditingItem] = useState<KnowledgeItem | null>(null);
  const [viewingContent, setViewingContent] = useState<KnowledgeItem | null>(null);

  const [editContent, setEditContent] = useState("");
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [activeDataTool, setActiveDataTool] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("knowledge-base");
  const itemsPerPage = 10;
  const search = searchParams.get("search") || "";

  const { data: knowledgeBaseResponse, isLoading } = useQuery({
    queryKey: ["knowledge-base-vectors", currentPage, search],
    queryFn: () =>
      apiService.getKnowledgeBaseVectors(currentPage, itemsPerPage, search),
    placeholderData: (previousData) => previousData,
  });
  const knowledgeBase = knowledgeBaseResponse?.items || [];

  const totalPages = knowledgeBaseResponse?.pagination?.total;
  const clearKnowledgeBaseMutation = useMutation<void, Error, string>({
    mutationFn: (id) => apiService.clearVectorKnowledgeBase(id),
    onMutate: () => {
      toast({
        title: "Clearing...",
        description: "Removing all knowledge base items.",
      });
    },
    onSuccess: () => {
      toast({
        title: "Knowledge Base Cleared",
        description: "All items have been successfully removed.",
      });

      queryClient.invalidateQueries({
        queryKey: ["knowledge-base-vectors"],
        exact: false,
      });
    },

    onError: () => {
      toast({
        title: "Error",
        description: "Failed to clear the knowledge base.",
        variant: "destructive",
      });
    },
  });

  const handleSaveEdit = () => {
    // Simulate API call
    toast({
      title: "Item Updated",
      description: "Knowledge base item has been successfully updated.",
    });
    setEditingItem(null);
    setEditContent("");
  };

  const handleDeleteItem = (itemId: string) => {
    clearKnowledgeBaseMutation.mutate(itemId);
  };

  const handleClearAll = () => {
    toast({
      title: "All items cleared",
      description: "All knowledge base items have been cleared.",
    });
  };

  const handleAddDataOption = (option: string) => {
    setIsAddModalOpen(false);
    setActiveDataTool(option);
  };

  const handleBackToTable = () => {
    setActiveDataTool(null);
    // Switch to recent resources tab to show new upload
    setActiveTab("recent-resources");
  };

  const handleCopyContent = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      toast({
        title: "Copied!",
        description: "Content copied to clipboard.",
      });
    } catch (err) {
      toast({
        title: "Failed to copy",
        description: "Could not copy content to clipboard.",
        variant: "destructive",
      });
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "Md":
        return <Type className="w-4 h-4 text-blue-500" />;

      case "Html":
        return <Link className="w-4 h-4 text-green-500" />;
      case "other":
        return <MessageSquare className="w-4 h-4 text-purple-500" />;
      default:
        return <FileText className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "processed":
        return "bg-green-500 text-white";
      case "processing":
        return "bg-yellow-500 text-white";
      case "error":
        return "bg-red-500 text-white";
      default:
        return "bg-gray-500 text-white";
    }
  };

  // If a data tool is active, show that tool instead of the table
  if (activeDataTool) {
    const renderActiveTool = () => {
      switch (activeDataTool) {
        case "link":
          return <LinkScrapingTool />;
        case "document":
          return <DocumentUpload />;
        case "text":
          return <PlainTextUpload />;
        case "qa":
          return <QAUpload />;
        default:
          return null;
      }
    };

    const getToolTitle = () => {
      switch (activeDataTool) {
        case "link":
          return "Link Scraping";
        case "document":
          return "Document Upload";
        case "text":
          return "Plain Text Upload";
        case "qa":
          return "Q&A Upload";
        default:
          return "";
      }
    };

    return (
      <div className="space-y-6">
        {/* Enhanced Header with Breadcrumb Navigation */}
        <div className="bg-card border rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBackToTable}
                className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
              >
                <ArrowLeft className="w-4 h-4" />
                Back
              </Button>
              <ChevronRight className="w-4 h-4 text-muted-foreground" />
              <div className="flex items-center gap-2">
                <Home className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">Knowledge Base</span>
              </div>
              <ChevronRight className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm font-medium">{getToolTitle()}</span>
            </div>
          </div>
          <div className="mt-3">
            <h2 className="text-xl font-semibold">{getToolTitle()}</h2>
            <p className="text-sm text-muted-foreground mt-1">
              Add new content to your knowledge base
            </p>
          </div>
        </div>
        {renderActiveTool()}
      </div>
    );
  }

  return (
      <div className="space-y-6">
        <DataSourcesOverview isVisible={!activeDataTool} />
        
        {/* Enhanced Header with Better Spacing */}
        <div className="bg-card/50 border border-border/50 rounded-xl p-6 space-y-4">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <h2 className="text-xl font-semibold text-foreground">Knowledge Base Content</h2>
                <HelpIcon
                  href="/guides/knowledge-base"
                  tooltip="View knowledge base management guide"
                  size="sm"
                />
              </div>
              <p className="text-sm text-muted-foreground max-w-2xl">
                Browse, search, and manage your knowledge base content and recent uploads
              </p>
            </div>
            <div className="flex flex-wrap gap-3">
              <Button
                onClick={() => setIsAddModalOpen(true)}
                className="flex items-center gap-2 shadow-sm hover:shadow-md transition-all"
                size="default"
              >
                <Plus className="w-4 h-4" />
                Add New Data
              </Button>
              {activeTab === "knowledge-base" && (
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button 
                      disabled 
                      variant="outline" 
                      size="default"
                      className="border-destructive/30 text-destructive hover:bg-destructive/10"
                    >
                      Clear All Data
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent className="max-w-md">
                    <AlertDialogHeader>
                      <AlertDialogTitle className="text-destructive">⚠️ Delete All Content</AlertDialogTitle>
                      <AlertDialogDescription className="text-sm leading-relaxed">
                        This action will permanently delete all items from your knowledge base. 
                        This cannot be undone and will affect your AI agent's knowledge.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction 
                        onClick={handleClearAll}
                        className="bg-destructive hover:bg-destructive/90"
                      >
                        Delete Everything
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              )}
            </div>
          </div>
        </div>

        {/* Enhanced Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 h-11 bg-muted/30 p-1">
            <TabsTrigger 
              value="knowledge-base" 
              className="data-[state=active]:bg-background data-[state=active]:shadow-sm font-medium"
            >
              📚 Knowledge Base
            </TabsTrigger>
            <TabsTrigger 
              value="recent-resources"
              className="data-[state=active]:bg-background data-[state=active]:shadow-sm font-medium"
            >
              🕒 Recent Uploads
            </TabsTrigger>
          </TabsList>

          <TabsContent value="knowledge-base" className="space-y-6 mt-6">
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              <div className="flex-1 max-w-md">
                <Searchfield />
              </div>
              <div className="text-sm text-muted-foreground bg-muted/30 px-3 py-2 rounded-lg">
                {knowledgeBase.length} items total
              </div>
            </div>
            
            {/* Enhanced Table Container */}
            <div className="border border-border/50 rounded-xl overflow-hidden bg-card/30 backdrop-blur-sm">
              <Table>
                <TableHeader className="bg-muted/20">
                  <TableRow className="border-border/50 hover:bg-transparent">
                    <TableHead className="font-semibold text-foreground/80">Type</TableHead>
                    <TableHead className="font-semibold text-foreground/80">Content Preview</TableHead>
                    <TableHead className="font-semibold text-foreground/80">Created Date</TableHead>
                    <TableHead className="font-semibold text-foreground/80 text-center">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableSkeleton />
                  ) : knowledgeBase.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={4} className="h-32">
                        <div className="flex flex-col items-center justify-center text-center space-y-3">
                          <div className="w-12 h-12 rounded-full bg-muted/50 flex items-center justify-center">
                            <Database className="w-6 h-6 text-muted-foreground" />
                          </div>
                          <div className="space-y-1">
                            <p className="text-sm font-medium text-foreground">No knowledge base content</p>
                            <p className="text-xs text-muted-foreground max-w-sm">
                              Start building your AI agent's knowledge by adding documents, links, or text content
                            </p>
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    knowledgeBase.map((item, index) => (
                      <TableRow key={item.id} className="hover:bg-muted/30 transition-colors border-border/30">
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <div className="p-2 rounded-lg bg-primary/10">
                              {getTypeIcon(item.payload.metadata.source_type)}
                            </div>
                            <div className="space-y-1">
                              <span className="text-xs font-medium uppercase tracking-wide text-primary">
                                {item.payload.metadata.source_type}
                              </span>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell title={item?.payload?.content}>
                          <div className="space-y-1 max-w-md">
                            <div className="text-sm text-foreground line-clamp-2 leading-relaxed">
                              {item?.payload?.content.length > 100
                                ? `${item?.payload?.content.substring(0, 100)}...`
                                : item?.payload?.content}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {item?.payload?.content.length} characters
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="text-sm text-muted-foreground">
                          <div className="flex items-center gap-2">
                            <Clock className="w-3 h-3" />
                            {new Date(+item?.payload?.created_at * 1000).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric'
                            })}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex justify-center gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setViewingContent(item)}
                              title="View full content"
                              className="hover:bg-primary/10 hover:text-primary"
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button 
                                  variant="ghost" 
                                  size="sm"
                                  className="hover:bg-destructive/10 hover:text-destructive"
                                  title="Delete item"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle className="text-destructive">Delete Knowledge Item</AlertDialogTitle>
                                  <AlertDialogDescription className="text-sm leading-relaxed">
                                    Are you sure you want to delete this knowledge base item? 
                                    This action cannot be undone and may affect your AI agent's responses.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => handleDeleteItem(item.id)}
                                    className="bg-destructive hover:bg-destructive/90"
                                  >
                                    Delete Item
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
            {/* Enhanced Pagination with Consistent UI */}
            {totalPages > 1 && (
              <div className="flex flex-col sm:flex-row items-center justify-between gap-4 pt-6 border-t border-border/50">
                <div className="text-sm text-muted-foreground order-2 sm:order-1">
                  Showing page {currentPage} of {totalPages} ({knowledgeBase.length} items)
                </div>
                <div className="order-1 sm:order-2">
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                          className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                        />
                      </PaginationItem>

                      {/* Show first page if not in range */}
                      {currentPage > 3 && (
                        <>
                          <PaginationItem>
                            <PaginationLink
                              onClick={() => setCurrentPage(1)}
                              className="cursor-pointer"
                            >
                              1
                            </PaginationLink>
                          </PaginationItem>
                          {currentPage > 4 && (
                            <PaginationItem>
                              <PaginationEllipsis />
                            </PaginationItem>
                          )}
                        </>
                      )}

                      {/* Show pages around current page */}
                      {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                        const startPage = Math.max(1, currentPage - 2);
                        const page = startPage + i;

                        if (page > totalPages) return null;

                        return (
                          <PaginationItem key={page}>
                            <PaginationLink
                              onClick={() => setCurrentPage(page)}
                              isActive={currentPage === page}
                              className="cursor-pointer"
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        );
                      })}

                      {/* Show last page if not in range */}
                      {currentPage < totalPages - 2 && (
                        <>
                          {currentPage < totalPages - 3 && (
                            <PaginationItem>
                              <PaginationEllipsis />
                            </PaginationItem>
                          )}
                          <PaginationItem>
                            <PaginationLink
                              onClick={() => setCurrentPage(totalPages)}
                              className="cursor-pointer"
                            >
                              {totalPages}
                            </PaginationLink>
                          </PaginationItem>
                        </>
                      )}

                      <PaginationItem>
                        <PaginationNext
                          onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                          className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="recent-resources" className="mt-6">
            <RecentResourcesTable />
          </TabsContent>
        </Tabs>

      {/* View Content Dialog */}
      <Dialog open={!!viewingContent} onOpenChange={() => setViewingContent(null)}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <span>Knowledge Base Content</span>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-xs">
                  {viewingContent?.payload?.metadata?.source_type}
                </Badge>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleCopyContent(viewingContent?.payload?.content || "")}
                  title="Copy content"
                >
                  <Copy className="w-4 h-4" />
                </Button>
              </div>
            </DialogTitle>
            <DialogDescription>
              Created: {viewingContent && new Date(+viewingContent.payload.created_at * 1000).toLocaleString()}
            </DialogDescription>
          </DialogHeader>
          <div className="flex-1 overflow-auto">
            <div className="border rounded-lg p-4 bg-muted/30 max-h-96 overflow-auto">
              <pre className="whitespace-pre-wrap text-sm leading-relaxed font-mono">
                {viewingContent?.payload?.content}
              </pre>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={!!editingItem} onOpenChange={() => setEditingItem(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Knowledge Base Item</DialogTitle>
            <DialogDescription>
              Make changes to your knowledge base item here.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-content">Content</Label>
              <Textarea
                id="edit-content"
                value={editContent}
                onChange={(e) => setEditContent(e.target.value)}
                rows={6}
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setEditingItem(null)}>
                Cancel
              </Button>
              <Button onClick={handleSaveEdit}>Save Changes</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Add Data Modal */}
      <AddDataModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSelectOption={handleAddDataOption}
      />
    </div>
  );
};

export default KnowledgeBaseBrowser;
