import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { MessageSquare, Plus, Trash2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useMutation } from "@tanstack/react-query";
import { apiService } from "@/services/api";
import { QAPair } from "@/types/api";

interface LocalQAPair {
  id: string;
  question: string;
  answer: string;
}

const QAUpload = () => {
  const { toast } = useToast();
  const [qaPairs, setQaPairs] = useState<LocalQAPair[]>([
    { id: Date.now().toString(), question: "", answer: "" },
  ]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const addQAPair = () => {
    setQaPairs([
      ...qaPairs,
      { id: Date.now().toString(), question: "", answer: "" },
    ]);
  };

  const removeQAPair = (id: string) => {
    if (qaPairs.length > 1) {
      setQaPairs(qaPairs.filter((pair) => pair.id !== id));
    }
  };

  const updateQAPair = (
    id: string,
    field: "question" | "answer",
    value: string
  ) => {
    setQaPairs(
      qaPairs.map((pair) =>
        pair.id === id ? { ...pair, [field]: value } : pair
      )
    );
  };

  const addQAMutation = useMutation<void, Error, QAPair[]>({
    mutationFn: (pairs) => apiService.addQAToKnowledgeBase(pairs),
    onMutate: () => {
      toast({
        title: "Uploading Q&A...",
        description: "Please wait while your Q&A pairs are being added.",
      });
    },
    onSuccess: (_, variables) => {
      toast({
        title: "Success",
        description: `${variables.length} Q&A pair(s) added successfully.`,
      });
      setQaPairs([{ id: Date.now().toString(), question: "", answer: "" }]);
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to add Q&A pairs.",
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const validPairs = qaPairs.filter(
      (pair) => pair.question.trim() && pair.answer.trim()
    );

    if (validPairs.length === 0) {
      toast({
        title: "Error",
        description: "Please provide at least one complete Q&A pair.",
        variant: "destructive",
      });
      return;
    }
    const cleanedPairs = validPairs.map(({ question, answer }) => ({
      question,
      answer,
    }));

    addQAMutation.mutate(cleanedPairs);
  };

  return (
    <Card className="glassmorphism border-white/10">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="w-5 h-5" />
          Add Q&A Pairs
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {qaPairs.map((pair, index) => (
            <div
              key={pair.id}
              className="space-y-4 p-4 border border-white/10 rounded-lg"
            >
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium">Q&A Pair #{index + 1}</h4>
                {qaPairs.length > 1 && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeQAPair(pair.id)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor={`question-${pair.id}`}>Question</Label>
                <Textarea
                  id={`question-${pair.id}`}
                  placeholder="Enter the question..."
                  value={pair.question}
                  onChange={(e) =>
                    updateQAPair(pair.id, "question", e.target.value)
                  }
                  className="min-h-20"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor={`answer-${pair.id}`}>Answer</Label>
                <Textarea
                  id={`answer-${pair.id}`}
                  placeholder="Enter the answer..."
                  value={pair.answer}
                  onChange={(e) =>
                    updateQAPair(pair.id, "answer", e.target.value)
                  }
                  className="min-h-24"
                />
              </div>
            </div>
          ))}

          <div className="flex gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={addQAPair}
              className="flex-1"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Another Q&A Pair
            </Button>
          </div>

          <Button type="submit" disabled={isSubmitting} className="w-full">
            <MessageSquare className="w-4 h-4 mr-2" />
            {isSubmitting ? "Adding..." : "Add Q&A to Knowledge Base"}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export default QAUpload;
