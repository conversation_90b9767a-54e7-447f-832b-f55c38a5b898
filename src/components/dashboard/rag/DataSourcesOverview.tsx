import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Database,
  Globe,
  FileText,
  Clock,
  MessageSquare,
  Type,
  TrendingUp,
  Activity,
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { apiService } from "@/services/api";
interface DataSourcesOverviewProps {
  isVisible?: boolean;
}

const DataSourcesOverview = ({
  isVisible = true,
}: DataSourcesOverviewProps) => {
  const { data: link } = useQuery({
    queryKey: ["user-credit", "LINK"],
    queryFn: () =>
      apiService.getKnowledgeBase("select=id&filter=sourceType:eq:LINK"),
  });

  const { data: qa } = useQuery({
    queryKey: ["user-credit", "QA"],
    queryFn: () =>
      apiService.getKnowledgeBase("select=id&filter=sourceType:eq:QA"),
  });

  const { data: plain } = useQuery({
    queryKey: ["user-credit", "TEXT"],
    queryFn: () =>
      apiService.getKnowledgeBase("select=id&filter=sourceType:eq:TEXT"),
  });

  const { data: fileUploads } = useQuery({
    queryKey: ["user-credit", "FILE"],
    queryFn: () =>
      apiService.getKnowledgeBase("select=id&filter=sourceType:eq:FILE"),
  });

  const dataSources = [
    {
      name: "Q&A Content",
      type: "qa",
      count: qa?.data?.length ?? 0,
      status: "active",
      icon: MessageSquare,
    },
    {
      name: "Link Scraping",
      type: "link",
      count: link?.data?.length ?? 0,
      status: "processing",
      icon: Globe,
    },
    {
      name: "Plain Text",
      type: "text",
      count: plain?.data?.length ?? 0,
      status: "active",
      icon: Type,
    },
    {
      name: "File Uploads",
      type: "documents",
      count: fileUploads?.data?.length ?? 0,
      status: "active",
      icon: FileText,
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-500";
      case "processing":
        return "bg-yellow-500";
      case "error":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* Enhanced Section Header */}
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <Activity className="w-5 h-5 text-primary" />
          <h3 className="text-lg font-semibold text-foreground">
            Data Sources Overview
          </h3>
        </div>
        <p className="text-sm text-muted-foreground">
          Monitor your knowledge base content across different source types
        </p>
      </div>

      {/* Enhanced Cards Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {dataSources.map((source, index) => {
          const Icon = source.icon;
          return (
            <Card
              key={index}
              className="group hover:shadow-md transition-all duration-200 hover:-translate-y-1 border-l-4 border-l-primary/20 hover:border-l-primary"
            >
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <div className="space-y-1">
                  <CardTitle className="text-sm font-medium text-foreground group-hover:text-primary transition-colors">
                    {source.name}
                  </CardTitle>
                </div>
                <div className="p-2 bg-primary/10 rounded-lg group-hover:bg-primary/20 transition-colors">
                  <Icon className="h-4 w-4 text-primary" />
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-baseline gap-2">
                  <div className="text-3xl font-bold text-foreground">
                    {source.count}
                  </div>
                  <div className="text-xs text-muted-foreground">items</div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div
                      className={`h-2 w-2 rounded-full animate-pulse ${getStatusColor(
                        source.status
                      )}`}
                    />
                    <span className="text-xs font-medium capitalize text-muted-foreground">
                      {source.status}
                    </span>
                  </div>
                  <Clock className="w-3 h-3 text-muted-foreground" />
                </div>

                {/* <p className="text-xs text-muted-foreground">
                  Updated {source.lastUpdated}
                </p> */}
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};

export default DataSourcesOverview;
