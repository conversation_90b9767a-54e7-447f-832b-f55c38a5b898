import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { FileText, Upload } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useMutation } from "@tanstack/react-query";
import { apiService } from "@/services/api";

const PlainTextUpload = () => {
  const { toast } = useToast();
  const [content, setContent] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const addTextMutation = useMutation({
    mutationFn: (text: string) => apiService.addTextToKnowledgeBase(text),
    onMutate: () => {
      toast({
        title: "Adding text...",
        description:
          "We're processing the text and adding it to your knowledge base.",
      });
    },
    onSuccess: () => {
      toast({
        title: "Text Added",
        description: "Your text content has been added to the knowledge base.",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to add text.",
        variant: "destructive",
      });
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!content.trim()) {
      toast({
        title: "Error",
        description: "Please provide both title and content.",
        variant: "destructive",
      });
      return;
    }

    addTextMutation.mutate(content);

    setContent("");
    setIsSubmitting(false);
  };

  return (
    <Card className="glassmorphism border-white/10">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="w-5 h-5" />
          Add Plain Text
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="text-content">Content</Label>
            <Textarea
              id="text-content"
              placeholder="Enter your text content here..."
              className="min-h-32"
              value={content}
              onChange={(e) => setContent(e.target.value)}
            />
          </div>

          <Button
            type="submit"
            disabled={isSubmitting || !content.trim()}
            className="w-full"
          >
            <Upload className="w-4 h-4 mr-2" />
            {isSubmitting ? "Adding..." : "Add Text to Knowledge Base"}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export default PlainTextUpload;
