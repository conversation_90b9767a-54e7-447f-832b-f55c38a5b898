import React, { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  FileText,
  Link,
  MessageSquare,
  Type,
  Download,
  CheckCircle,
  Clock,
  AlertCircle,
  Upload,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import Searchfield from "@/components/ui/Searchfield";
import { useSearchParams } from "react-router-dom";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { useQuery } from "@tanstack/react-query";
import { API_BASE_URL, apiService } from "@/services/api";
import { KnowledgeBaseStatus, KnowledgeBaseType } from "@/types/api";
import { toast } from "@/components/ui/use-toast";

import TableSkeleton from "@/components/ui/TableSkeleton";

const RecentResourcesTable = () => {
  const [searchParams] = useSearchParams();
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const search = searchParams.get("search") || "";

  const { data: knowledgeBaseResponse, isLoading } = useQuery({
    queryKey: ["knowledge-base", search, currentPage],
    queryFn: () =>
      apiService.getKnowledgeBase(
        `sort=createdAt:DESC&search=${search}&page=${currentPage}`
      ),
  });

  const knowledgeBase = knowledgeBaseResponse?.data || [];

  // Pagination
  // const totalPages = knowledgeBaseResponse?.meta?.total;
  const totalPages = Math.ceil(
    knowledgeBaseResponse?.meta?.total / itemsPerPage
  );
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentResources = knowledgeBase.slice(startIndex, endIndex);

  const handleDownload = async (resourceId: string) => {
    try {
      toast({ title: "Download started", description: "Fetching file..." });

      const response = await apiService.getKnowledgeBaseDownloadLink(
        resourceId
      );
      const downloadUrl = response;
      if (!downloadUrl) {
        throw new Error("No download link found.");
      }

      const a = document.createElement("a");
      a.href = downloadUrl;
      a.download = "";
      a.target = "_blank";
      document.body.appendChild(a);
      a.click();
      a.remove();

      toast({
        title: "Download initiated",
        description: "Your download should begin shortly.",
      });
    } catch (error) {
      toast({
        title: "Download failed",
        description: "Unable to fetch or start the download.",
        variant: "destructive",
      });
      console.error("Download error:", error);
    }
  };

  const getTypeIcon = (type: KnowledgeBaseType) => {
    switch (type) {
      case KnowledgeBaseType.FILE:
        return <FileText className="w-4 h-4 text-blue-500" />;
      case KnowledgeBaseType.LINK:
        return <Link className="w-4 h-4 text-green-500" />;
      case KnowledgeBaseType.TEXT:
        return <Type className="w-4 h-4 text-purple-500" />;
      case KnowledgeBaseType.QA:
        return <MessageSquare className="w-4 h-4 text-orange-500" />;
      default:
        return <Upload className="w-4 h-4 text-muted-foreground" />;
    }
  };

  const getStatusIcon = (status: KnowledgeBaseStatus) => {
    switch (status) {
      case KnowledgeBaseStatus.FINISH:
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case KnowledgeBaseStatus.PROCESSING:
        return (
          <div className="w-4 h-4 border-2 border-yellow-500 border-t-transparent rounded-full animate-spin" />
        );
      case KnowledgeBaseStatus.PENDING:
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case KnowledgeBaseStatus.FAILED:
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-muted-foreground" />;
    }
  };

  const getStatusColor = (status: KnowledgeBaseStatus) => {
    switch (status) {
      case KnowledgeBaseStatus.FINISH:
        return "bg-green-500 text-white";
      case KnowledgeBaseStatus.PROCESSING:
        return "bg-yellow-500 text-white";
      case KnowledgeBaseStatus.PENDING:
        return "bg-blue-500 text-white";
      case KnowledgeBaseStatus.FAILED:
        return "bg-red-500 text-white";
      default:
        return "bg-muted text-muted-foreground";
    }
  };

  return (
    <div className="space-y-6">
      <Searchfield />

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[100px]">Type</TableHead>
              <TableHead>Name</TableHead>
              <TableHead className="w-[120px]">Status</TableHead>
              <TableHead className="w-[140px]">Upload Date</TableHead>
              <TableHead className="w-[120px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableSkeleton />
            ) : currentResources.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={5}
                  className="text-center py-8 text-muted-foreground"
                >
                  {search
                    ? "No resources found matching your search."
                    : "No recent resources found."}
                </TableCell>
              </TableRow>
            ) : (
              currentResources.map((resource) => (
                <TableRow key={resource.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getTypeIcon(resource.sourceType)}
                      <span className="text-xs uppercase tracking-wide text-muted-foreground">
                        {resource.sourceType}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="max-w-xs">
                      <div
                        className="font-medium truncate"
                        title={resource.name}
                      >
                        {resource.name}
                      </div>
                      {/* {resource.name && (
                        <div className="text-sm text-muted-foreground truncate">
                          {resource.name.length > 50
                            ? `${resource.name.substring(0, 50)}...`
                            : resource.name}
                        </div>
                      )} */}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(resource.status)}
                      <Badge className={getStatusColor(resource.status)}>
                        {resource.status}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell className="text-sm text-muted-foreground">
                    {new Date(resource.createdAt).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <Button
                      disabled={!resource.s3Id}
                      variant="ghost"
                      className="!disabled:cursor-not-allowed"
                      size="sm"
                      onClick={() => handleDownload(resource.id)}
                      title="Download"
                    >
                      <Download className="w-4 h-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing {startIndex + 1} to{" "}
            {Math.min(endIndex, knowledgeBase.length)} of {knowledgeBase.length}{" "}
            results
          </div>
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(1, prev - 1))
                  }
                  className={
                    currentPage === 1
                      ? "pointer-events-none opacity-50"
                      : "cursor-pointer"
                  }
                />
              </PaginationItem>

              {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                (page) => (
                  <PaginationItem key={page}>
                    <PaginationLink
                      onClick={() => setCurrentPage(page)}
                      isActive={page === currentPage}
                      className="cursor-pointer"
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                )
              )}

              <PaginationItem>
                <PaginationNext
                  onClick={() =>
                    setCurrentPage((prev) => Math.min(totalPages, prev + 1))
                  }
                  className={
                    currentPage === totalPages
                      ? "pointer-events-none opacity-50"
                      : "cursor-pointer"
                  }
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  );
};

export default RecentResourcesTable;
