import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Upload, FileText } from "lucide-react";
import { useMutation } from "@tanstack/react-query";
import { useRef, useState } from "react";
import { toast } from "@/components/ui/use-toast";
import { apiService } from "@/services/api";

const MAX_FILE_SIZE_MB = 10;
const SUPPORTED_TYPES = [
  "application/pdf",
  "text/plain",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
];

const DocumentUploadCard = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const addPDFMutation = useMutation<void, Error, File>({
    mutationFn: (file) => apiService.addPDFToKnowledgeBase(file),
    onMutate: () => {
      toast({
        title: "File upload started",
        description: "Processing your document...",
      });
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Your document has been added successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to upload the document.",
        variant: "destructive",
      });
    },
  });

  const handleFileUpload = () => {
    if (!selectedFile) return;
    if (!SUPPORTED_TYPES.includes(selectedFile.type)) {
      toast({ title: "Unsupported file type", variant: "destructive" });
      return;
    } 
    const sizeInMB = selectedFile.size / (1024 * 1024);
    if (sizeInMB > MAX_FILE_SIZE_MB) {
      toast({ title: "File too large", variant: "destructive" });
      return;
    }

    addPDFMutation.mutate(selectedFile);
    setSelectedFile(null);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const file = e.dataTransfer.files?.[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };
const isUploading = addPDFMutation.status === "pending";

  return (
    <Card className="glassmorphism border-white/10">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="w-5 h-5" />
          Document Upload
        </CardTitle>
        <CardDescription>
          Upload PDFs, Word docs, and text files to your knowledge base
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div
          className="border-2 border-dashed border-white/20 rounded-lg p-6 text-center cursor-pointer"
          onClick={() => fileInputRef.current?.click()}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
        >
          <Upload className="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
          <div className="text-sm text-muted-foreground mb-2">
            Drag and drop files here, or click to browse
          </div>

          <input
            type="file"
            accept=".pdf,.docx,.txt"
            ref={fileInputRef}
            className="hidden"
            onChange={(e) => {
              const file = e.target.files?.[0];
              if (file) setSelectedFile(file);
            }}
          />

          {selectedFile && (
            <div className="mt-3 text-sm text-muted-foreground">
              Selected file: <strong>{selectedFile.name}</strong>
            </div>
          )}

          <div className="text-xs text-muted-foreground mt-2">
            Supports PDF, DOCX, TXT files up to 10MB
          </div>

          <Button
            onClick={(e) => {
              e.stopPropagation();
              handleFileUpload();
            }}
            className="mt-4"
            disabled={!selectedFile || isUploading}
          >
            {isUploading ? "Uploading..." : "Upload"}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
export default DocumentUploadCard;
