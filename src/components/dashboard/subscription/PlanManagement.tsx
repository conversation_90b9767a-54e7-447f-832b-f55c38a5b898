
import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Check, ArrowUp } from "lucide-react";

const PlanManagement = () => {
  const plans = [
    {
      name: "Basic",
      price: "$29",
      features: ["10K API calls", "1K sessions", "1 GB storage"],
      current: false
    },
    {
      name: "Premium",
      price: "$99",
      features: ["50K API calls", "10K sessions", "5 GB storage"],
      current: true
    },
    {
      name: "Enterprise",
      price: "$299",
      features: ["Unlimited calls", "Unlimited sessions", "50 GB storage"],
      current: false
    }
  ];

  return (
    <Card className="card-modern">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ArrowUp className="w-5 h-5 text-primary" />
          Plan Options
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {plans.map((plan) => (
            <div key={plan.name} className={`p-4 rounded-lg border transition-colors ${
              plan.current ? 'border-primary bg-primary/5' : 'border-white/10 hover:border-white/20'
            }`}>
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <h3 className="font-semibold">{plan.name}</h3>
                  {plan.current && <Badge variant="secondary">Current</Badge>}
                </div>
                <div className="font-bold">{plan.price}/mo</div>
              </div>
              
              <div className="space-y-1 mb-3">
                {plan.features.map((feature) => (
                  <div key={feature} className="flex items-center gap-2 text-sm">
                    <Check className="w-3 h-3 text-green-400" />
                    {feature}
                  </div>
                ))}
              </div>
              
              {!plan.current && (
                <Button variant="outline" size="sm" className="w-full">
                  {plan.price === "$29" ? "Downgrade" : "Upgrade"}
                </Button>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default PlanManagement;
