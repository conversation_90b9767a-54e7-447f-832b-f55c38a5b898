import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useQuery } from "@tanstack/react-query";
import { apiService } from "@/services/api";
import { useAuth } from "@/hooks/useAuth";
import {
  Download,
  Receipt,
  CreditCard,
  Calendar,
  Zap,
  Eye,
} from "lucide-react";
import { Billing, Invoice } from "@/types/api";

const BillingHistory = () => {
  const { user } = useAuth();
  const [selectedBilling, setSelectedBilling] = useState<Billing | null>(null);
  const [isDetailOpen, setIsDetailOpen] = useState(false);

 
  const { data: billingHistory, isLoading: billingLoading } = useQuery({
    queryKey: ["billing-history"],
    queryFn: () => apiService.getBillingHistory(), 
  });
  console.log(billingHistory);

  const { data: invoices, isLoading: invoicesLoading } = useQuery({
    queryKey: ["user-invoices", user?.id],
    // queryFn: () => Promise.resolve(mockInvoices), // Use mock data
    queryFn: () => user ? apiService.getUserInvoices(user.id) : Promise.resolve([]),
    enabled: !!user?.id,
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case "PAID":
        return "bg-green-500 text-white";
      case "PENDING":
        return "bg-yellow-500 text-white";
      case "FAILED":
      case "CANCELLED":
        return "bg-red-500 text-white";
      case "REFUNDED":
        return "bg-blue-500 text-white";
      default:
        return "bg-gray-500 text-white";
    }
  };

  const getPaymentProviderIcon = (provider: string) => {
    switch (provider) {
      case "stripe":
        return <CreditCard className="w-4 h-4" />;
      case "zarinpal":
        return <CreditCard className="w-4 h-4" />;
      default:
        return <CreditCard className="w-4 h-4" />;
    }
  };

  const handleViewBilling = (billing: Billing) => {
    setSelectedBilling(billing);
    setIsDetailOpen(true);
  };

  const getBillingStatusColor = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return "bg-green-500 text-white";
      case "EXPIRED":
        return "bg-red-500 text-white";
      default:
        return "bg-gray-500 text-white";
    }
  };

  const getInvoicesForBilling = (billingId: string) => {
    return invoices?.filter((invoice) => invoice.creditId === billingId) || [];
  };

  if (billingLoading) {
    return (
      <Card className="card-modern">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Receipt className="w-5 h-5 text-primary" />
            Billing History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-16 bg-white/10 rounded-lg"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className="card-modern">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Receipt className="w-5 h-5 text-primary" />
            Billing History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {billingHistory?.data?.map((billing) => (
              <div
                key={billing.id}
                className="flex items-center justify-between p-4 rounded-lg hover:bg-white/5 transition-colors cursor-pointer border border-white/10"
                onClick={() => handleViewBilling(billing)}
              >
                <div className="flex items-center gap-3">
                  <Zap className="w-5 h-5 text-primary" />
                  <div>
                    <div className="font-medium">
                      Billing Period #{billing.id.slice(0, 8)}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {new Date(billing.startDate).toLocaleDateString()} -{" "}
                      {new Date(billing.endDate).toLocaleDateString()}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="text-right">
                    <div className="font-medium">
                      {billing.available.toLocaleString()} credits
                    </div>
                    <Badge
                      variant="secondary"
                      className={getBillingStatusColor(billing.status)}
                    >
                      {billing.status}
                    </Badge>
                  </div>
                  <Button variant="ghost" size="sm">
                    <Eye className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            ))}

            {(!billingHistory) && (
              <div className="text-center py-8 text-muted-foreground">
                No billing history available yet.
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Billing Detail Modal */}
      <Dialog open={isDetailOpen} onOpenChange={setIsDetailOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Receipt className="w-5 h-5 text-primary" />
              Billing Details - #{selectedBilling?.id.slice(0, 8)}
            </DialogTitle>
          </DialogHeader>

          {selectedBilling && (
            <div className="space-y-6">
              {/* Billing Overview */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-background/50 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Zap className="w-4 h-4 text-primary" />
                    <span className="text-sm font-medium">
                      Available Credits
                    </span>
                  </div>
                  <div className="text-2xl font-bold">
                    {selectedBilling.available.toLocaleString()}
                  </div>
                </div>

                <div className="bg-background/50 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Calendar className="w-4 h-4 text-green-500" />
                    <span className="text-sm font-medium">Start Date</span>
                  </div>
                  <div className="text-lg font-semibold">
                    {new Date(selectedBilling.startDate).toLocaleDateString()}
                  </div>
                </div>

                <div className="bg-background/50 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Calendar className="w-4 h-4 text-red-500" />
                    <span className="text-sm font-medium">End Date</span>
                  </div>
                  <div className="text-lg font-semibold">
                    {new Date(selectedBilling.endDate).toLocaleDateString()}
                  </div>
                </div>
              </div>

              {/* Status */}
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Status:</span>
                <Badge
                  variant="secondary"
                  className={getBillingStatusColor(selectedBilling.status)}
                >
                  {selectedBilling.status}
                </Badge>
              </div>

              {/* Associated Invoices */}
              <div>
                <h3 className="text-lg font-semibold mb-4">
                  Associated Invoices
                </h3>
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {getInvoicesForBilling(selectedBilling.id).map((invoice) => (
                    <div
                      key={invoice.id}
                      className="flex items-center justify-between p-3 border border-white/10 rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        {getPaymentProviderIcon(invoice.paymentProvider)}
                        <div>
                          <div className="font-medium">
                            Invoice #{invoice.id.slice(0, 8)}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {new Date(invoice.issuedAt).toLocaleDateString()}
                            {invoice.description && ` • ${invoice.description}`}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="text-right">
                          <div className="font-medium">
                            ${invoice.amount.toFixed(2)}
                          </div>
                          <Badge
                            variant="secondary"
                            className={getStatusColor(invoice.status)}
                          >
                            {invoice.status}
                          </Badge>
                        </div>
                        <Button variant="ghost" size="sm">
                          <Download className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  ))}

                  {getInvoicesForBilling(selectedBilling.id).length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      No invoices found for this billing period.
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default BillingHistory;
