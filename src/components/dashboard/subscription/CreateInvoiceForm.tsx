import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Receipt, CreditCard, Plus } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const CreateInvoiceForm = () => {
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [creditCount, setCreditCount] = useState("");
  const [isCreating, setIsCreating] = useState(false);

  const creditPrice = 0.10; // $0.10 per credit

  const calculateTotal = () => {
    const credits = parseInt(creditCount) || 0;
    return (credits * creditPrice).toFixed(2);
  };

  const handleCreateInvoice = async () => {
    if (!creditCount || parseInt(creditCount) <= 0) {
      toast({
        title: "Invalid amount",
        description: "Please enter a valid number of credits.",
        variant: "destructive",
      });
      return;
    }

    setIsCreating(true);
    
    try {
      // Mock invoice creation - would integrate with actual payment system
      await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate API call
      
      toast({
        title: "Invoice created",
        description: `Invoice for ${creditCount} credits ($${calculateTotal()}) has been created.`,
      });
      
      // Mock redirect to payment URL
      const paymentUrl = `https://checkout.example.com/pay?amount=${calculateTotal()}&description=Credits+Purchase`;
      window.open(paymentUrl, '_blank');
      
      setCreditCount("");
      setIsOpen(false);
    } catch (error) {
      toast({
        title: "Creation failed",
        description: "There was an error creating the invoice.",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button disabled className="w-full">
          <Plus className="w-4 h-4 mr-2" />
          Create Invoice
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Receipt className="w-5 h-5 text-primary" />
            Create Credit Invoice
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          <div className="bg-background/50 rounded-lg p-4">
            <div className="text-sm font-medium mb-2">Credit Pricing</div>
            <div className="text-xs text-muted-foreground">
              ${creditPrice.toFixed(2)} per credit • Credits will be added to your active billing period
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <Label htmlFor="creditCount">Number of Credits</Label>
              <Input
                id="creditCount"
                type="number"
                placeholder="Enter number of credits"
                value={creditCount}
                onChange={(e) => setCreditCount(e.target.value)}
                min="1"
              />
            </div>

            {creditCount && parseInt(creditCount) > 0 && (
              <div className="bg-primary/10 rounded-lg p-4 space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Credits:</span>
                  <span>{parseInt(creditCount).toLocaleString()}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Price per credit:</span>
                  <span>${creditPrice.toFixed(2)}</span>
                </div>
                <div className="border-t border-white/20 pt-2 flex justify-between font-medium">
                  <span>Total Amount:</span>
                  <span>${calculateTotal()}</span>
                </div>
              </div>
            )}

            <div className="flex gap-2">
              <Button
                onClick={handleCreateInvoice}
                disabled={!creditCount || parseInt(creditCount) <= 0 || isCreating}
                className="flex-1"
              >
                <CreditCard className="w-4 h-4 mr-2" />
                {isCreating ? 'Creating...' : 'Create & Pay Invoice'}
              </Button>
              <Button
                variant="outline"
                onClick={() => setIsOpen(false)}
                disabled={isCreating}
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CreateInvoiceForm;