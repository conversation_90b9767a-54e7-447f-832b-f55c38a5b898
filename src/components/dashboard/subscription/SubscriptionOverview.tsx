import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useQuery } from "@tanstack/react-query";
import { apiService } from "@/services/api";
import { Zap, CreditCard } from "lucide-react";
import CreateInvoiceForm from "./CreateInvoiceForm";
const SubscriptionOverview = () => {
  const {
    data: userCredit,
    isLoading
  } = useQuery({
    queryKey: ['user-credit'],
    queryFn: () => apiService.getUserCredit()
  });
  if (isLoading) {
    return <Card className="card-modern">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="w-5 h-5 text-primary" />
            Credit Balance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse">
            <div className="w-32 h-8 bg-white/10 rounded mb-2"></div>
            <div className="w-48 h-4 bg-white/10 rounded"></div>
          </div>
        </CardContent>
      </Card>;
  }
  return <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
      <Card className="card-modern md:col-span-2">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="w-5 h-5 text-primary" />
            Available Credit
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center gap-2">
                <h3 className="text-4xl font-bold">{userCredit?.toLocaleString() || '0'}</h3>
                <Badge variant="secondary" className="bg-green-500 text-white">Active</Badge>
              </div>
              <p className="text-muted-foreground">Credits available in current billing</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="card-modern">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="w-5 h-5 text-primary" />
            Purchase Credits
          </CardTitle>
        </CardHeader>
        <CardContent>
          <CreateInvoiceForm />
        </CardContent>
      </Card>

      
    </div>;
};
export default SubscriptionOverview;