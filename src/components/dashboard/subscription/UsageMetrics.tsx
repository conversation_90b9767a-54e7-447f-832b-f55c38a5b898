import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Zap, Database, Users, MessageSquare } from "lucide-react";
const UsageMetrics = () => {
  const metrics = [{
    icon: Zap,
    label: "API Calls",
    used: 12543,
    limit: 50000,
    unit: "calls"
  }, {
    icon: Users,
    label: "Support Sessions",
    used: 2847,
    limit: 10000,
    unit: "sessions"
  }, {
    icon: Database,
    label: "Data Storage",
    used: 1.2,
    limit: 5,
    unit: "GB"
  }, {
    icon: MessageSquare,
    label: "Channels",
    used: 3,
    limit: 10,
    unit: "active"
  }];
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {metrics.map((metric, index) => {
        const Icon = metric.icon;
        const percentage = (metric.used / metric.limit) * 100;
        return (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{metric.label}</CardTitle>
              <Icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metric.used.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                of {metric.limit.toLocaleString()} {metric.unit}
              </p>
              <Progress value={percentage} className="mt-2" />
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};
export default UsageMetrics;