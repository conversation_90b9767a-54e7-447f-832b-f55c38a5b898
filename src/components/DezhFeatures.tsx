
import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Users, Settings } from "lucide-react";

const DezhFeatures = () => {
  const features = [
    {
      icon: Bo<PERSON>,
      title: "Smart Support Agent",
      description: "Acts like a human employee, understanding context and providing natural, helpful responses to customer inquiries.",
      accent: "accent-blue",
    },
    {
      icon: Zap,
      title: "Automatic Function Calling",
      description: "Seamlessly integrates with your backend systems to fetch data, update records, and resolve issues without manual intervention.",
      accent: "primary",
    },
    {
      icon: Users,
      title: "Human-in-the-Loop",
      description: "Intelligently escalates complex issues to human agents when needed, ensuring customers always get the help they need.",
      accent: "accent-blue",
    },
    {
      icon: Settings,
      title: "System-Change Resilient",
      description: "Adapts automatically when your systems update or change, maintaining support quality without manual reconfiguration.",
      accent: "primary",
    },
  ];

  const getAccentClasses = (accent: string) => {
    switch (accent) {
      case "accent-blue": return "bg-accent-blue/10 border-accent-blue/20 text-accent-blue group-hover:bg-accent-blue/20";
      default: return "bg-primary/10 border-primary/20 text-primary group-hover:bg-primary/20";
    }
  };

  return (
    <section id="features" className="py-24 bg-background relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div>
      
      {/* Simplified grid overlay */}
      <div className="absolute inset-0 sci-fi-grid opacity-30"></div>
      
      <div className="section-container relative z-10">
        <div className="text-center mb-16">
          <h2 className="section-title opacity-0 animate-on-scroll">
            Powerful Features for 
            <span className="bg-gradient-to-r from-primary to-accent-blue bg-clip-text text-transparent"> Modern Support</span>
          </h2>
          <p className="section-subtitle mx-auto opacity-0 animate-on-scroll stagger-1">
            Everything you need to deliver exceptional customer support, automatically.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <div 
                key={index}
                className={`card-modern group opacity-0 animate-on-scroll stagger-${index + 2} relative overflow-hidden`}
              >
                {/* Simplified corner accent */}
                <div className={`absolute top-0 right-0 w-12 h-12 ${feature.accent === 'primary' ? 'bg-primary/10' : 'bg-accent-blue/10'} clip-polygon opacity-50`}></div>
                
                <div className="mb-6 relative z-10">
                  <div className={`w-12 h-12 ${getAccentClasses(feature.accent)} border rounded-lg flex items-center justify-center transition-all duration-300 relative overflow-hidden`}>
                    <Icon className="w-6 h-6 relative z-10" />
                    <div className="absolute inset-0 bg-gradient-to-br from-transparent to-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>
                </div>
                <h3 className="text-lg font-semibold text-foreground mb-3">{feature.title}</h3>
                <p className="text-muted-foreground leading-relaxed text-sm">{feature.description}</p>
                
                {/* Simplified hover glow effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default DezhFeatures;
