
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>ch, CheckCircle } from "lucide-react";

const DezhHowItWorks = () => {
  const steps = [
    {
      icon: MessageCircle,
      title: "Customer Inquiry",
      description: "A customer reaches out with a question or issue through any channel.",
    },
    {
      icon: <PERSON>,
      title: "AI Analysis",
      description: "Dezh AI understands the context and determines the best approach to help.",
    },
    {
      icon: Wrench,
      title: "Automatic Resolution",
      description: "Calls necessary backend functions, fetches data, and performs actions to resolve the issue.",
    },
    {
      icon: CheckCircle,
      title: "Instant Response",
      description: "Provides a complete, helpful response or escalates to a human when needed.",
    },
  ];

  return (
    <section id="how-it-works" className="py-24 bg-gradient-to-br from-dezh-light to-white relative overflow-hidden">
      <div className="section-container">
        <div className="text-center mb-16">
          <h2 className="section-title opacity-0 animate-on-scroll">
            How <span className="text-dezh-accent">R<PERSON><PERSON></span> Works
          </h2>
          <p className="section-subtitle mx-auto opacity-0 animate-on-scroll stagger-1">
            Simple, powerful automation that transforms your support workflow.
          </p>
        </div>

        <div className="relative">
          {/* Connection lines for desktop */}
          <div className="hidden lg:block absolute top-24 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-dezh-accent/50 to-transparent"></div>
          
          <div className="grid lg:grid-cols-4 gap-8 lg:gap-4">
            {steps.map((step, index) => {
              const Icon = step.icon;
              return (
                <div 
                  key={index}
                  className={`text-center relative opacity-0 animate-on-scroll stagger-${index + 2}`}
                >
                  {/* Step number */}
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-dezh-accent text-white rounded-full font-bold text-lg mb-6 relative z-10">
                    {index + 1}
                  </div>
                  
                  {/* Icon */}
                  <div className="mb-6">
                    <div className="w-20 h-20 bg-white shadow-lg rounded-2xl flex items-center justify-center mx-auto border border-dezh-light">
                      <Icon className="w-10 h-10 text-dezh-accent" />
                    </div>
                  </div>
                  
                  {/* Content */}
                  <h3 className="text-xl font-bold text-dezh-dark mb-4">{step.title}</h3>
                  <p className="text-dezh-dark/70 leading-relaxed">{step.description}</p>
                </div>
              );
            })}
          </div>
        </div>

        {/* CTA */}
        <div className="text-center mt-16 opacity-0 animate-on-scroll stagger-6">
          <button 
            onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
            className="btn-primary"
          >
            See It in Action
          </button>
        </div>
      </div>
    </section>
  );
};

export default DezhHowItWorks;
