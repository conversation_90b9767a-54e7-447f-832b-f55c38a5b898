import React from "react";

const DezhTrustedCompanies = () => {
  const companies = [
    { name: "Tech<PERSON>low", logo: "TF" },
    { name: "CloudBridge", logo: "CB" },
    { name: "InnovateLabs", logo: "IL" },
    { name: "DataSync", logo: "<PERSON>" },
    { name: "Next<PERSON>en", logo: "NG" },
    { name: "FlowO<PERSON>", logo: "FO" },
    { name: "ScaleUp", logo: "SU" },
    { name: "DevCor<PERSON>", logo: "DC" },
    { name: "AITech", logo: "AT" },
    { name: "CloudOps", logo: "CO" },
    { name: "NeuralNet", logo: "NN" },
    { name: "Byte<PERSON><PERSON>", logo: "BF" }
  ];

  // Duplicate for seamless loop
  const duplicatedCompanies = [...companies, ...companies];

  return (
    <section className="py-16 bg-background border-b border-border/50 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <p className="text-sm text-muted-foreground/70 uppercase tracking-wider font-medium mb-2">
            Trusted by innovative teams worldwide
          </p>
          <div className="flex items-center justify-center gap-2 text-xs text-muted-foreground/60">
            <div className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div>
            <span>Join 500+ companies automating support</span>
          </div>
        </div>
        
        {/* Sliding animation container */}
        <div className="relative hidden md:block">
          <div className="flex animate-slide-infinite">
            {duplicatedCompanies.map((company, index) => (
              <div 
                key={`${company.name}-${index}`} 
                className="flex-shrink-0 mx-8 group"
              >
                <div className="flex flex-col items-center space-y-3 opacity-60 hover:opacity-90 transition-all duration-300 group-hover:scale-105">
                  {/* Company logo */}
                  <div className="w-16 h-16 bg-gradient-to-br from-primary/20 to-accent/20 rounded-xl flex items-center justify-center border border-border/30 backdrop-blur-sm group-hover:border-primary/30 transition-all duration-300">
                    <span className="text-lg font-bold text-foreground/80 group-hover:text-primary transition-colors">
                      {company.logo}
                    </span>
                  </div>
                  
                  {/* Company name */}
                  <span className="text-xs font-medium text-muted-foreground/60 group-hover:text-foreground/80 transition-colors">
                    {company.name}
                  </span>
                </div>
              </div>
            ))}
          </div>
          
          {/* Gradient overlays for fade effect */}
          <div className="absolute left-0 top-0 w-32 h-full bg-gradient-to-r from-background via-background/80 to-transparent pointer-events-none z-10"></div>
          <div className="absolute right-0 top-0 w-32 h-full bg-gradient-to-l from-background via-background/80 to-transparent pointer-events-none z-10"></div>
        </div>
        
        {/* Alternative static grid for smaller screens */}
        <div className="md:hidden grid grid-cols-3 gap-6 mt-8">
          {companies.slice(0, 6).map((company, index) => (
            <div key={company.name} className="text-center opacity-60">
              <div className="w-12 h-12 bg-gradient-to-br from-primary/20 to-accent/20 rounded-lg flex items-center justify-center border border-border/30 mx-auto mb-2">
                <span className="text-sm font-bold text-foreground/80">
                  {company.logo}
                </span>
              </div>
              <span className="text-xs font-medium text-muted-foreground/60">
                {company.name}
              </span>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default DezhTrustedCompanies;