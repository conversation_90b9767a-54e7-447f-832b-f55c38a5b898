
import React, { useState } from "react";
import { Mail, Phone, MapPin, Send, CheckCircle, Sparkles, Zap, ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

const DezhContact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    message: ''
  });

  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted:', formData);
    setIsSubmitted(true);
    // Reset form after a delay
    setTimeout(() => {
      setFormData({
        name: '',
        email: '',
        company: '',
        message: ''
      });
      setIsSubmitted(false);
    }, 3000);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  const features = [
    {
      icon: CheckCircle,
      title: "Free 30-Day Trial",
      description: "Try Dezh AI risk-free with full access to all features."
    },
    {
      icon: Zap,
      title: "Quick Setup",
      description: "Get up and running in under 15 minutes with our guided setup."
    },
    {
      icon: Sparkles,
      title: "Expert Support",
      description: "Our team helps you optimize Dezh AI for your specific needs."
    }
  ];

  return (
    <section className="py-24 bg-gradient-to-br from-background via-background to-muted/30 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-primary/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/4 left-1/4 w-80 h-80 bg-accent/3 rounded-full blur-3xl"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header */}
        <div className="text-center mb-20">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-6">
            Get Started with <span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">Dezh AI</span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Ready to automate your support? Let's talk about how Dezh AI can transform your business.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-16 items-start">
          {/* Left side - Form */}
          <div className="relative">
            {/* Success overlay */}
            {isSubmitted && (
              <div className="absolute inset-0 bg-card/95 backdrop-blur-sm rounded-2xl flex items-center justify-center z-10 transition-all duration-500">
                <div className="text-center">
                  <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckCircle className="w-8 h-8 text-green-500" />
                  </div>
                  <h3 className="text-xl font-semibold text-foreground mb-2">Thank you!</h3>
                  <p className="text-muted-foreground">We'll get back to you soon.</p>
                </div>
              </div>
            )}
            
            <div className="bg-card/50 backdrop-blur-sm border border-border rounded-2xl p-8 hover:bg-card/70 transition-all duration-300">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                  <Send className="w-5 h-5 text-primary" />
                </div>
                <h3 className="text-xl font-semibold text-foreground">Start Your Free Trial</h3>
              </div>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name" className="text-foreground font-medium text-sm">Full Name</Label>
                    <Input 
                      id="name" 
                      name="name" 
                      type="text" 
                      value={formData.name} 
                      onChange={handleChange} 
                      required 
                      className="mt-2 bg-background/50 border-border focus:border-primary/50 transition-colors" 
                      placeholder="John Doe"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="email" className="text-foreground font-medium text-sm">Email Address</Label>
                    <Input 
                      id="email" 
                      name="email" 
                      type="email" 
                      value={formData.email} 
                      onChange={handleChange} 
                      required 
                      className="mt-2 bg-background/50 border-border focus:border-primary/50 transition-colors" 
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="company" className="text-foreground font-medium text-sm">Company Name</Label>
                  <Input 
                    id="company" 
                    name="company" 
                    type="text" 
                    value={formData.company} 
                    onChange={handleChange} 
                    className="mt-2 bg-background/50 border-border focus:border-primary/50 transition-colors" 
                    placeholder="Your Company Inc."
                  />
                </div>
                
                <div>
                  <Label htmlFor="message" className="text-foreground font-medium text-sm">Tell us about your support needs</Label>
                  <textarea 
                    id="message" 
                    name="message" 
                    value={formData.message} 
                    onChange={handleChange} 
                    rows={4} 
                    className="mt-2 w-full px-3 py-2 bg-background/50 border border-border rounded-md focus:outline-none focus:border-primary/50 transition-colors text-foreground placeholder:text-muted-foreground resize-none" 
                    placeholder="Describe your current support challenges and what you'd like to achieve..." 
                  />
                </div>
                
                <button 
                  type="submit" 
                  className="w-full bg-primary text-primary-foreground px-6 py-3 rounded-lg font-semibold hover:bg-primary/90 transition-all duration-200 flex items-center justify-center group"
                >
                  <Send className="w-4 h-4 mr-2" />
                  <span>Get Started Free</span>
                  <ArrowRight className="w-4 h-4 ml-2 transition-transform group-hover:translate-x-1" />
                </button>
              </form>
            </div>
          </div>

          {/* Right side - Benefits */}
          <div className="space-y-8">
            <div>
              <h3 className="text-2xl font-bold text-foreground mb-6">Why Choose Dezh AI?</h3>
              <div className="space-y-6">
                {features.map((feature, index) => (
                  <div key={index} className="flex items-start gap-4 group">
                    <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0 group-hover:bg-primary/20 transition-colors">
                      <feature.icon className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-foreground mb-2">{feature.title}</h4>
                      <p className="text-muted-foreground leading-relaxed">{feature.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Contact info card */}
            <div className="bg-card/50 border border-border rounded-xl p-6 backdrop-blur-sm">
              <h4 className="font-semibold text-foreground mb-4 flex items-center gap-2">
                <Phone className="w-4 h-4 text-primary" />
                Need Help Getting Started?
              </h4>
              <div className="space-y-3 text-sm">
                <div className="flex items-center gap-3 text-muted-foreground hover:text-foreground transition-colors">
                  <Mail className="w-4 h-4 text-primary" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center gap-3 text-muted-foreground hover:text-foreground transition-colors">
                  <Phone className="w-4 h-4 text-primary" />
                  <span>+1 (555) 123-DEZH</span>
                </div>
                <div className="flex items-center gap-3 text-muted-foreground hover:text-foreground transition-colors">
                  <MapPin className="w-4 h-4 text-primary" />
                  <span>Available worldwide</span>
                </div>
              </div>
            </div>

            {/* Quick stats */}
            <div className="bg-gradient-to-r from-primary/10 to-accent/10 rounded-xl p-6 border border-primary/20">
              <div className="grid grid-cols-2 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-primary mb-1">15 min</div>
                  <div className="text-xs text-muted-foreground">Setup time</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-primary mb-1">30 days</div>
                  <div className="text-xs text-muted-foreground">Free trial</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DezhContact;
