import React from "react";
import { CheckCircle, Upload, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>R<PERSON> } from "lucide-react";
import { useNavigate } from "react-router-dom";

const DezhModernProcess = () => {
  const navigate = useNavigate();
  const steps = [
    {
      step: "1",
      title: "Load your data",
      description: "Upload docs, or add Q&As and give your agent a fancy name. Rokovo your business in minutes.",
      features: [ "URLs and Webpages", "PDFs and Markdowns", "DOCX and PPTX", "Custom text and Q&As"],
      icon: Upload
    },
    {
      step: "2", 
      title: "Configure tools",
      description: "Define the actions your agent can take with HTTP APIs. Connect  and onboard it to your existing systems and workflows once forever!",
      features: ["HTTP API calls", "Database queries", "Webhook triggers", "Custom logic"],
      icon: Settings
    },
    {
      step: "3",
      title: "Deploy everywhere",
      description: "Launch across all channels with one click. Your agent is ready to help users instantly.",
      features: ["Website widget", "Telegram bots", "Discord", "API endpoints"],
      icon: Rocket
    }
  ];

  return (
    <section id="how-it-works" className="py-24 bg-muted/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-20">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-6">
            Start in 
            <span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent"> 3 simple steps</span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            From setup to serving customers in under an hour
          </p>
        </div>

        {/* Steps */}
        <div className="grid lg:grid-cols-3 gap-8 mb-20">
          {steps.map((step, index) => {
            const Icon = step.icon;
            return (
              <div key={index} className="relative">
                {/* Connection line */}
                {index < steps.length - 1 && (
                  <div className="hidden lg:block absolute top-16 left-full w-8 h-0.5 bg-border z-0 transform translate-x-4">
                    <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-primary rounded-full"></div>
                  </div>
                )}
                
                <div className="bg-card border border-border rounded-xl p-8 relative z-10 h-full">
                  {/* Step indicator */}
                  <div className="flex items-center gap-4 mb-6">
                    <div className="w-12 h-12 bg-primary text-primary-foreground rounded-lg flex items-center justify-center font-bold">
                      {step.step}
                    </div>
                    <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                      <Icon className="w-5 h-5 text-primary" />
                    </div>
                  </div>
                  
                  <h3 className="text-xl font-semibold text-foreground mb-3">{step.title}</h3>
                  <p className="text-muted-foreground mb-6 leading-relaxed">{step.description}</p>
                  
                  {/* Features list */}
                  <ul className="space-y-2">
                    {step.features.map((feature, fIndex) => (
                      <li key={fIndex} className="flex items-center text-sm text-muted-foreground">
                        <CheckCircle className="w-4 h-4 text-primary mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            );
          })}
        </div>

        {/* Result showcase */}
        <div className="bg-card border border-border rounded-xl p-8 md:p-12 text-center">
          <div className="mb-8">
            <h3 className="text-2xl font-bold text-foreground mb-4">The result!</h3>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Your AI handles complex support requests autonomously while seamlessly escalating when human expertise is needed.
            </p>
          </div>
          
          {/* Demo conversation */}
          <div className="bg-muted/50 rounded-lg p-6 max-w-2xl mx-auto mb-8">
            <div className="space-y-4 text-left">
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center text-xs">👤</div>
                <div className="bg-background rounded-lg px-3 py-2 max-w-xs">
                  <div className="text-sm">Can you cancel my subscription?</div>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                  <Bot className="w-4 h-4 text-primary-foreground" />
                </div>
                <div className="bg-primary/10 border border-primary/20 rounded-lg px-3 py-2 max-w-sm">
                  <div className="text-sm">I've cancelled your subscription and processed a prorated refund of $23.50. You'll receive an email confirmation shortly.</div>
                  <div className="text-xs text-muted-foreground mt-2 font-mono">→ Called cancelSubscription()</div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Enhanced Stats with ROI focus */}
          <div className="grid grid-cols-3 gap-8 text-center mb-8">
            <div className="group hover:scale-105 transition-transform duration-200">
              <div className="text-3xl font-bold text-primary mb-2 group-hover:text-accent transition-colors">80%</div>
              <div className="text-sm text-muted-foreground">Auto-Resolution Rate</div>
              <div className="text-xs text-muted-foreground/60 mt-1">vs 20% industry avg</div>
            </div>
            <div className="group hover:scale-105 transition-transform duration-200">
              <div className="text-3xl font-bold text-primary mb-2 group-hover:text-accent transition-colors">24/7</div>
              <div className="text-sm text-muted-foreground">Always Available</div>
              <div className="text-xs text-muted-foreground/60 mt-1">No downtime ever</div>
            </div>
            <div className="group hover:scale-105 transition-transform duration-200">
              <div className="text-3xl font-bold text-primary mb-2 group-hover:text-accent transition-colors">70%</div>
              <div className="text-sm text-muted-foreground">Cost Reduction</div>
              <div className="text-xs text-muted-foreground/60 mt-1">vs human agents</div>
            </div>
          </div>
          
          {/* Call to Action */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-6">
            <button
              onClick={() => navigate("/dashboard")}
              className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-4 rounded-xl font-semibold transition-all duration-200 inline-flex items-center group hover:scale-105 hover:shadow-xl"
            >
              <span>Get Started Now</span>
              <ArrowRight className="ml-2 w-4 h-4 transition-transform group-hover:translate-x-1" />
            </button>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <span>Free plan available</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DezhModernProcess;