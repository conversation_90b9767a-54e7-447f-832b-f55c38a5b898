
import React, { useState, useEffect, useRef } from "react";
import { TrendingUp, Users, Clock, Sparkles, MessageCircle, X } from "lucide-react";
import { useNavigate } from "react-router-dom";

const DezhBenefits = () => {
  const navigate = useNavigate();
  const sectionRef = useRef<HTMLElement>(null);
  const [showBlurOverlay, setShowBlurOverlay] = useState(false);
  const [hasShownOverlay, setHasShownOverlay] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        // Only show overlay if it hasn't been shown yet and section is visible
        if (entry.isIntersecting && !hasShownOverlay) {
          setTimeout(() => {
            setShowBlurOverlay(true);
            setHasShownOverlay(true); // Mark as shown
          }, 1000);
        }
      },
      { threshold: 0.5 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, [hasShownOverlay]);

  const closeOverlay = () => {
    setShowBlurOverlay(false);
  };

  const benefits = [
    {
      icon: Clock,
      title: "24/7 Instant Support",
      description: "Never miss a customer inquiry. Dezh AI provides round-the-clock support without breaks or downtime.",
      metric: "99.9% Uptime",
    },
    {
      icon: TrendingUp,
      title: "Reduce Support Costs",
      description: "Cut support costs by up to 70% while improving response times and customer satisfaction.",
      metric: "70% Cost Reduction",
    },
    {
      icon: Users,
      title: "Scale Effortlessly", 
      description: "Handle thousands of inquiries simultaneously without hiring additional support staff.",
      metric: "Unlimited Scale",
    },
    {
      icon: Sparkles,
      title: "Future-Proof Automation",
      description: "System changes don't break your support. Dezh AI adapts automatically to keep serving customers.",
      metric: "Zero Downtime",
    },
  ];

  return (
    <>
      <section 
        ref={sectionRef}
        className="py-24 bg-card/30 relative overflow-hidden"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-6">
              Why teams choose 
              <span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent"> Rokovo</span>
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Automated support that works even when your systems change — no manual intervention required.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 mb-16">
            {benefits.map((benefit, index) => {
              const Icon = benefit.icon;
              return (
                <div 
                  key={index}
                  className="bg-card border border-border rounded-xl p-8 hover:bg-card/80 transition-all duration-300"
                >
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Icon className="w-6 h-6 text-primary" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-xl font-semibold text-foreground">{benefit.title}</h3>
                        <span className="text-xs font-medium text-primary bg-primary/10 px-2 py-1 rounded">
                          {benefit.metric}
                        </span>
                      </div>
                      <p className="text-muted-foreground leading-relaxed">{benefit.description}</p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Demo CTA section */}
          <div className="text-center bg-muted/50 rounded-2xl p-12 border border-border">
            <h3 className="text-2xl font-bold text-foreground mb-4">
              Experience our AI in action
            </h3>
            <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
              Don't just read about it - try it yourself! Our AI agent is ready to answer your questions.
            </p>
            
            <button 
              onClick={() => navigate("/book-demo")}
              className="bg-primary text-primary-foreground px-8 py-4 rounded-lg font-semibold hover:bg-primary/90 transition-all duration-200"
            >
              Start Your Free Trial
            </button>
          </div>
        </div>
      </section>

      {/* Blur Overlay */}
      {showBlurOverlay && (
        <div className="fixed inset-0 z-40 transition-all duration-500">
          {/* Backdrop blur - everything except chat icon */}
          <div className="absolute inset-0 backdrop-blur-sm bg-background/80"></div>
          
          {/* Popup message */}
          <div className="absolute bottom-32 right-8 md:right-16 lg:right-24">
            <div className="bg-primary text-primary-foreground px-6 py-4 rounded-lg shadow-2xl relative max-w-xs animate-fade-in">
              <button 
                onClick={closeOverlay}
                className="absolute -top-2 -right-2 w-6 h-6 bg-card border border-border rounded-full flex items-center justify-center hover:bg-muted transition-colors"
              >
                <X className="w-3 h-3 text-foreground" />
              </button>
              
              <div className="flex items-center gap-3 mb-3">
                <MessageCircle className="w-5 h-5" />
                <span className="font-semibold">Try our AI agent!</span>
              </div>
              
              <p className="text-sm leading-relaxed mb-3">
                You can test our agent here! Ask a question about Rokovo and see the results.
              </p>
              
              <div className="text-xs opacity-90">
                Click the chat icon to start →
              </div>
              
              {/* Arrow pointing to chat icon */}
              <div className="absolute -bottom-2 right-4 w-4 h-4 bg-primary rotate-45"></div>
            </div>
          </div>
          
          {/* Click overlay to close */}
          <div 
            className="absolute inset-0 cursor-pointer"
            onClick={closeOverlay}
          ></div>
        </div>
      )}
    </>
  );
};

export default DezhBenefits;
