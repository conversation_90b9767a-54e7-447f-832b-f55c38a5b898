import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ThumbsUp, ThumbsDown } from 'lucide-react';
import { cn } from '@/lib/utils';

interface MessageFeedbackProps {
  messageId: string;
  currentFeedback?: 'positive' | 'negative' | null;
  onFeedback: (messageId: string, feedback: 'positive' | 'negative') => void;
  className?: string;
}

const MessageFeedback: React.FC<MessageFeedbackProps> = ({
  messageId,
  currentFeedback,
  onFeedback,
  className
}) => {
  const [hoveredFeedback, setHoveredFeedback] = useState<'positive' | 'negative' | null>(null);

  const handleFeedback = (feedback: 'positive' | 'negative') => {
    onFeedback(messageId, feedback);
  };

  const handleKeyDown = (e: React.KeyboardEvent, feedback: 'positive' | 'negative') => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleFeedback(feedback);
    }
  };

  return (
    <div className={cn("flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity", className)}>
      {/* Thumbs Up */}
      <Button
        variant="ghost"
        size="sm"
        className={cn(
          "h-6 w-6 p-0 hover:bg-green-100 hover:text-green-600 transition-colors",
          currentFeedback === 'positive' && "bg-green-100 text-green-600"
        )}
        onClick={() => handleFeedback('positive')}
        onKeyDown={(e) => handleKeyDown(e, 'positive')}
        onMouseEnter={() => setHoveredFeedback('positive')}
        onMouseLeave={() => setHoveredFeedback(null)}
        aria-label="Mark as helpful"
        title="Mark as helpful"
      >
        <ThumbsUp 
          className={cn(
            "w-3 h-3 transition-transform",
            (currentFeedback === 'positive' || hoveredFeedback === 'positive') && "scale-110"
          )} 
        />
      </Button>

      {/* Thumbs Down */}
      <Button
        variant="ghost"
        size="sm"
        className={cn(
          "h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600 transition-colors",
          currentFeedback === 'negative' && "bg-red-100 text-red-600"
        )}
        onClick={() => handleFeedback('negative')}
        onKeyDown={(e) => handleKeyDown(e, 'negative')}
        onMouseEnter={() => setHoveredFeedback('negative')}
        onMouseLeave={() => setHoveredFeedback(null)}
        aria-label="Mark as not helpful"
        title="Mark as not helpful"
      >
        <ThumbsDown 
          className={cn(
            "w-3 h-3 transition-transform",
            (currentFeedback === 'negative' || hoveredFeedback === 'negative') && "scale-110"
          )} 
        />
      </Button>
    </div>
  );
};

export default MessageFeedback;
