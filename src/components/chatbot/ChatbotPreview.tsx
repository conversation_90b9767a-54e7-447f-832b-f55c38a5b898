import React, { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Send, Bot, User, Mic, MicOff, Phone, PhoneOff, Minimize2 } from "lucide-react";
import { cn } from "@/lib/utils";
import MessageFeedback from "./MessageFeedback";

interface ChatbotPreviewProps {
  config: {
    businessName: string;
    agentName: string;
    primaryColor: string;
    secondaryColor: string;
    chatBubbleStyle: 'rounded' | 'square' | 'pill';
    avatarUrl?: string;
  };
}

interface PreviewMessage {
  id: string;
  content: string;
  isBot: boolean;
  timestamp: Date;
  feedback?: 'positive' | 'negative' | null;
}

const ChatbotPreview: React.FC<ChatbotPreviewProps> = ({ config }) => {
  const [messages, setMessages] = useState<PreviewMessage[]>([
    {
      id: '1',
      content: `Hello! I'm ${config.agentName} from ${config.businessName}. How can I help you today?`,
      isBot: true,
      timestamp: new Date()
    }
  ]);
  
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isVoiceMode, setIsVoiceMode] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Update welcome message when config changes
  useEffect(() => {
    setMessages(prev => prev.map((msg, index) => 
      index === 0 ? {
        ...msg,
        content: `Hello! I'm ${config.agentName} from ${config.businessName}. How can I help you today?`
      } : msg
    ));
  }, [config.agentName, config.businessName]);

  const handleSendMessage = () => {
    if (!inputMessage.trim()) return;

    const userMessage: PreviewMessage = {
      id: Date.now().toString(),
      content: inputMessage,
      isBot: false,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsTyping(true);

    // Simulate AI response
    setTimeout(() => {
      const responses = [
        "Thank you for your message! This is a preview of how your chatbot will respond.",
        "I'm here to help! In the live version, I'll be powered by your AI agent.",
        "Great question! Your customers will receive intelligent responses like this.",
        "This preview shows how smooth the conversation experience will be.",
      ];
      
      const botResponse: PreviewMessage = {
        id: (Date.now() + 1).toString(),
        content: responses[Math.floor(Math.random() * responses.length)],
        isBot: true,
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, botResponse]);
      setIsTyping(false);
    }, 1000 + Math.random() * 1000);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const getBubbleStyle = (style: string) => {
    switch (style) {
      case 'square':
        return 'rounded-none';
      case 'pill':
        return 'rounded-full';
      default:
        return 'rounded-lg';
    }
  };

  const toggleVoiceMode = () => {
    setIsVoiceMode(!isVoiceMode);
  };

  // Handle message feedback for preview
  const handleMessageFeedback = (messageId: string, feedback: 'positive' | 'negative') => {
    setMessages(prev => prev.map(msg =>
      msg.id === messageId
        ? { ...msg, feedback: msg.feedback === feedback ? null : feedback }
        : msg
    ));
  };

  if (isMinimized) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsMinimized(false)}
          className="rounded-full w-12 h-12 sm:w-14 sm:h-14 shadow-lg hover:scale-105 transition-transform p-2 sm:p-3"
          style={{ backgroundColor: config.primaryColor }}
          aria-label="Open chat"
        >
          <img
            src="/logo.svg"
            alt="Rokovo logo"
            className="w-full h-full object-contain"
          />
        </Button>
      </div>
    );
  }

  return (
    <div className="w-full max-w-md mx-auto bg-card/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-border overflow-hidden">
      {/* Header */}
      <div className="bg-primary/10 border-b border-border p-6 flex items-center gap-4">
        <div className="w-12 h-12 rounded-xl flex items-center justify-center overflow-hidden" style={{ backgroundColor: config.primaryColor }}>
          {config.avatarUrl ? (
            <img src={config.avatarUrl} alt="Agent avatar" className="w-full h-full object-cover" />
          ) : (
            <Bot className="w-6 h-6 text-white" />
          )}
        </div>
        <div className="flex-1">
          <h3 className="font-semibold text-foreground text-lg">{config.agentName}</h3>
          <div className="flex items-center gap-2 mt-1">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <p className="text-sm text-muted-foreground">Online • {config.businessName}</p>
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsMinimized(true)}
          className="w-8 h-8 rounded-lg hover:bg-muted/50 flex items-center justify-center transition-colors"
        >
          <Minimize2 className="w-4 h-4 text-muted-foreground" />
        </Button>
      </div>

      {/* Messages */}
      <div className="h-80 overflow-y-auto p-6 space-y-4 scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent">
        {messages.map((message) => (
          <div
            key={message.id}
            className={cn(
              "flex gap-3 items-start group",
              message.isBot ? "justify-start" : "justify-end"
            )}
          >
            {message.isBot && (
              <div className="w-8 h-8 bg-primary/20 rounded-lg flex items-center justify-center flex-shrink-0 overflow-hidden">
                {config.avatarUrl ? (
                  <img src={config.avatarUrl} alt="Agent avatar" className="w-full h-full object-cover" />
                ) : (
                  <Bot className="w-4 h-4 text-primary" />
                )}
              </div>
            )}
            <div className="flex flex-col max-w-[80%]">
              <div className="relative">
                <div
                  className={cn(
                    "p-4 rounded-xl text-sm leading-relaxed",
                    message.isBot
                      ? "bg-muted/50 text-foreground border border-border rounded-bl-md"
                      : "text-primary-foreground rounded-br-md"
                  )}
                  style={!message.isBot ? { backgroundColor: config.primaryColor } : {}}
                >
                  {message.content}
                </div>
                {message.isBot && (
                  <div className="absolute bottom-2 right-2">
                    <MessageFeedback
                      messageId={message.id}
                      currentFeedback={message.feedback}
                      onFeedback={handleMessageFeedback}
                    />
                  </div>
                )}
              </div>
              <div className={cn(
                "text-xs text-muted-foreground mt-1 px-1 flex items-center justify-between",
                message.isBot ? "flex-row" : "flex-row-reverse"
              )}>
                <span>
                  {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </span>
                {message.isBot && message.feedback && (
                  <span className={cn(
                    "text-xs px-1.5 py-0.5 rounded-full",
                    message.feedback === 'positive'
                      ? "bg-green-100 text-green-700"
                      : "bg-red-100 text-red-700"
                  )}>
                    {message.feedback === 'positive' ? 'Helpful' : 'Not helpful'}
                  </span>
                )}
              </div>
            </div>
            {!message.isBot && (
              <div className="w-8 h-8 bg-muted/50 rounded-lg flex items-center justify-center flex-shrink-0">
                <User className="w-4 h-4 text-muted-foreground" />
              </div>
            )}
          </div>
        ))}
        
        {isTyping && (
          <div className="flex gap-3 items-start justify-start">
            <div className="w-8 h-8 bg-primary/20 rounded-lg flex items-center justify-center flex-shrink-0 overflow-hidden">
              {config.avatarUrl ? (
                <img src={config.avatarUrl} alt="Agent avatar" className="w-full h-full object-cover" />
              ) : (
                <Bot className="w-4 h-4 text-primary" />
              )}
            </div>
            <div className="bg-muted/50 border border-border p-4 rounded-xl rounded-bl-md">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="border-t border-border p-6">
        <div className="flex gap-3">
          <input
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type your message here..."
            className="flex-1 bg-background border border-border rounded-xl px-4 py-3 text-sm text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50 transition-all"
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || isTyping}
            className="px-4 py-3 rounded-xl hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center min-w-[48px] text-primary-foreground"
            style={{ backgroundColor: config.primaryColor }}
          >
            <Send size={18} />
          </button>
        </div>
        <div className="mt-3 text-xs text-muted-foreground text-center">
          <a
            href={window.location.origin}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center gap-1.5 hover:text-foreground transition-colors group"
            aria-label="Powered by Rokovo - Visit our website"
          >
            <img
              src="/logo.svg"
              alt="Rokovo logo"
              className="w-4 h-4 group-hover:scale-110 transition-transform"
            />
            <span className="font-medium">Powered by Rokovo</span>
          </a>
        </div>
      </div>
    </div>
  );
};

export default ChatbotPreview;
