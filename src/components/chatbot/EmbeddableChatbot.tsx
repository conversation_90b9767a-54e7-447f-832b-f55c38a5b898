import React, { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Send, Bot, User, Mic, MicOff, Phone, PhoneOff, Minimize2, Maximize2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { apiService } from "@/services/api";
import { ChatbotConfiguration, ChatbotMessage } from "@/types/api";
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import MessageFeedback from "./MessageFeedback";

interface EmbeddableChatbotProps {
  publishableKey: string;
}

const EmbeddableChatbot: React.FC<EmbeddableChatbotProps> = ({ publishableKey }) => {
  const [config, setConfig] = useState<ChatbotConfiguration | null>(null);
  const [messages, setMessages] = useState<ChatbotMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Load configuration and initialize session
  useEffect(() => {
    const initializeChatbot = async () => {
      try {
        setIsLoading(true);
        
        // Load configuration
        const configData = await apiService.getChatbotConfigurationByKey(publishableKey);
        if (!configData) {
          setError("Chatbot configuration not found");
          return;
        }
        
        setConfig(configData);
        
        // Initialize session
        const sessionResponse = await apiService.initSession(`external_${Date.now()}`);
        setSessionId(sessionResponse.id);
        
        // Set welcome message
        const welcomeMessage: ChatbotMessage = {
          id: '1',
          content: `Hello! I'm ${configData.agentName} from ${configData.businessName}. How can I help you today?`,
          role: 'assistant',
          timestamp: new Date()
        };
        
        setMessages([welcomeMessage]);
        
      } catch (err) {
        console.error('Failed to initialize chatbot:', err);
        setError("Failed to load chatbot");
      } finally {
        setIsLoading(false);
      }
    };

    if (publishableKey) {
      initializeChatbot();
    }
  }, [publishableKey]);

  // Cleanup session on unmount
  useEffect(() => {
    return () => {
      if (sessionId) {
        apiService.closeSession(sessionId).catch(console.error);
      }
    };
  }, [sessionId]);

  // Handle message feedback
  const handleMessageFeedback = (messageId: string, feedback: 'positive' | 'negative') => {
    setMessages(prev => prev.map(msg =>
      msg.id === messageId
        ? { ...msg, feedback: msg.feedback === feedback ? null : feedback }
        : msg
    ));

    // Store feedback in localStorage
    const feedbackKey = `message_feedback_${messageId}`;
    const existingFeedback = localStorage.getItem(feedbackKey);

    if (existingFeedback === feedback) {
      // Remove feedback if clicking the same button
      localStorage.removeItem(feedbackKey);
    } else {
      // Store new feedback
      localStorage.setItem(feedbackKey, JSON.stringify({
        messageId,
        feedback,
        timestamp: new Date().toISOString(),
        sessionId
      }));
    }
  };

  const handleSendMessage = async (messageText?: string) => {
    const textToSend = messageText || inputMessage;
    if (!textToSend.trim() || !sessionId || !config) return;

    const userMessage: ChatbotMessage = {
      id: Date.now().toString(),
      content: textToSend,
      role: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    if (!messageText) setInputMessage(''); // Only clear input if not from voice
    setIsTyping(true);

    try {
      const response = await apiService.continueSession({
        sessionId,
        content: textToSend
      });

      // Add assistant response
      const assistantMessage: ChatbotMessage = {
        id: (Date.now() + 1).toString(),
        content: response[1].content, // Get assistant response
        role: 'assistant',
        timestamp: new Date()
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Failed to send message:', error);

      // Add error message
      const errorMessage: ChatbotMessage = {
        id: (Date.now() + 1).toString(),
        content: "I'm sorry, I'm having trouble responding right now. Please try again.",
        role: 'assistant',
        timestamp: new Date()
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const getBubbleStyle = (style: string) => {
    switch (style) {
      case 'square':
        return 'rounded-none';
      case 'pill':
        return 'rounded-full';
      default:
        return 'rounded-lg';
    }
  };



  if (isLoading) {
    return (
      <div className="w-full h-full max-w-md mx-auto bg-white rounded-lg shadow-xl border overflow-hidden flex items-center justify-center">
        <div className="p-6 sm:p-8 text-center">
          <div className="animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 border-b-2 border-orange-500 mx-auto mb-3 sm:mb-4"></div>
          <p className="text-gray-600 text-sm sm:text-base">Loading chatbot...</p>
          <p className="text-gray-400 text-xs mt-2">Please wait while we set up your chat experience</p>
        </div>
      </div>
    );
  }

  if (error || !config) {
    return (
      <div className="w-full h-full max-w-md mx-auto bg-white rounded-lg shadow-xl border overflow-hidden flex items-center justify-center">
        <div className="p-6 sm:p-8 text-center">
          <div className="text-red-500 mb-3 sm:mb-4">
            <Bot className="w-10 h-10 sm:w-12 sm:h-12 mx-auto" />
          </div>
          <h3 className="font-semibold text-gray-900 mb-2 text-sm sm:text-base">Chatbot Unavailable</h3>
          <p className="text-gray-600 text-xs sm:text-sm">{error || "This chatbot configuration could not be found"}</p>
          <p className="text-gray-400 text-xs mt-2">Please check the embed code and try again</p>
        </div>
      </div>
    );
  }

  if (isMinimized) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsMinimized(false)}
          className="rounded-full w-12 h-12 sm:w-14 sm:h-14 shadow-lg hover:scale-105 transition-transform p-2 sm:p-3"
          style={{ backgroundColor: config.primaryColor }}
          aria-label="Open chat"
        >
          <img
            src="/logo.svg"
            alt="Rokovo logo"
            className="w-full h-full object-contain"
          />
        </Button>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className="w-full h-full max-w-md mx-auto bg-card/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-border overflow-hidden flex flex-col">
        {/* Header */}
        <div className="bg-primary/10 border-b border-border p-4 sm:p-6 flex items-center gap-3 sm:gap-4 flex-shrink-0">
          <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-xl flex items-center justify-center flex-shrink-0 overflow-hidden" style={{ backgroundColor: config.primaryColor }}>
            {config.avatarUrl ? (
              <img src={config.avatarUrl} alt="Agent avatar" className="w-full h-full object-cover" />
            ) : (
              <Bot className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
            )}
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-foreground text-base sm:text-lg truncate">{config.agentName}</h3>
            <div className="flex items-center gap-2 mt-1">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <p className="text-xs sm:text-sm text-muted-foreground truncate">Online • {config.businessName}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="relative">
                  <Button
                    variant="ghost"
                    size="sm"
                    disabled
                    className="w-8 h-8 rounded-lg opacity-50 cursor-not-allowed flex items-center justify-center"
                    aria-label="Voice calls feature coming soon - currently unavailable"
                  >
                    <Phone className="w-4 h-4 text-muted-foreground" />
                  </Button>
                  {/* Coming Soon Badge */}
                  <div className="absolute -top-1 -right-1 bg-gradient-to-r from-orange-400 to-orange-500 text-white text-[8px] font-bold px-1 py-0.5 rounded-full shadow-sm">
                    SOON
                  </div>
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom" className="glassmorphism border-white/20">
                <div className="text-center">
                  <p className="font-medium text-foreground">Voice Calls</p>
                  <p className="text-xs text-muted-foreground mt-1">Coming Soon!</p>
                  <p className="text-xs text-muted-foreground">Talk directly with your AI agent</p>
                </div>
              </TooltipContent>
            </Tooltip>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMinimized(true)}
              className="w-8 h-8 rounded-lg hover:bg-muted/50 flex items-center justify-center transition-colors"
              title="Minimize chat"
            >
              <Minimize2 className="w-4 h-4 text-muted-foreground" />
            </Button>
          </div>
        </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 sm:p-6 space-y-4 scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent min-h-0">
        {messages.map((message) => (
          <div
            key={message.id}
            className={cn(
              "flex gap-3 items-start group",
              message.role === 'user' ? "justify-end" : "justify-start"
            )}
          >
            {message.role === 'assistant' && (
              <div className="w-8 h-8 bg-primary/20 rounded-lg flex items-center justify-center flex-shrink-0 overflow-hidden">
                {config.avatarUrl ? (
                  <img src={config.avatarUrl} alt="Agent avatar" className="w-full h-full object-cover" />
                ) : (
                  <Bot className="w-4 h-4 text-primary" />
                )}
              </div>
            )}
            <div className="flex flex-col max-w-[80%]">
              <div className="relative">
                <div
                  className={cn(
                    "p-4 rounded-xl text-sm leading-relaxed",
                    message.role === 'user'
                      ? "text-primary-foreground rounded-br-md"
                      : "bg-muted/50 text-foreground border border-border rounded-bl-md"
                  )}
                  style={message.role === 'user' ? { backgroundColor: config.primaryColor } : {}}
                >
                  {message.content}
                </div>
                {message.role === 'assistant' && (
                  <div className="absolute bottom-2 right-2">
                    <MessageFeedback
                      messageId={message.id}
                      currentFeedback={message.feedback}
                      onFeedback={handleMessageFeedback}
                    />
                  </div>
                )}
              </div>
              <div className={cn(
                "text-xs text-muted-foreground mt-1 px-1 flex items-center justify-between",
                message.role === 'user' ? "flex-row-reverse" : "flex-row"
              )}>
                <span>
                  {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </span>
                {message.role === 'assistant' && message.feedback && (
                  <span className={cn(
                    "text-xs px-1.5 py-0.5 rounded-full",
                    message.feedback === 'positive'
                      ? "bg-green-100 text-green-700"
                      : "bg-red-100 text-red-700"
                  )}>
                    {message.feedback === 'positive' ? 'Helpful' : 'Not helpful'}
                  </span>
                )}
              </div>
            </div>
            {message.role === 'user' && (
              <div className="w-8 h-8 bg-muted/50 rounded-lg flex items-center justify-center flex-shrink-0">
                <User className="w-4 h-4 text-muted-foreground" />
              </div>
            )}
          </div>
        ))}
        
        {isTyping && (
          <div className="flex gap-3 items-start justify-start">
            <div className="w-8 h-8 bg-primary/20 rounded-lg flex items-center justify-center flex-shrink-0 overflow-hidden">
              {config.avatarUrl ? (
                <img src={config.avatarUrl} alt="Agent avatar" className="w-full h-full object-cover" />
              ) : (
                <Bot className="w-4 h-4 text-primary" />
              )}
            </div>
            <div className="bg-muted/50 border border-border p-4 rounded-xl rounded-bl-md">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="border-t border-border p-4 sm:p-6 flex-shrink-0">
        <div className="flex gap-3">
          <input
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder="Type your message here..."
            className="flex-1 bg-background border border-border rounded-xl px-4 py-3 text-sm text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50 transition-all"
            disabled={isTyping}
          />
          <button
            onClick={() => handleSendMessage()}
            disabled={!inputMessage.trim() || isTyping}
            className="px-4 py-3 rounded-xl hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center min-w-[48px] text-primary-foreground"
            style={{ backgroundColor: config.primaryColor }}
          >
            <Send size={18} />
          </button>
        </div>
        <div className="mt-3 text-xs text-muted-foreground text-center">
          <a
            href={window.location.origin}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center gap-1.5 hover:text-foreground transition-colors group"
            aria-label="Powered by Rokovo - Visit our website"
          >
            <img
              src="/logo.svg"
              alt="Rokovo logo"
              className="w-4 h-4 group-hover:scale-110 transition-transform"
            />
            <span className="font-medium">Powered by Rokovo</span>
          </a>
          </div>
        </div>
      </div>
    </TooltipProvider>
  );
};

export default EmbeddableChatbot;
