// API Types based on Swagger schema
export interface UserSettings {
  businessName?: string;
  businessType?: string;
  agentName?: string;
  businessDescription?:string
}

// Tools
export type HttpMethod = "GET" | "POST" | "PUT" | "PATCH" | "DELETE";
export type AuthModel = "NONE" | "BASIC" | "BEARER" | "API_KEY" | "OAUTH2";
export type ApiKeyIn = "HEADER" | "QUERY";

export interface AuthInfo {
  model: AuthModel;
  username?: string;
  password?: string;
  token?: string;
  apiKeyName?: string;
  apiKeyIn?: ApiKeyIn;
  clientId?: string;
  clientSecret?: string;
  tokenUrl?: string;
  scopes?: string[];
}
interface ToolAuth {
  model: AuthModel
  username: string;
  password: string;
  token: string;
  apiKeyName: string;
  apiKeyIn: ApiKeyIn
}
export interface ToolSettings {
  queryParams?: Record<string, any>;
  bodyParams?: Record<string, any>;
  outputSchema?: Record<string, any>;
  headerParams: Record<string, any>;
  pathParams: Record<string, any>;
  headers?: Record<string, string>;
  parameters?: Record<string, any>;
  endpoint: string;
  method: HttpMethod;
  auth: AuthInfo;
  
}
export interface ToolSetting {
  queryParams: Record<string, any>;
  bodyParams: Record<string, any>;
  headerParams: Record<string, any>;
  pathParams: Record<string, any>;
  outputSchema: Record<string, any>;
  endpoint: string;
  method: string; 
  auth: ToolAuth;
  headers: Record<string, any>;
}

export interface Tool {
  id: string;
  name: string;
  description: string;
  setting: ToolSettings;
  userId: string;
  createdAt?:string
}

// Transports
export interface TelegramConfig {
  token: string;
}

export interface DiscordConfig {
  token: string;
}

export interface PureApiConfig {
  apiKeyHash: string;
}

export interface ChatbotConfig {
  publishableKey: string;
  businessName: string;
  agentName: string;
  primaryColor: string;
  secondaryColor: string;
  chatBubbleStyle: 'rounded' | 'square' | 'pill';
  avatarUrl?: string;
  isActive: boolean;
}

export interface TransportConfig {
  telegram?: TelegramConfig;
  discord?: DiscordConfig;
  pure?: PureApiConfig;
  chatbot?: ChatbotConfig;
  apiKeyHash?: string; // For backward compatibility
}

enum TransportType {
  TELEGRAM = 'TELEGRAM',
  DISCORD = 'DISCORD',
  PURE = 'PURE',
  CHATBOT = 'CHATBOT',
}

interface Transport {
  id: string;
  createdAt: string;
  updatedAt: string;
  name: string;
  description: string;
  image: string;
  type: TransportType;
}
export interface TransportUser {
  id: string;
  userId: string;
  transportId: string;
  transport :Transport;
  setting: TransportConfig;
}

// Knowledge Base

export enum KnowledgeBaseStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  FINISH = 'FINISH',
  FAILED = 'FAILED',
}
export enum KnowledgeBaseType {
  LINK = "LINK",
  QA = "QA",
  TEXT = "TEXT",
  FILE = "FILE",
}

export interface QAPair {
  question: string;
  answer: string;
}

export interface KnowledgeBaseItem {
  id: string;
  sourceType:KnowledgeBaseType;
  content: string;
  createdAt: string;
  status: KnowledgeBaseStatus;
  s3Id:string;
  name:string;
}

// Billing & Credits
export type CreditStatus = "ACTIVE" | "EXPIRED";

export interface Billing {
  id: string;
  userId: string;
  available: number;
  startDate: string;
  endDate: string;
  status: CreditStatus;
}

// Invoices
export type InvoiceStatus =
  | "PENDING"
  | "PAID"
  | "FAILED"
  | "CANCELLED"
  | "REFUNDED";
export type PaymentProvider = "stripe" | "zarinpal";

export interface Invoice {
  id: string;
  creditId: string;
  amount: number;
  issuedAt: string;
  paidAt?: string;
  status: InvoiceStatus;
  paymentProvider: PaymentProvider;
  paymentMethod?: string;
  description?: string;
}

// API Response Types
export interface PageMeta {
  page: number;
  total: number;
  limit: number;
}

export interface PaginatedResponse<T> {
  data: T;
  meta: PageMeta;
}
export interface PaginatedItemResponse<T> {
  items: T;
  pagination: PageMeta;
}

export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

export interface PaginatedApiResponse<T> {
  success: boolean;
  data: PaginatedResponse<T>;
  message?: string;
}
export interface PaginatedItemsApiResponse<T> {
  success: boolean;
  data: PaginatedItemResponse<T>;
  message?: string;
}


interface VariableSettings {
  businessName: string;
  businessType: string;
  agentName: string;
  businessDescription: string;
}

interface UserSetting {
  variables: VariableSettings;
}

interface PromptSetting {
  sampling: {
    temp: number;
    topK: number;
    topP: number;
  };
  outputControl: {
    maxTokens: number;
    repetitionPenalty: number;
    frequencyPenalty: number;
    presencePenalty: number;
  };
}

interface Prompt {
  id: string;
  type: string;
  content: string;
  userId: string;
  user: string;
  setting: PromptSetting;
  createdAt: string;
  updatedAt: string;
}

interface ToolSession {
  id: string;
  name: string;
  description: string;
  setting: ToolSetting;
  userId: string;
  isPublic: boolean;
  user: string;
  createdAt: string;
  updatedAt: string;
}

interface TransportSession {
  id: string;
  name: string;
  description: string;
  image: string;
  type: string;
  users: string[];
  sessions: string[];
  createdAt: string;
  updatedAt: string;
}

interface UserSession {
  id: string;
  name: string;
  email: string;
  avatar: string;
  slug: string;
  setting: UserSetting;
  prompts: Prompt[];
  tools: ToolSession[];
  transports: TransportSession[];
  createdAt: string;
  updatedAt: string;
}

interface Message {
  id: string;
  content: string;
  role: "user" | "assistant" | "system";
  sessionId: string;
  session: string;
  createdAt: string;
  updatedAt: string;
}

export interface Session {
  id: string;
  externalUserId: string;
  status: "OPEN" | "CLOSED" | "HITL";
  userId: string;
  transportId: string;
  user: UserSession;
  transport: TransportSession;
  messages: Message[];
  createdAt: string;
  updatedAt: string;
}

// Chatbot Customization Types
export interface ChatbotConfiguration {
  id: string;
  userId: string;
  businessName: string;
  agentName: string;
  primaryColor: string;
  secondaryColor: string;
  chatBubbleStyle: 'rounded' | 'square' | 'pill';
  avatarUrl?: string;
  publishableKey: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ChatbotMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  feedback?: 'positive' | 'negative' | null;
}

export interface ChatbotSession {
  id: string;
  externalUserId: string;
  messages: ChatbotMessage[];
  isActive: boolean;
  createdAt: string;
}

// Mock API Response Types for Chatbot
export interface InitSessionResponse {
  id: string;
}

export interface ContinueSessionRequest {
  sessionId: string;
  content: string;
}

export interface ContinueSessionResponse {
  content: string;
  role: 'user' | 'assistant';
}[]
