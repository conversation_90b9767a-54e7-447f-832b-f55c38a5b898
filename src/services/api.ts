import { KnowledgeItem } from "@/components/dashboard/rag/KnowledgeBaseBrowser";
import {
  UserSettings,
  Tool,
  ToolSettings,
  TransportUser,
  TransportConfig,
  ChatbotConfig,
  KnowledgeBaseItem,
  Invoice,
  Billing,
  PaginatedResponse,
  PaginatedApiResponse,
  ApiResponse,
  PaginatedItemResponse,
  PaginatedItemsApiResponse,
  QAPair,
  ChatbotConfiguration,
  ChatbotSession,
  InitSessionResponse,
  ContinueSessionRequest,
  ContinueSessionResponse,
} from "@/types/api";
import { Session } from "inspector/promises";
import { Message } from "react-hook-form";

export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

class ApiService {
  private token: string | null = null;

  setToken(token: string) {
    this.token = token;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;

    const isFormData = options.body instanceof FormData;

    const headers = {
      ...(isFormData ? {} : { "Content-Type": "application/json" }),
      ...(this.token && { Authorization: `Bearer ${this.token}` }),
      ...options.headers,
    };

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }

    if (response.status === 204) {
      return null as any;
    }

    return response.json();
  }

  // User Settings
  async getUserSettings(): Promise<UserSettings> {
    const res = await this.request<ApiResponse<UserSettings>>("/user/setting");
    return res.data;
  }

  async updateUserSettings(settings: UserSettings): Promise<void> {
    return this.request<void>("/user/setting", {
      method: "PATCH",
      body: JSON.stringify(settings),
    });
  }

  // Tools Management
  async getAllTools(): Promise<PaginatedResponse<Tool[]>> {
    const res = await this.request<PaginatedApiResponse<Tool[]>>("/tools");
    return res.data;
  }

  async getTool(id: string): Promise<Tool> {
    const res = await this.request<ApiResponse<Tool>>(`/tools/${id}`);
    return res.data;
  }

  async createTool(tool: Omit<Tool, "id" | "userId">): Promise<Tool> {
    return this.request<Tool>("/tools", {
      method: "POST",
      body: JSON.stringify(tool),
    });
  }

  async updateTool(id: string, settings: ToolSettings): Promise<Tool> {
    return this.request<Tool>(`/tools/${id}`, {
      method: "PATCH",
      body: JSON.stringify(settings),
    });
  }

  async deleteTool(id: string): Promise<void> {
    return this.request<void>(`/tools/${id}`, {
      method: "DELETE",
    });
  }

  // Transport Management
  async getUserTransports(): Promise<PaginatedResponse<TransportUser[]>> {
    const res = await this.request<PaginatedApiResponse<TransportUser[]>>(
      "/user-transports?join=transport"
    );

    // Get chatbot transports from localStorage (mock implementation)
    const chatbotTransports = JSON.parse(localStorage.getItem('user_transports') || '[]');

    // Combine both sources
    const combinedTransports = [...res.data.data, ...chatbotTransports];

    return {
      ...res.data,
      data: combinedTransports
    };
  }

  async getTransportUser(id: string): Promise<TransportUser> {
    return this.request<TransportUser>(`/user-transports/${id}`);
  }

  async createPureApiTransport(): Promise<TransportUser> {
    return this.request<TransportUser>("/user-transports/pure-api", {
      method: "POST",
    });
  }

  async createTelegramTransport(token: string): Promise<TransportUser> {
    return this.request<TransportUser>("/user-transports/telegram", {
      method: "POST",
      body: JSON.stringify({ token }),
    });
  }

  async createChatbotTransport(config: Omit<ChatbotConfig, 'publishableKey' | 'isActive'>): Promise<TransportUser> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        try {
          // Backend would generate a secure publishable key
          const publishableKey = `pk_live_${Date.now()}_${Math.random().toString(36).substr(2, 20)}`;

          const chatbotConfig: ChatbotConfig = {
            ...config,
            publishableKey,
            isActive: true
          };

          // Mock backend response - in real implementation this would be handled by the server
          const mockTransport: TransportUser = {
            id: `transport_${Date.now()}_${Math.random().toString(36).substr(2, 8)}`,
            userId: 'mock_user_id',
            transportId: 'chatbot_transport_id',
            transport: {
              id: 'chatbot_transport_id',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              name: 'Website Chatbot',
              description: 'Embeddable chatbot for your website',
              image: '/chatbot-icon.svg',
              type: 'CHATBOT' as any
            },
            setting: {
              chatbot: chatbotConfig
            }
          };

          // Store in localStorage (mock backend persistence)
          const existingTransports = JSON.parse(localStorage.getItem('user_transports') || '[]');
          const updatedTransports = [...existingTransports, mockTransport];
          localStorage.setItem('user_transports', JSON.stringify(updatedTransports));

          resolve(mockTransport);
        } catch (error) {
          reject(new Error('Failed to generate publishable key'));
        }
      }, 800); // Simulate backend processing time
    });
  }

  async updateChatbotTransport(transportId: string, config: Partial<ChatbotConfig>): Promise<TransportUser> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        try {
          // Mock implementation - update in localStorage
          const existingTransports = JSON.parse(localStorage.getItem('user_transports') || '[]');
          const transportIndex = existingTransports.findIndex((t: TransportUser) => t.id === transportId);

          if (transportIndex !== -1) {
            // Preserve the publishable key and other critical fields
            const currentConfig = existingTransports[transportIndex].setting.chatbot;
            existingTransports[transportIndex].setting.chatbot = {
              ...currentConfig,
              ...config,
              // Ensure publishable key is never overwritten
              publishableKey: currentConfig.publishableKey,
              // Update timestamp
              updatedAt: new Date().toISOString()
            };

            localStorage.setItem('user_transports', JSON.stringify(existingTransports));
            resolve(existingTransports[transportIndex]);
          } else {
            reject(new Error('Transport not found'));
          }
        } catch (error) {
          reject(new Error('Failed to update chatbot configuration'));
        }
      }, 400); // Simulate backend processing time
    });
  }

  async deleteTransport(id: string): Promise<void> {
    return this.request<void>(`/user-transports/${id}`, {
      method: "DELETE",
    });
  }

  // Knowledge Base
  async getKnowledgeBase(
    query?: string
  ): Promise<PaginatedResponse<KnowledgeBaseItem[]>> {
    const url = query ? `/knowledge-base?${query}` : `/knowledge-base`;
    const res = await this.request<PaginatedApiResponse<KnowledgeBaseItem[]>>(
      url
    );
    return res.data;
  }
  async getKnowledgeBaseDownloadLink(id?: string): Promise<string> {
    const res = await this.request<ApiResponse<string>>(
      `/knowledge-base/download/${id}`
    );
    return res.data;
  }

  async getKnowledgeBaseVectors(
    page = 1,
    limit = 10,
    search = ""
  ): Promise<PaginatedItemResponse<KnowledgeItem[]>> {
    const res = await this.request<PaginatedItemsApiResponse<KnowledgeItem[]>>(
      `/knowledge-base/vectors?q=${search}`
    );
    return res.data;
  }

  async addTextToKnowledgeBase(content: string): Promise<void> {
    return this.request<void>("/knowledge-base/text", {
      method: "POST",
      body: JSON.stringify({ content }),
    });
  }

  async addLinkToKnowledgeBase(link: string): Promise<void> {
    return this.request<void>("/knowledge-base/link", {
      method: "POST",
      body: JSON.stringify({ link }),
    });
  }

  async addPDFToKnowledgeBase(file: File): Promise<void> {
    const formData = new FormData();
    formData.append("file", file);

    return this.request<void>("/knowledge-base/file", {
      method: "POST",
      body: formData,
    });
  }

  async addQAToKnowledgeBase(pairs: QAPair[]): Promise<void> {
    return this.request<void>("/knowledge-base/qa", {
      method: "POST",
      body: JSON.stringify(pairs),
    });
  }

  async updateKnowledgeBases(form: KnowledgeItem): Promise<void> {
    return this.request<void>("/knowledge-base", {
      method: "PATCH",
      body: JSON.stringify(form),
    });
  }
  async clearVectorKnowledgeBase(id: string): Promise<void> {
    return this.request<void>(`/knowledge-base/vectors/${id}`, {
      method: "DELETE",
    });
  }
  async clearAllKnowledgeBase(): Promise<void> {
    return this.request<void>("/knowledge-base", {
      method: "DELETE",
    });
  }

  // Billing & Credits
  async getUserCredit(): Promise<number> {
    const res = await this.request<ApiResponse<number>>("/billing/credit");
    return res.data;
  }

  async getBillingHistory(): Promise<PaginatedResponse<Billing[]>> {
    const res = await this.request<PaginatedApiResponse<Billing[]>>("/billing");
    return res.data;
  }

  async getUserInvoices(userId: string): Promise<Invoice[]> {
    return this.request<Invoice[]>(`/invoices/user/${userId}`);
  }

  // sessions

  async getAllSessions(): Promise<PaginatedResponse<Session[]>> {
    const res = await this.request<PaginatedApiResponse<Session[]>>(
      "/sessions?join=messages,transport&sort=createdAt:DESC"
    );
    return res.data;
  }
  async getTestAgent(query?: string): Promise<PaginatedResponse<Session[]>> {
    const res = await this.request<PaginatedApiResponse<Session[]>>(
      `/transport/test-agent?${query}`
    );
    return res.data;
  }

  async createConversationWithTestAgent(): Promise<void> {
    return this.request<void>("/transport/test-agent", {
      method: "POST",
    });
  }

  async sendMessageToTestAgent(
    form: { content: string },
    id: string
  ): Promise<Message[]> {
    const res = await this.request<ApiResponse<Message[]>>(
      `/transport/test-agent/${id}`,
      {
        method: "PATCH",
        body: JSON.stringify(form),
      }
    );
    return res.data;
  }

  async deleteSessionsTestAgent(id: string): Promise<void> {
    return this.request<void>(`/transport/test-agent/${id}`, {
      method: "DELETE",

    });
  }
  async deleteSessions(id: string): Promise<void> {
    return this.request<void>(`/sessions/${id}`, {
      method: "DELETE",
    });
  }

  // Chatbot Configuration Methods (Mock implementation with localStorage)
  async getChatbotConfiguration(): Promise<ChatbotConfiguration | null> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const config = localStorage.getItem('chatbot_configuration');
        resolve(config ? JSON.parse(config) : null);
      }, 300);
    });
  }

  async saveChatbotConfiguration(config: Omit<ChatbotConfiguration, 'id' | 'userId' | 'publishableKey' | 'createdAt' | 'updatedAt'>): Promise<ChatbotConfiguration> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newConfig: ChatbotConfiguration = {
          ...config,
          id: `config_${Date.now()}`,
          userId: 'mock_user_id',
          publishableKey: `pk_${Date.now()}_${Math.random().toString(36).substr(2, 16)}`,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        localStorage.setItem('chatbot_configuration', JSON.stringify(newConfig));
        resolve(newConfig);
      }, 500);
    });
  }

  async updateChatbotConfiguration(id: string, config: Partial<ChatbotConfiguration>): Promise<ChatbotConfiguration> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const existingConfig = localStorage.getItem('chatbot_configuration');
        if (existingConfig) {
          const parsedConfig = JSON.parse(existingConfig);
          const updatedConfig = {
            ...parsedConfig,
            ...config,
            updatedAt: new Date().toISOString(),
          };

          localStorage.setItem('chatbot_configuration', JSON.stringify(updatedConfig));
          resolve(updatedConfig);
        } else {
          // If no existing config, create new one
          const newConfig: ChatbotConfiguration = {
            businessName: config.businessName || 'Your Business',
            agentName: config.agentName || 'AI Assistant',
            primaryColor: config.primaryColor || '#ff6600',
            secondaryColor: config.secondaryColor || '#f0f0f0',
            chatBubbleStyle: config.chatBubbleStyle || 'rounded',
            isActive: config.isActive ?? true,
            id: `config_${Date.now()}`,
            userId: 'mock_user_id',
            publishableKey: `pk_${Date.now()}_${Math.random().toString(36).substr(2, 16)}`,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };

          localStorage.setItem('chatbot_configuration', JSON.stringify(newConfig));
          resolve(newConfig);
        }
      }, 500);
    });
  }

  async getChatbotConfigurationByKey(publishableKey: string): Promise<ChatbotConfiguration | null> {
    return new Promise((resolve) => {
      setTimeout(() => {
        // Check chatbot transports in localStorage
        const chatbotTransports = JSON.parse(localStorage.getItem('user_transports') || '[]');
        const transport = chatbotTransports.find((t: any) =>
          t.setting?.chatbot?.publishableKey === publishableKey
        );

        if (transport?.setting?.chatbot) {
          const chatbotConfig = transport.setting.chatbot;
          // Convert to old ChatbotConfiguration format for compatibility
          const config: ChatbotConfiguration = {
            id: transport.id,
            userId: transport.userId,
            businessName: chatbotConfig.businessName,
            agentName: chatbotConfig.agentName,
            primaryColor: chatbotConfig.primaryColor,
            secondaryColor: chatbotConfig.secondaryColor,
            chatBubbleStyle: chatbotConfig.chatBubbleStyle,
            avatarUrl: chatbotConfig.avatarUrl,
            publishableKey: chatbotConfig.publishableKey,
            isActive: chatbotConfig.isActive,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };
          resolve(config);
        } else {
          resolve(null);
        }
      }, 300);
    });
  }

  // Mock Chatbot Session Methods (to be replaced with real API)
  async initSession(externalUserId: string): Promise<InitSessionResponse> {
    // Mock implementation with local storage persistence
    return new Promise((resolve) => {
      setTimeout(() => {
        const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        // Store session in localStorage for persistence
        const sessionData = {
          id: sessionId,
          externalUserId,
          messages: [],
          createdAt: new Date().toISOString(),
          isActive: true
        };

        localStorage.setItem(`chatbot_session_${sessionId}`, JSON.stringify(sessionData));

        resolve({ id: sessionId });
      }, 300);
    });
  }

  async continueSession(request: ContinueSessionRequest): Promise<ContinueSessionResponse> {
    // Mock implementation with context-aware responses
    return new Promise((resolve) => {
      setTimeout(() => {
        const { sessionId, content } = request;

        // Get session from localStorage
        const sessionData = localStorage.getItem(`chatbot_session_${sessionId}`);
        let session = sessionData ? JSON.parse(sessionData) : null;

        // Generate contextual responses based on message content
        let response = this.generateContextualResponse(content, session?.messages || []);

        // Update session with new messages
        if (session) {
          session.messages.push(
            { content, role: 'user', timestamp: new Date().toISOString() },
            { content: response, role: 'assistant', timestamp: new Date().toISOString() }
          );
          localStorage.setItem(`chatbot_session_${sessionId}`, JSON.stringify(session));
        }

        // Return only the assistant's response as per ContinueSessionResponse type
        const assistantMessage = { content: response, role: 'assistant' as const };

        resolve(assistantMessage);
      }, 800 + Math.random() * 1200);
    });
  }

  private generateContextualResponse(message: string, previousMessages: any[]): string {
    const lowerMessage = message.toLowerCase();

    // Greeting responses
    if (lowerMessage.includes('hello') || lowerMessage.includes('hi') || lowerMessage.includes('hey')) {
      return "Hello! I'm here to help you. What can I assist you with today?";
    }

    // Pricing inquiries
    if (lowerMessage.includes('price') || lowerMessage.includes('cost') || lowerMessage.includes('pricing')) {
      return "I'd be happy to help you with pricing information! Our pricing varies based on your specific needs. Would you like me to connect you with our sales team for a personalized quote?";
    }

    // Product/service inquiries
    if (lowerMessage.includes('product') || lowerMessage.includes('service') || lowerMessage.includes('what do you')) {
      return "We offer comprehensive AI solutions designed to help businesses automate and enhance their customer interactions. Our platform includes chatbots, voice assistants, and intelligent automation tools. What specific area interests you most?";
    }

    // Support requests
    if (lowerMessage.includes('help') || lowerMessage.includes('support') || lowerMessage.includes('problem')) {
      return "I'm here to help! Could you please describe the specific issue or question you have? The more details you provide, the better I can assist you.";
    }

    // Contact information
    if (lowerMessage.includes('contact') || lowerMessage.includes('phone') || lowerMessage.includes('email')) {
      return "You can reach our team through this chat, or if you prefer, I can help you schedule a call with one of our specialists. What would work best for you?";
    }

    // Demo requests
    if (lowerMessage.includes('demo') || lowerMessage.includes('trial') || lowerMessage.includes('test')) {
      return "I'd be happy to arrange a demo for you! Our demos are personalized to show how our AI solutions can specifically benefit your business. Would you like to schedule one now?";
    }

    // Integration questions
    if (lowerMessage.includes('integrate') || lowerMessage.includes('api') || lowerMessage.includes('connect')) {
      return "Our platform offers flexible integration options including REST APIs, webhooks, and pre-built connectors for popular platforms. What system are you looking to integrate with?";
    }

    // Follow-up based on conversation context
    if (previousMessages.length > 2) {
      const responses = [
        "Based on our conversation, I think I can help you further with that. What specific aspect would you like to explore?",
        "That's a great follow-up question! Let me provide you with more detailed information about that.",
        "I can see you're interested in learning more. Here's additional information that might be helpful.",
      ];
      return responses[Math.floor(Math.random() * responses.length)];
    }

    // Default responses
    const defaultResponses = [
      "Thank you for your message! I'm here to help you with any questions about our AI solutions. Could you tell me more about what you're looking for?",
      "I understand you're interested in learning more. What specific aspect of our services would you like to know about?",
      "That's a great question! I'd be happy to provide more information. What would be most helpful for you to know?",
      "I'm here to assist you with any questions or concerns. How can I best help you today?",
    ];

    return defaultResponses[Math.floor(Math.random() * defaultResponses.length)];
  }

  async closeSession(sessionId: string): Promise<void> {
    // Mock implementation with cleanup
    return new Promise((resolve) => {
      setTimeout(() => {
        // Update session status in localStorage
        const sessionData = localStorage.getItem(`chatbot_session_${sessionId}`);
        if (sessionData) {
          const session = JSON.parse(sessionData);
          session.isActive = false;
          session.closedAt = new Date().toISOString();
          localStorage.setItem(`chatbot_session_${sessionId}`, JSON.stringify(session));
        }

        console.log(`Session ${sessionId} closed`);
        resolve();
      }, 200);
    });
  }
}

export const apiService = new ApiService();
