import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import PrePreparedToolsTab from "@/components/dashboard/tools/PrePreparedToolsTab";
import UserToolsTab from "@/components/dashboard/tools/UserToolsTab";
import CreateToolForm from "@/components/dashboard/tools/CreateToolForm";
import { Wrench, Plus } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiService } from "@/services/api";
import { Tool, ToolSettings } from "@/types/api";

const DashboardTools = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: toolsResponse, isLoading } = useQuery({
    queryKey: ["tools"],
    queryFn: () => apiService.getAllTools(),
  });
  const tools = toolsResponse?.data || [];

  const updateToolMutation = useMutation({
    mutationFn: ({ id, settings }: { id: string; settings: ToolSettings }) =>
      apiService.updateTool(id, settings),
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ["tools"] }),
  });

  const deleteToolMutation = useMutation({
    mutationFn: (id: string) => apiService.deleteTool(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["tools"] });
      toast({
        title: "Tool deleted",
        description: `Tool deleted successfully.`,
      });
    },
  });

  const handleActivateTool = async (tool: Tool) => {
    await apiService.createTool({
      name: tool.name,
      description: tool.description,
      setting: tool.setting,
      // status: "active",
    });
    queryClient.invalidateQueries({ queryKey: ["tools"] });
  };

  const handleUpdateTool = (tool: Tool) => {
    updateToolMutation.mutate({
      id: tool.id,
      settings: tool.setting,
    });
  };

  const handleDeleteTool = (id: string, name: string) => {
    deleteToolMutation.mutate(id);
    toast({
      title: "Tool deleted",
      description: `"${name}" has been removed.`,
    });
  };

  // const handleToggleEnabled = (toolId: string, enabled: boolean) => {
  //   const tool = tools?.find((t) => t.id === toolId);
  //   if (!tool) return;

  //   const updatedTool = { ...tool, status: enabled ? "active" : "inactive" };
  //   updateToolMutation.mutate(updatedTool);

  //   toast({
  //     title: enabled ? "Tool enabled" : "Tool disabled",
  //     description: `Tool is now ${enabled ? "active" : "inactive"}.`,
  //   });
  // };

  const activatedToolIds = tools?.map((t) => t.id);

  return (
    <DashboardLayout>
      <div className="space-y-8 animate-fade-in">
        {/* Enhanced Page Header */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <div className="p-2.5 bg-primary/10 rounded-xl">
                  <Wrench className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold tracking-tight text-foreground">
                    Tools Management
                  </h1>
                  <p className="text-lg text-muted-foreground">
                    Create and manage custom API tools for your agent
                  </p>
                </div>
              </div>
            </div>
            {/* Tool Stats */}
            <div className="hidden sm:flex items-center gap-2">
              <div className="flex items-center gap-2 bg-primary/10 text-primary text-sm font-medium px-3 py-1.5 rounded-full border border-primary/20">
                <span className="font-semibold">{tools?.length || 0}</span>
                Active Tools
              </div>
            </div>
          </div>
        </div>

        <Tabs defaultValue="my-tools" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 max-w-lg">
            {/* <TabsTrigger value="browse" className="flex items-center gap-2">
              <Package className="w-4 h-4" />
              Browse Tools
            </TabsTrigger> */}

            <TabsTrigger value="my-tools" className="flex items-center gap-2">
              <Wrench className="w-4 h-4" />
              My Tools
            </TabsTrigger>
            <TabsTrigger value="create" className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              Add New Tool
            </TabsTrigger>
          </TabsList>
          {/* <TabsContent value="browse" className="space-y-0">
            <PrePreparedToolsTab
              activatedToolIds={activatedToolIds}
              onActivateTool={handleActivateTool}
            />
          </TabsContent> */}
          <TabsContent value="my-tools">
            <UserToolsTab
              tools={tools}
              onUpdateTool={handleUpdateTool}
              onDeleteTool={handleDeleteTool}
              // onToggleEnabled={handleToggleEnabled}
              // isLoading={isLoading}
            />
          </TabsContent>

          <TabsContent value="create">
            <CreateToolForm />
          </TabsContent>

          <TabsContent value="browse">
            <PrePreparedToolsTab
              activatedToolIds={activatedToolIds}
              onActivateTool={handleActivateTool}
            />
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default DashboardTools;
