import { useLocation, <PERSON> } from "react-router-dom";
import { useEffect } from "react";
import { Bot, Home, ArrowLeft, Search, MessageSquare } from "lucide-react";
import { Button } from "@/components/ui/button";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-20 w-64 h-64 bg-primary rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-accent rounded-full blur-3xl animate-float"></div>
      </div>
      
      <div className="max-w-lg text-center space-y-8 relative z-10 animate-fade-in">
        {/* Enhanced Robot Icon */}
        <div className="relative">
          <div className="w-32 h-32 mx-auto bg-primary/10 rounded-full flex items-center justify-center animate-pulse-glow ring-4 ring-primary/20">
            <Bot className="w-16 h-16 text-primary" />
          </div>
          <div className="absolute -top-2 -right-2 w-8 h-8 bg-primary rounded-full animate-bounce shadow-lg flex items-center justify-center">
            <div className="w-3 h-3 bg-primary-foreground rounded-full"></div>
          </div>
          {/* Floating tech elements */}
          <div className="absolute top-2 left-2 w-2 h-2 bg-primary/40 rounded-full animate-float"></div>
          <div className="absolute bottom-2 right-2 w-3 h-3 bg-primary/30 rounded-full animate-bounce" style={{ animationDelay: '0.3s' }}></div>
          <div className="absolute top-8 right-8 w-1 h-1 bg-primary/50 rounded-full animate-pulse" style={{ animationDelay: '0.6s' }}></div>
        </div>

        {/* Enhanced Error Content */}
        <div className="space-y-6">
          <div className="space-y-3">
            <h1 className="text-7xl font-bold text-primary tracking-tighter bg-gradient-to-b from-primary to-primary/70 bg-clip-text text-transparent">
              404
            </h1>
            <h2 className="text-3xl font-semibold text-foreground">Page Not Found</h2>
          </div>
          <div className="space-y-4">
            <p className="text-lg text-muted-foreground max-w-md mx-auto leading-relaxed">
              Our AI agent couldn't locate the page you're looking for. It seems this URL has wandered off the neural network.
            </p>
            <div className="inline-flex items-center gap-2 bg-muted/50 text-muted-foreground text-sm px-4 py-2 rounded-full">
              <Search className="w-3 h-3" />
              AI Search Complete
            </div>
          </div>
          <div className="text-sm text-muted-foreground/70 font-mono bg-muted/50 rounded-xl p-4 border border-primary/20 shadow-inner">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
              <span className="font-semibold">Attempted route:</span>
            </div>
            <span className="text-primary font-medium">{location.pathname}</span>
          </div>
        </div>

        {/* Enhanced Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button 
            asChild 
            variant="default" 
            size="lg" 
            className="gap-2 bg-primary hover:bg-primary/90 shadow-lg hover:shadow-xl transition-all duration-200 hover:-translate-y-0.5"
          >
            <Link to="/">
              <Home className="w-4 h-4" />
              Return Home
            </Link>
          </Button>
          
          <Button 
            variant="outline" 
            size="lg" 
            className="gap-2 hover:bg-muted/50 shadow-sm hover:shadow-md transition-all duration-200"
            onClick={() => window.history.back()}
          >
            <ArrowLeft className="w-4 h-4" />
            Go Back
          </Button>
        </div>

        {/* Enhanced Helpful Links */}
        <div className="pt-8 border-t border-border/50">
          <div className="space-y-4">
            <p className="text-base font-medium text-muted-foreground">Looking for something specific?</p>
            <p className="text-sm text-muted-foreground/80">Try these popular destinations</p>
          </div>
          <div className="flex flex-wrap gap-3 justify-center mt-6">
            <Button 
              asChild 
              variant="ghost" 
              size="sm"
              className="gap-2 hover:bg-primary/10 hover:text-primary transition-colors"
            >
              <Link to="/dashboard">
                <Home className="w-3 h-3" />
                Dashboard
              </Link>
            </Button>
            <Button 
              asChild 
              variant="ghost" 
              size="sm"
              className="gap-2 hover:bg-muted/50 transition-colors"
            >
              <Link to="/support">
                <MessageSquare className="w-3 h-3" />
                Support
              </Link>
            </Button>
            <Button 
              asChild 
              variant="ghost" 
              size="sm"
              className="gap-2 hover:bg-muted/50 transition-colors"
            >
              <Link to="/about-us">
                <Bot className="w-3 h-3" />
                About Us
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
