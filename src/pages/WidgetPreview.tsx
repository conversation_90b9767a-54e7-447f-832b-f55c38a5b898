import React, { useEffect } from "react";
import { useSearchParams } from "react-router-dom";

const WidgetPreview = () => {
  const [searchParams] = useSearchParams();
  const publishableKey = searchParams.get('key');
  const businessName = searchParams.get('businessName') || 'Your Business';
  const agentName = searchParams.get('agentName') || 'AI Assistant';
  const primaryColor = searchParams.get('primaryColor') || '#ff6600';
  const secondaryColor = searchParams.get('secondaryColor') || '#f0f0f0';
  const chatBubbleStyle = searchParams.get('chatBubbleStyle') || 'rounded';
  const avatarUrl = searchParams.get('avatarUrl');

  useEffect(() => {
    if (!publishableKey) return;

    // Create and inject the widget script
    const script = document.createElement('script');
    script.src = `${window.location.origin}/Rokovo-widget.js`;
    script.setAttribute('data-publishable-key', publishableKey);
    script.setAttribute('data-business-name', businessName);
    script.setAttribute('data-agent-name', agentName);
    script.setAttribute('data-primary-color', primaryColor);
    script.setAttribute('data-secondary-color', secondaryColor);
    script.setAttribute('data-chat-bubble-style', chatBubbleStyle);
    if (avatarUrl) {
      script.setAttribute('data-avatar-url', avatarUrl);
    }
    script.async = true;

    document.body.appendChild(script);

    // Cleanup on unmount
    return () => {
      if (script.parentNode) {
        script.parentNode.removeChild(script);
      }
      // Remove widget container if it exists
      const widgetContainer = document.getElementById('Rokovo-widget-container');
      if (widgetContainer) {
        widgetContainer.remove();
      }
      // Remove widget styles
      const widgetStyles = document.getElementById('Rokovo-widget-styles');
      if (widgetStyles) {
        widgetStyles.remove();
      }
    };
  }, [publishableKey, businessName, agentName, primaryColor, secondaryColor, chatBubbleStyle, avatarUrl]);

  if (!publishableKey) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center p-4">
        <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full text-center">
          <div className="text-red-500 mb-4">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Invalid Widget Configuration</h2>
          <p className="text-gray-600 text-sm">
            No publishable key provided. Please check your widget configuration and try again.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-2xl w-full">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Widget Preview</h1>
          <p className="text-gray-600 mb-6">
            This is a preview of your chatbot widget. The widget will appear in the bottom-right corner.
          </p>
        </div>

        <div className="bg-gray-50 rounded-xl p-6 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Sample Website Content</h3>
          <p className="text-gray-700 mb-4">
            This represents your website content. The chatbot widget is embedded and ready to use.
          </p>
          <p className="text-gray-700 mb-4">
            <strong>Click the chat button in the bottom-right corner to test the widget!</strong>
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
            <div className="bg-white p-4 rounded-lg border">
              <h4 className="font-medium text-gray-900 mb-2">Widget Configuration</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li><strong>Business:</strong> {businessName}</li>
                <li><strong>Agent:</strong> {agentName}</li>
                <li><strong>Primary Color:</strong> <span className="inline-block w-4 h-4 rounded ml-1" style={{backgroundColor: primaryColor}}></span></li>
                <li><strong>Style:</strong> {chatBubbleStyle}</li>
              </ul>
            </div>
            
            <div className="bg-white p-4 rounded-lg border">
              <h4 className="font-medium text-gray-900 mb-2">Features</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>✓ Mobile responsive</li>
                <li>✓ Glassmorphism design</li>
                <li>✓ Collapsible interface</li>
                <li>✓ Real-time messaging</li>
                <li>✓ Custom branding</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="text-center">
          <p className="text-sm text-gray-500">
            This preview demonstrates how the widget will appear on your website.
            <br />
            The actual widget will load your configured AI agent and knowledge base.
          </p>
        </div>
      </div>
    </div>
  );
};

export default WidgetPreview;
