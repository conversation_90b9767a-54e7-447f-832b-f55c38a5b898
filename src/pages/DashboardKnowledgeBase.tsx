import React from "react";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import DataSourcesOverview from "@/components/dashboard/rag/DataSourcesOverview";
import KnowledgeBaseBrowser from "@/components/dashboard/rag/KnowledgeBaseBrowser";

const DashboardRagData = () => {
  return (
    <DashboardLayout>
      <div className="space-y-8 animate-fade-in">
        {/* Enhanced Page Header */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight text-foreground">
                Knowledge Base
              </h1>
              <p className="text-lg text-muted-foreground mt-1 max-w-2xl">
                Manage your AI agent's knowledge with documents, links, and custom content
              </p>
            </div>
            {/* Quick Stats Badge */}
            <div className="hidden sm:flex items-center gap-2">
              <div className="bg-primary/10 text-primary text-xs font-medium px-3 py-1.5 rounded-full">
                Knowledge Center
              </div>
            </div>
          </div>
        </div>
        
        <KnowledgeBaseBrowser />
      </div>
    </DashboardLayout>
  );
};

export default DashboardRagData;
