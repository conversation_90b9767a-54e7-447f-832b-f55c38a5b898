import React from "react";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import SessionMonitor from "@/components/dashboard/transports/SessionMonitor";
import { Activity, Users, Clock } from "lucide-react";

const DashboardSessions = () => {
  return (
    <DashboardLayout>
      <div className="space-y-8 animate-fade-in">
        {/* Enhanced Page Header */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <div className="p-2.5 bg-primary/10 rounded-xl">
                  <Activity className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold tracking-tight text-foreground">
                    Session Monitor
                  </h1>
                  <p className="text-lg text-muted-foreground">
                    Monitor and manage customer sessions across all transport channels
                  </p>
                </div>
              </div>
            </div>
            {/* Live Status Indicators */}
            <div className="hidden sm:flex items-center gap-3">
              <div className="flex items-center gap-2 bg-green-500/10 text-green-600 text-sm font-medium px-3 py-1.5 rounded-full border border-green-500/20">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                Live Monitoring
              </div>
              <div className="flex items-center gap-2 bg-blue-500/10 text-blue-600 text-sm font-medium px-3 py-1.5 rounded-full border border-blue-500/20">
                <Users className="w-3 h-3" />
                Active Sessions
              </div>
            </div>
          </div>
        </div>
        
        <SessionMonitor />
      </div>
    </DashboardLayout>
  );
};

export default DashboardSessions;