import React from "react";
import { useSearchParams } from "react-router-dom";
import EmbeddableChatbot from "@/components/chatbot/EmbeddableChatbot";

const EmbedChatbot = () => {
  const [searchParams] = useSearchParams();
  const publishableKey = searchParams.get('key');

  if (!publishableKey) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center p-4">
        <div className="w-full max-w-md mx-auto bg-white rounded-lg shadow-xl border overflow-hidden">
          <div className="p-8 text-center">
            <div className="text-red-500 mb-4">
              <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h2 className="text-lg font-semibold text-gray-900 mb-2">Invalid Embed Code</h2>
            <p className="text-gray-600 text-sm">
              No publishable key provided. Please check your embed code and try again.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center p-2 sm:p-4">
      <div className="w-full max-w-md h-[90vh] sm:h-[600px]">
        <EmbeddableChatbot publishableKey={publishableKey} />
      </div>
    </div>
  );
};

export default EmbedChatbot;
