
import React from "react";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import SubscriptionOverview from "@/components/dashboard/subscription/SubscriptionOverview";
// import UsageMetrics from "@/components/dashboard/subscription/UsageMetrics";
import BillingHistory from "@/components/dashboard/subscription/BillingHistory";
// import PlanManagement from "@/components/dashboard/subscription/PlanManagement";
import { CreditCard, TrendingUp, Shield, DollarSign } from "lucide-react";

const DashboardSubscription = () => {
  return (
    <DashboardLayout>
      <div className="space-y-8 animate-fade-in">
        {/* Enhanced Page Header */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <div className="p-2.5 bg-primary/10 rounded-xl">
                  <CreditCard className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold tracking-tight text-foreground">
                    Billing & Credits
                  </h1>
                  <p className="text-lg text-muted-foreground">
                    Manage your credits, usage, and billing history
                  </p>
                </div>
              </div>
            </div>
            {/* Quick Status */}
            <div className="hidden sm:flex items-center gap-2">
              <div className="flex items-center gap-2 bg-green-500/10 text-green-600 text-sm font-medium px-3 py-1.5 rounded-full border border-green-500/20">
                <Shield className="w-3 h-3" />
                Secure Billing
              </div>
              <div className="flex items-center gap-2 bg-blue-500/10 text-blue-600 text-sm font-medium px-3 py-1.5 rounded-full border border-blue-500/20">
                <TrendingUp className="w-3 h-3" />
                Usage Tracking
              </div>
            </div>
          </div>
        </div>
        
        <SubscriptionOverview />
        {/* <UsageMetrics /> */}
        <BillingHistory />
      </div>
    </DashboardLayout>
  );
};

export default DashboardSubscription;
