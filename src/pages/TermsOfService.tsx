import React from "react";
import <PERSON>zh<PERSON><PERSON><PERSON> from "@/components/DezhNavbar";
import <PERSON><PERSON><PERSON>Footer from "@/components/DezhFooter";
import { Scale, Shield, AlertTriangle, FileText, CreditCard, Users } from "lucide-react";

const TermsOfService = () => {
  const sections = [
    {
      title: "Service Description",
      icon: FileText,
      content: [
        "Dezh AI provides AI-powered customer support automation platform",
        "Integration capabilities with various business systems and APIs",
        "Real-time chat and messaging support across multiple channels",
        "Analytics and reporting tools for support performance",
        "Customizable AI agent training and knowledge base management"
      ]
    },
    {
      title: "User Responsibilities",
      icon: Users,
      content: [
        "Provide accurate account and billing information",
        "Maintain the security of your account credentials",
        "Use the service in compliance with applicable laws",
        "Not attempt to reverse engineer or misuse our technology",
        "Report any security vulnerabilities responsibly"
      ]
    },
    {
      title: "Payment Terms",
      icon: CreditCard,
      content: [
        "Subscription fees are billed in advance on a monthly or annual basis",
        "Usage-based charges are calculated based on AI agent activity",
        "Payment is due within the billing period specified",
        "Refunds are provided according to our refund policy",
        "Price changes require 30 days advance notice"
      ]
    },
    {
      title: "Limitations & Disclaimers",
      icon: AlertTriangle,
      content: [
        "Service availability is subject to maintenance and technical issues",
        "AI responses may not always be 100% accurate",
        "We are not liable for third-party integrations or data loss",
        "Maximum liability is limited to the amount paid in the last 12 months",
        "Some features may be in beta and subject to changes"
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <DezhNavbar />
      
      {/* Hero Section */}
      <section className="pt-24 pb-16 relative overflow-hidden hero-gradient">
        <div className="absolute top-20 right-10 w-80 h-80 bg-primary/8 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 left-10 w-96 h-96 bg-accent/5 rounded-full blur-3xl animate-pulse-glow"></div>
        
        <div className="section-container relative z-10">
          <div className="max-w-4xl mx-auto">
            <div className="inline-flex items-center px-6 py-3 glassmorphism glassmorphism-hover rounded-full mb-8 opacity-0 animate-fade-in">
              <Scale className="w-4 h-4 mr-3 text-primary" />
              <span className="text-sm font-medium text-foreground">Legal Terms</span>
            </div>
            
            <h1 className="section-title opacity-0 animate-fade-in stagger-1">
              Terms of 
              <span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent"> Service </span>
            </h1>
            
            <p className="section-subtitle opacity-0 animate-fade-in stagger-2">
              Please read these terms carefully before using Dezh AI. These terms govern your use 
              of our AI-powered customer support platform.
            </p>
            
            <div className="text-sm text-muted-foreground mt-6 opacity-0 animate-fade-in stagger-3">
              Last updated: January 2, 2025
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16">
        <div className="section-container">
          <div className="max-w-4xl mx-auto">
            {/* Acceptance */}
            <div className="card-modern mb-12 opacity-0 animate-fade-in">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center">
                    <Shield className="w-6 h-6 text-primary" />
                  </div>
                </div>
                <div>
                  <h2 className="text-2xl font-semibold text-foreground mb-4">Acceptance of Terms</h2>
                  <p className="text-muted-foreground leading-relaxed">
                    By accessing or using Dezh AI services, you agree to be bound by these Terms of Service 
                    and all applicable laws and regulations. If you do not agree with any of these terms, 
                    you are prohibited from using or accessing this service.
                  </p>
                </div>
              </div>
            </div>

            {/* Main Sections */}
            <div className="space-y-8">
              {sections.map((section, index) => (
                <div key={index} className="card-modern opacity-0 animate-fade-in glassmorphism-hover" style={{ animationDelay: `${index * 0.1}s` }}>
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center">
                        <section.icon className="w-6 h-6 text-primary" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-foreground mb-4">{section.title}</h3>
                      <ul className="space-y-2">
                        {section.content.map((item, itemIndex) => (
                          <li key={itemIndex} className="flex items-start space-x-3">
                            <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                            <span className="text-muted-foreground leading-relaxed">{item}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Additional Important Sections */}
            <div className="space-y-8 mt-12">
              <div className="card-modern opacity-0 animate-fade-in" style={{ animationDelay: '0.4s' }}>
                <h3 className="text-xl font-semibold text-foreground mb-4">Intellectual Property</h3>
                <div className="space-y-3 text-muted-foreground">
                  <p>
                    The Dezh AI platform, including all software, algorithms, and content, is protected by 
                    intellectual property laws. You may not copy, modify, distribute, or reverse engineer 
                    any part of our service.
                  </p>
                  <p>
                    You retain ownership of your data and content. By using our service, you grant us a 
                    license to process your data solely for providing and improving our services.
                  </p>
                </div>
              </div>

              <div className="card-modern opacity-0 animate-fade-in" style={{ animationDelay: '0.6s' }}>
                <h3 className="text-xl font-semibold text-foreground mb-4">Termination</h3>
                <div className="space-y-3 text-muted-foreground">
                  <p>
                    You may terminate your account at any time by contacting our support team. We may 
                    terminate or suspend your account immediately if you violate these terms.
                  </p>
                  <p>
                    Upon termination, your right to use the service will stop immediately. We will 
                    provide you with a reasonable opportunity to export your data.
                  </p>
                </div>
              </div>

              <div className="card-modern opacity-0 animate-fade-in" style={{ animationDelay: '0.8s' }}>
                <h3 className="text-xl font-semibold text-foreground mb-4">Governing Law</h3>
                <p className="text-muted-foreground leading-relaxed">
                  These terms are governed by the laws of [Your Jurisdiction]. Any disputes will be 
                  resolved through binding arbitration in accordance with the rules of the American 
                  Arbitration Association.
                </p>
              </div>
            </div>

            {/* Contact Information */}
            <div className="card-modern mt-12 bg-primary/5 border-primary/20 opacity-0 animate-fade-in" style={{ animationDelay: '1s' }}>
              <h3 className="text-xl font-semibold text-foreground mb-4">Questions About These Terms</h3>
              <div className="space-y-2 text-muted-foreground">
                <p>If you have any questions about these Terms of Service, please contact us:</p>
                <p>Email: <EMAIL></p>
                <p>Address: 123 AI Street, Tech City, TC 12345</p>
                <p>Phone: +****************</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <DezhFooter />
    </div>
  );
};

export default TermsOfService;