
import React, { useEffect, useState } from "react";
import Dezh<PERSON>avbar from "@/components/DezhNavbar";
import DezhHero from "@/components/DezhHero";
import DezhTrustedCompanies from "@/components/DezhTrustedCompanies";
import DezhModernFeatures from "@/components/DezhModernFeatures";
import DezhModernProcess from "@/components/DezhModernProcess";

import DezhCustomers from "@/components/DezhCustomers";
import DezhTransformSection from "@/components/DezhTransformSection";
import DezhFooter from "@/components/DezhFooter";
import { MessageCircle, X } from "lucide-react";

const DezhIndex = () => {
  const [showChatFocus, setShowChatFocus] = useState(false);
  const [hasTriggered, setHasTriggered] = useState(false);

  useEffect(() => {
    // Initialize intersection observer for scroll animations
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in");
            observer.unobserve(entry.target);
          }
        });
      },
      { 
        threshold: 0.1,
        rootMargin: "50px"
      }
    );
    
    const elements = document.querySelectorAll(".animate-on-scroll");
    elements.forEach((el) => observer.observe(el));

    // Scroll detection for "Deploy in 3 simple steps" section
    const deployStepsObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !hasTriggered) {
            setShowChatFocus(true);
            setHasTriggered(true);
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
              setShowChatFocus(false);
            }, 5000);
          }
        });
      },
      { 
        threshold: 0.3,
        rootMargin: "-100px"
      }
    );

    // Wait for component to mount and then observe the section
    setTimeout(() => {
      const deploySection = document.getElementById('how-it-works');
      if (deploySection) {
        deployStepsObserver.observe(deploySection);
      }
    }, 100);
    
    return () => {
      elements.forEach((el) => observer.unobserve(el));
      deployStepsObserver.disconnect();
    };
  }, [hasTriggered]);

  return (
    <div className="min-h-screen bg-background">
      <DezhNavbar />
      <main>
        <DezhHero />
        <DezhTrustedCompanies />
        <DezhModernFeatures />
        <DezhModernProcess />
        
        <DezhCustomers />
        <DezhTransformSection />
      </main>
      <DezhFooter />
      
      {/* Chat Focus Overlay */}
      {showChatFocus && (
        <div className="fixed inset-0 z-[60] pointer-events-none">
          {/* Blur overlay */}
          <div className="absolute inset-0 bg-black/50 backdrop-blur-md animate-fade-in"></div>
          
          {/* Spotlight effect on chat button */}
          <div 
            className="absolute bottom-6 right-6 w-16 h-16 rounded-full animate-pulse"
            style={{
              boxShadow: '0 0 0 4px rgba(255, 72, 0, 0.3), 0 0 0 8px rgba(255, 72, 0, 0.2), 0 0 40px 20px rgba(255, 72, 0, 0.1)'
            }}
          ></div>
        </div>
      )}
    </div>
  );
};

export default DezhIndex;
