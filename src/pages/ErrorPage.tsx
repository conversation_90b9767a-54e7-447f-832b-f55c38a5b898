import { useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { AlertTriangle, Home, RefreshCw, MessageSquare } from "lucide-react";
import { Button } from "@/components/ui/button";

interface ErrorPageProps {
  error?: Error;
  errorInfo?: React.ErrorInfo;
  onRetry?: () => void;
}

const ErrorPage = ({ error, errorInfo, onRetry }: ErrorPageProps) => {
  useEffect(() => {
    console.error("Application Error:", error);
    console.error("Error Info:", errorInfo);
  }, [error, errorInfo]);

  const handleRefresh = () => {
    if (onRetry) {
      onRetry();
    } else {
      window.location.reload();
    }
  };

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-20 w-64 h-64 bg-destructive rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-muted rounded-full blur-3xl animate-float"></div>
      </div>
      
      <div className="max-w-lg text-center space-y-8 relative z-10 animate-fade-in">
        {/* Enhanced Error Icon */}
        <div className="relative">
          <div className="w-32 h-32 mx-auto bg-destructive/10 rounded-full flex items-center justify-center ring-4 ring-destructive/20 animate-pulse-glow">
            <AlertTriangle className="w-16 h-16 text-destructive animate-pulse" />
          </div>
          <div className="absolute -top-2 -right-2 w-8 h-8 bg-destructive rounded-full flex items-center justify-center animate-bounce shadow-lg">
            <span className="text-sm text-destructive-foreground font-bold">!</span>
          </div>
          {/* Floating particles */}
          <div className="absolute top-0 left-0 w-2 h-2 bg-destructive/30 rounded-full animate-float"></div>
          <div className="absolute bottom-0 right-0 w-3 h-3 bg-destructive/20 rounded-full animate-bounce" style={{ animationDelay: '0.5s' }}></div>
        </div>

        {/* Enhanced Error Content */}
        <div className="space-y-6">
          <div className="space-y-2">
            <h1 className="text-5xl font-bold text-destructive tracking-tight">Oops!</h1>
            <h2 className="text-2xl font-semibold text-foreground">Something went wrong</h2>
          </div>
          <div className="space-y-4">
            <p className="text-lg text-muted-foreground max-w-md mx-auto leading-relaxed">
              Our AI agent encountered an unexpected error. Don't worry, our support team has been notified and we're working on a fix.
            </p>
            <div className="inline-flex items-center gap-2 bg-muted/50 text-muted-foreground text-sm px-4 py-2 rounded-full">
              <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
              Automatically reported to our team
            </div>
          </div>
          
          {error && (
            <div className="text-sm text-muted-foreground/70 font-mono bg-muted/50 rounded-xl p-4 border border-destructive/20 max-w-full overflow-auto shadow-inner">
              <div className="text-destructive font-semibold mb-2 flex items-center gap-2">
                <AlertTriangle className="w-4 h-4" />
                Error Details:
              </div>
              <div className="break-all text-foreground/80 bg-background/50 p-2 rounded border">{error.message}</div>
            </div>
          )}
        </div>

        {/* Enhanced Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button 
            onClick={handleRefresh} 
            variant="default" 
            size="lg" 
            className="gap-2 bg-primary hover:bg-primary/90 shadow-lg hover:shadow-xl transition-all duration-200 hover:-translate-y-0.5"
          >
            <RefreshCw className="w-4 h-4" />
            Try Again
          </Button>
          
          <Button 
            asChild 
            variant="outline" 
            size="lg" 
            className="gap-2 hover:bg-muted/50 shadow-sm hover:shadow-md transition-all duration-200"
          >
            <Link to="/">
              <Home className="w-4 h-4" />
              Go Home
            </Link>
          </Button>
        </div>

        {/* Enhanced Support Options */}
        <div className="pt-8 border-t border-border/50">
          <div className="space-y-4">
            <p className="text-base font-medium text-muted-foreground">Still having trouble?</p>
            <p className="text-sm text-muted-foreground/80">We're here to help you get back on track</p>
          </div>
          <div className="flex flex-wrap gap-3 justify-center mt-6">
            <Button 
              asChild 
              variant="ghost" 
              size="sm" 
              className="gap-2 hover:bg-primary/10 hover:text-primary transition-colors"
            >
              <Link to="/support">
                <MessageSquare className="w-4 h-4" />
                Contact Support
              </Link>
            </Button>
            <Button 
              asChild 
              variant="ghost" 
              size="sm"
              className="gap-2 hover:bg-muted/50 transition-colors"
            >
              <Link to="/dashboard">
                <Home className="w-4 h-4" />
                Dashboard
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ErrorPage;