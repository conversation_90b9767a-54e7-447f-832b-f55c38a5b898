import React, { useState, useEffect, useRef } from "react";
import { useSearchParams } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Send, Bot, User, MessageCircle, Mic, MicOff, Phone, PhoneOff } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface Message {
  id: string;
  content: string;
  isBot: boolean;
  timestamp: Date;
}

const CustomerSupportChat = () => {
  const [searchParams] = useSearchParams();
  const businessId = searchParams.get('id');
  const { toast } = useToast();
  
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: "Hello! I'm your AI support assistant. How can I help you today?",
      isBot: true,
      timestamp: new Date()
    }
  ]);
  
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isVoiceMode, setIsVoiceMode] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (!businessId) {
      toast({
        title: "Missing Business ID",
        description: "No business ID provided in the URL parameters.",
        variant: "destructive"
      });
    }
  }, [businessId, toast]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputMessage,
      isBot: false,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsTyping(true);

    // Simulate AI response
    setTimeout(() => {
      const botResponse: Message = {
        id: (Date.now() + 1).toString(),
        content: "Thank you for your message. I'm here to help you with any questions or issues you might have. Could you please provide more details about what you need assistance with?",
        isBot: true,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, botResponse]);
      setIsTyping(false);
    }, 1500);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const startVoiceCall = async () => {
    try {
      // Request microphone permission
      await navigator.mediaDevices.getUserMedia({ audio: true });
      setIsVoiceMode(true);
      
      toast({
        title: "Voice Call Started",
        description: "You can now speak with the AI agent",
      });

      // Simulate voice interaction states
      setTimeout(() => setIsSpeaking(true), 2000);
      setTimeout(() => setIsSpeaking(false), 5000);
      
    } catch (error) {
      console.error('Failed to access microphone:', error);
      toast({
        title: "Microphone Access Denied",
        description: "Please allow microphone access to use voice features",
        variant: "destructive"
      });
    }
  };

  const endVoiceCall = () => {
    setIsVoiceMode(false);
    setIsSpeaking(false);
    toast({
      title: "Voice Call Ended",
      description: "Voice conversation has been terminated",
    });
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5 flex items-center justify-center p-4">
      <Card className="w-full max-w-4xl h-[90vh] flex flex-col">
        <CardHeader className="border-b bg-gradient-to-r from-primary/10 to-primary/5">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-full">
                <MessageCircle className="h-6 w-6 text-primary" />
              </div>
              <div>
                <CardTitle className="text-xl">Customer Support</CardTitle>
                <p className="text-sm text-muted-foreground">
                  {businessId ? `Business ID: ${businessId}` : 'General Support'}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              {/* Voice Call Controls */}
              <div className="flex items-center gap-2">
                {!isVoiceMode ? (
                  <Button
                    onClick={startVoiceCall}
                    variant="outline"
                    size="sm"
                    className="gap-2"
                  >
                    <Phone className="h-4 w-4" />
                    Voice Call
                  </Button>
                ) : (
                  <Button
                    onClick={endVoiceCall}
                    variant="destructive"
                    size="sm"
                    className="gap-2"
                  >
                    <PhoneOff className="h-4 w-4" />
                    End Call
                  </Button>
                )}
                
                {isVoiceMode && (
                  <div className="flex items-center gap-2">
                    <div className="flex items-center gap-1 px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      {isSpeaking ? 'AI Speaking' : 'Listening'}
                    </div>
                    {isSpeaking && <Mic className="h-4 w-4 text-green-600 animate-pulse" />}
                  </div>
                )}
              </div>
              
              <Badge variant="secondary" className="bg-green-100 text-green-700">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                Online
              </Badge>
            </div>
          </div>
        </CardHeader>

        <CardContent className="flex-1 flex flex-col p-0">
          <div className="flex-1 overflow-y-auto p-6 space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex items-start gap-3 ${
                  message.isBot ? 'justify-start' : 'justify-end'
                }`}
              >
                {message.isBot && (
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="bg-primary/10">
                      <Bot className="h-4 w-4 text-primary" />
                    </AvatarFallback>
                  </Avatar>
                )}
                
                <div className={`max-w-[70%] ${message.isBot ? 'order-2' : 'order-1'}`}>
                  <div
                    className={`rounded-lg px-4 py-3 ${
                      message.isBot
                        ? 'bg-muted text-foreground'
                        : 'bg-primary text-primary-foreground'
                    }`}
                  >
                    <p className="text-sm">{message.content}</p>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1 px-1">
                    {formatTime(message.timestamp)}
                  </p>
                </div>

                {!message.isBot && (
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="bg-secondary">
                      <User className="h-4 w-4" />
                    </AvatarFallback>
                  </Avatar>
                )}
              </div>
            ))}

            {isTyping && (
              <div className="flex items-start gap-3 justify-start">
                <Avatar className="h-8 w-8">
                  <AvatarFallback className="bg-primary/10">
                    <Bot className="h-4 w-4 text-primary" />
                  </AvatarFallback>
                </Avatar>
                <div className="bg-muted rounded-lg px-4 py-3">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-primary/60 rounded-full animate-pulse"></div>
                    <div className="w-2 h-2 bg-primary/60 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                    <div className="w-2 h-2 bg-primary/60 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>

          <div className="border-t p-4">
            {!isVoiceMode ? (
              <div className="flex gap-2">
                <Input
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Type your message..."
                  className="flex-1"
                  disabled={isTyping}
                />
                <Button 
                  onClick={handleSendMessage}
                  disabled={!inputMessage.trim() || isTyping}
                  size="sm"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            ) : (
              <div className="text-center py-4">
                <div className="flex items-center justify-center gap-3 mb-2">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                    {isSpeaking ? (
                      <MicOff className="h-6 w-6 text-primary animate-pulse" />
                    ) : (
                      <Mic className="h-6 w-6 text-primary" />
                    )}
                  </div>
                </div>
                <p className="text-sm text-muted-foreground">
                  {isSpeaking ? 'AI is speaking...' : 'Speak now to chat with AI'}
                </p>
                <Button
                  onClick={endVoiceCall}
                  variant="destructive"
                  size="sm"
                  className="mt-3 gap-2"
                >
                  <PhoneOff className="h-4 w-4" />
                  End Voice Call
                </Button>
              </div>
            )}
            <p className="text-xs text-muted-foreground mt-2 text-center">
              {isVoiceMode 
                ? 'Voice conversation active - WebSocket integration coming soon'
                : 'This is a demo support chat. Your messages are not stored.'
              }
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CustomerSupportChat;