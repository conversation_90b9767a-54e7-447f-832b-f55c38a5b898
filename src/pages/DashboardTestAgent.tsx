import React, { useEffect, useState } from "react";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Plus,
  MessageCircle,
  X,
  Send,
  Bot,
  User,
  Circle,
  Clock,
  CheckCircle,
  Loader2,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";
import { useMutation, useQuery } from "@tanstack/react-query";
import { apiService } from "@/services/api";

interface ChatSession {
  id: string;
  status: "OPEN" | "CLOSED" | "HITL";
  externalUserId: string;
  userId: string;
  transportId: string;
  createdAt: string;
  updatedAt?: string;
  messages: {
    id: string;
    createdAt: string;
    updatedAt?: string;
    content: string;
    role: "user" | "assistant";
    sessionId: string;
  }[];
}

export interface Message {
  id: string;
  content: string;
  role: "user" | "assistant";
  sessionId: string;
  createdAt: string;
}

const DashboardTestAgent = () => {
  const { toast } = useToast();

  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const {
    data: sessionsResponse,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["sessions"],
    queryFn: () => apiService.getTestAgent("join=messages&sort=createdAt:DESC"),
  });

  const addConversationMutation = useMutation<void, Error>({
    mutationFn: () => apiService.createConversationWithTestAgent(),

    onSuccess: (res: any) => {
      toast({
        title: "New session created",
        description: "Started a new conversation with your agent.",
      });
      setCurrentSessionId(res.data.id);
      refetch();
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to create new conversation.",
        variant: "destructive",
      });
    },
  });

  const deleteSessionTestAgentMutation = useMutation({
    mutationFn: (sessionId: string) =>
      apiService.deleteSessionsTestAgent(sessionId),
    onSuccess: () => {
      toast({
        title: "Session closed",
        description: "The conversation has been closed.",
      });
      refetch();
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to disable transport.",
        variant: "destructive",
      });
    },
  });
  const sendMessageMutation = useMutation<
    any,
    Error,
    { form: { content: string }; id: string }
  >({
    mutationFn: ({ form, id }) => apiService.sendMessageToTestAgent(form, id),

    onSuccess: (res) => {
      setNewMessage("");
      setMessages(res);
      setSessions((prev) =>
        prev.map((session) =>
          session.id === currentSessionId
            ? { ...session, messages: res }
            : session
        )
      );
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to Send message!.",
        variant: "destructive",
      });
    },
  });
  const { isPending: isSending } = sendMessageMutation;
  useEffect(() => {
    const session = sessionsResponse?.data || [];
    setSessions(session as any);
  }, [sessionsResponse]);

  const [currentSessionId, setCurrentSessionId] = useState<string>("1");
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState("");

  const currentSession = sessions?.find((s) => s.id === currentSessionId);

  useEffect(() => {
    setMessages(currentSession?.messages);
  }, [currentSessionId]);

  const createNewSession = () => {
    addConversationMutation.mutate();
    toast({
      title: "New session created",
      description: "Started a new conversation with your agent.",
    });
  };

  const closeSession = (sessionId: string) => {
    deleteSessionTestAgentMutation.mutate(currentSessionId);

    // If closing current session, switch to another active one
    // if (sessionId === currentSessionId) {
    //   const nextSession = sessions?.find(
    //     (s) => s.id !== sessionId && s.status === "OPEN"
    //   );
    //   if (nextSession) {
    //     setCurrentSessionId(nextSession.id);
    //   }
    // }
  };

  const sendMessage = () => {
    if (!newMessage.trim() || isSending) return;
    sendMessageMutation.mutate({
      form: { content: newMessage },
      id: currentSession.id,
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "OPEN":
        return <Circle className="w-3 h-3 fill-green-500 text-green-500" />;
      case "HITL":
        return <Clock className="w-3 h-3 text-yellow-500" />;
      case "CLOSED":
        return <CheckCircle className="w-3 h-3 text-muted-foreground" />;
      default:
        return <Circle className="w-3 h-3 text-muted-foreground" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "OPEN":
        return "bg-green-500/10 text-green-500 border-green-500/20";
      case "HITL":
        return "bg-yellow-500/10 text-yellow-500 border-yellow-500/20";
      case "CLOSED":
        return "bg-muted text-muted-foreground border-muted";
      default:
        return "bg-muted text-muted-foreground border-muted";
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6 animate-fade-in">
        {/* Enhanced Page Header */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <div className="p-2.5 bg-primary/10 rounded-xl">
                  <Bot className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold tracking-tight text-foreground">
                    Test Agent
                  </h1>
                  <p className="text-lg text-muted-foreground">
                    Test your AI agent in real-time conversations
                  </p>
                </div>
              </div>
            </div>
            {/* Session Stats */}
            <div className="hidden sm:flex items-center gap-2">
              <div className="flex items-center gap-2 bg-green-500/10 text-green-600 text-sm font-medium px-3 py-1.5 rounded-full border border-green-500/20">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                Agent Online
              </div>
              <div className="flex items-center gap-2 bg-blue-500/10 text-blue-600 text-sm font-medium px-3 py-1.5 rounded-full border border-blue-500/20">
                <span className="font-semibold">{sessions?.length || 0}</span>
                Active Sessions
              </div>
            </div>
          </div>
        </div>
        
        <div className="flex h-[calc(100vh-16rem)] bg-background rounded-xl border border-border overflow-hidden shadow-sm">
          {/* Sessions Sidebar */}
          <div className="w-80 border-r border-border flex flex-col">
          <div className="p-4 border-b border-border">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold">Test Sessions</h2>
              <Button
                onClick={createNewSession}
                size="sm"
                className="bg-primary text-primary-foreground hover:bg-primary/90"
              >
                <Plus className="w-4 h-4 mr-2" />
                New Chat
              </Button>
            </div>
          </div>

          <ScrollArea className="flex-1">
            <div className="p-2 space-y-2">
              {sessions?.map((session) => {
                const lastMessage =
                  session?.messages?.[session.messages?.length - 1];
                return (
                  <Card
                    key={session.id}
                    className={cn(
                      "p-3 cursor-pointer transition-colors hover:bg-muted/50",
                      currentSessionId === session.id &&
                        "bg-muted border-primary/50"
                    )}
                    onClick={() => setCurrentSessionId(session.id)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <MessageCircle className="w-4 h-4 text-muted-foreground" />
                          <span className="font-medium text-sm truncate">
                            {session.id.slice(0, 4) ?? "-"}
                          </span>
                        </div>
                        <p className="text-xs text-muted-foreground truncate mb-2">
                          {lastMessage?.content}
                        </p>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {getStatusIcon(session.status)}
                            <Badge
                              variant="outline"
                              className={cn(
                                "text-xs",
                                getStatusColor(session.status)
                              )}
                            >
                              {session.status}
                            </Badge>
                          </div>
                          <span className="text-xs text-muted-foreground">
                            {session?.messages?.length} msgs
                          </span>
                        </div>
                      </div>
                      {session.status === "OPEN" && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            closeSession(session.id);
                          }}
                          className="ml-2 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 hover:bg-destructive/10 hover:text-destructive"
                        >
                          <X className="w-3 h-3" />
                        </Button>
                      )}
                    </div>
                  </Card>
                );
              })}
            </div>
          </ScrollArea>
        </div>

        {/* Chat Area */}
        <div className="flex-1 flex flex-col">
          {currentSession ? (
            <>
              {/* Chat Header */}
              <div className="p-4 border-b border-border">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(currentSession.status)}
                      <h3 className="font-semibold">
                        {currentSession.id.slice(0, 4) ?? "-"}
                      </h3>
                    </div>
                    <Badge
                      variant="outline"
                      className={getStatusColor(currentSession.status)}
                    >
                      {currentSession.status}
                    </Badge>
                  </div>
                  {currentSession.status === "OPEN" && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => closeSession(currentSession.id)}
                      className="text-destructive hover:bg-destructive/10"
                    >
                      <X className="w-4 h-4 mr-2" />
                      Close Session
                    </Button>
                  )}
                </div>
              </div>

              {/* Messages */}
              <ScrollArea className="flex-1 p-4">
                <div className="space-y-6 max-w-3xl mx-auto">
                  {messages

                    ?.sort((a, b) => {
                      const timeDiff =
                        new Date(a.createdAt).getTime() -
                        new Date(b.createdAt).getTime();

                      if (timeDiff !== 0) {
                        return timeDiff;
                      }

                      if (a.role === "user" && b.role !== "user") return -1;
                      if (a.role !== "user" && b.role === "user") return 1;

                      return 0;
                    })

                    .map((message) => (
                      <div
                        key={message.id}
                        className={cn(
                          "flex gap-3 ",
                          message.role === "user"
                            ? "flex-row-reverse"
                            : "flex-row"
                        )}
                      >
                        <div className="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0">
                          {message.role === "assistant" ? (
                            <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                              <Bot className="w-4 h-4 text-primary" />
                            </div>
                          ) : (
                            <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center">
                              <User className="w-4 h-4 text-muted-foreground" />
                            </div>
                          )}
                        </div>
                        <div
                          className={cn(
                            "flex-1 max-w-[80%]",
                            message.role === "user" ? "text-right" : "text-left"
                          )}
                        >
                          <div
                            className={cn(
                              "p-3 rounded-lg max-w-fit",
                              message.role === "user"
                                ? "bg-primary text-primary-foreground ml-auto"
                                : "bg-muted"
                            )}
                          >
                            <p className="text-sm leading-relaxed">
                              {message.content}
                            </p>
                          </div>
                          <p className="text-xs text-muted-foreground mt-1">
                            {new Date(message.createdAt).toLocaleTimeString()}
                          </p>
                        </div>
                      </div>
                    ))}
                </div>
              </ScrollArea>

              {/* Message Input */}
              {currentSession.status === "OPEN" && (
                <div className="p-4 border-t border-border">
                  <div className="flex gap-2 max-w-3xl mx-auto">
                    <Input
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      placeholder="Type your message..."
                      onKeyPress={(e) => e.key === "Enter" && sendMessage()}
                      className="flex-1"
                    />
                    <Button
                      onClick={sendMessage}
                      disabled={!newMessage.trim() || isSending}
                      className="bg-primary text-primary-foreground hover:bg-primary/90"
                    >
                      {isSending ? (
                        <Loader2 className="w-6 h-6 animate-spin" />
                      ) : (
                        <Send className="w-4 h-4" />
                      )}
                    </Button>
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <Bot className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  No session selected
                </h3>
                <p className="text-muted-foreground mb-4">
                  Select a session from the sidebar or create a new one to start
                  testing your agent.
                </p>
                <Button onClick={createNewSession}>
                  <Plus className="w-4 h-4 mr-2" />
                  Create New Session
                </Button>
              </div>
            </div>
          )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default DashboardTestAgent;
