
import React from "react";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import TransportsOverview from "@/components/dashboard/transports/TransportsOverview";
import { Radio, Zap, Globe } from "lucide-react";

const DashboardTransports = () => {
  return (
    <DashboardLayout>
      <div className="space-y-8 animate-fade-in">
        {/* Enhanced Page Header */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <div className="p-2.5 bg-primary/10 rounded-xl">
                  <Radio className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold tracking-tight text-foreground">
                    Transport Management
                  </h1>
                  <p className="text-lg text-muted-foreground">
                    Configure and manage your communication channels
                  </p>
                </div>
              </div>
            </div>
            {/* Transport Status */}
            <div className="hidden sm:flex items-center gap-2">
              <div className="flex items-center gap-2 bg-green-500/10 text-green-600 text-sm font-medium px-3 py-1.5 rounded-full border border-green-500/20">
                <Zap className="w-3 h-3" />
                Active Channels
              </div>
              <div className="flex items-center gap-2 bg-blue-500/10 text-blue-600 text-sm font-medium px-3 py-1.5 rounded-full border border-blue-500/20">
                <Globe className="w-3 h-3" />
                Connected
              </div>
            </div>
          </div>
        </div>
        
        <TransportsOverview />
      </div>
    </DashboardLayout>
  );
};

export default DashboardTransports;
