
import React from "react";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import CompanyInformation from "@/components/dashboard/profile/CompanyInformation";
import { Building2, Shield, User } from "lucide-react";

const DashboardProfile = () => {
  return (
    <DashboardLayout>
      <div className="space-y-8 animate-fade-in">
        {/* Enhanced Page Header */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <div className="p-2.5 bg-primary/10 rounded-xl">
                  <Building2 className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold tracking-tight text-foreground">
                    Business Profile
                  </h1>
                  <p className="text-lg text-muted-foreground">
                    Manage your company information and account settings
                  </p>
                </div>
              </div>
            </div>
            {/* Quick Navigation */}
            <div className="hidden sm:flex items-center gap-2">
              <div className="flex items-center gap-2 bg-muted/50 text-muted-foreground text-sm font-medium px-3 py-1.5 rounded-full">
                <User className="w-3 h-3" />
                Profile Settings
              </div>
              <div className="flex items-center gap-2 bg-green-500/10 text-green-600 text-sm font-medium px-3 py-1.5 rounded-full">
                <Shield className="w-3 h-3" />
                Secure
              </div>
            </div>
          </div>
        </div>
        
        <CompanyInformation />
      </div>
    </DashboardLayout>
  );
};

export default DashboardProfile;
