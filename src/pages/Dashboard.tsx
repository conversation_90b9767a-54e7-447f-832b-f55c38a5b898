
import React from "react";
import { Navigate } from "react-router-dom";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import DashboardOverview from "@/components/dashboard/DashboardOverview";
import { BarChart3, Sparkles } from "lucide-react";

const Dashboard = () => {
  // For now, we'll assume user is authenticated. In a real app, this would check auth state
  const isAuthenticated = true; // This would come from your auth context

  if (!isAuthenticated) {
    return <Navigate to="/book-demo" replace />;
  }

  return (
    <DashboardLayout>
      <div className="space-y-8 animate-fade-in">
        {/* Enhanced Page Header */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <div className="p-2.5 bg-primary/10 rounded-xl">
                  <BarChart3 className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold tracking-tight text-foreground">
                    Dashboard Overview
                  </h1>
                  <p className="text-lg text-muted-foreground">
                    Monitor your AI agent performance and business metrics
                  </p>
                </div>
              </div>
            </div>
            {/* Status Badge */}
            <div className="hidden sm:flex items-center gap-2">
              <div className="flex items-center gap-2 bg-green-500/10 text-green-600 text-sm font-medium px-4 py-2 rounded-full border border-green-500/20">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                System Online
              </div>
              <div className="flex items-center gap-2 bg-primary/10 text-primary text-sm font-medium px-4 py-2 rounded-full border border-primary/20">
                <Sparkles className="w-4 h-4" />
                AI Active
              </div>
            </div>
          </div>
        </div>
        
        <DashboardOverview />
      </div>
    </DashboardLayout>
  );
};

export default Dashboard;
