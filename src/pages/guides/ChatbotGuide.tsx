import React from "react";
import { <PERSON> } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Globe,
  ArrowRight,
  CheckCircle,
  Clock,
  Settings,
  Copy,
  Palette,
  Code,
  Smartphone,
  Monitor,
  Info,
  Zap,
  Eye,
  AlertTriangle,
} from "lucide-react";

const ChatbotGuide = () => {
  const steps = [
    {
      title: "Generate Publishable Key",
      description: "Create your chatbot and get the embed code",
      icon: Zap,
      details: [
        "Go to Dashboard > Transports",
        "Find the Website Chatbot card",
        "Click 'Setup Website Chatbot'",
        "Your publishable key will be generated automatically",
        "The chatbot is now ready with default settings",
      ],
    },
    {
      title: "Customize Appearance",
      description: "Style your chatbot to match your brand",
      icon: Palette,
      details: [
        "Click 'Customize' to open the configuration panel",
        "Set your business name and agent name",
        "Choose primary and secondary colors",
        "Select chat bubble style (rounded, square, or pill)",
        "Upload a custom avatar (optional)",
        "Preview changes in real-time",
      ],
    },
    {
      title: "Get Embed Code",
      description: "Copy the script tag for your website",
      icon: Code,
      details: [
        "Click 'Copy Widget Script' to get the embed code",
        "The code includes all your customization settings",
        "Paste the script tag before the closing </body> tag",
        "The chatbot will appear automatically on your website",
        "Test the chatbot on your live website",
      ],
    },
  ];

  const features = [
    { title: "Custom Styling", description: "Match your brand colors and style", icon: Palette },
    { title: "Mobile Responsive", description: "Works perfectly on all devices", icon: Smartphone },
    { title: "Easy Integration", description: "Single script tag installation", icon: Code },
    { title: "Real-time Chat", description: "Instant responses to visitors", icon: Zap },
  ];

  const customizationOptions = [
    { option: "Business Name", description: "Displayed in the chat header", example: "Your Company" },
    { option: "Agent Name", description: "Name of your AI assistant", example: "AI Assistant" },
    { option: "Primary Color", description: "Main theme color for buttons and accents", example: "#ff6600" },
    { option: "Secondary Color", description: "Background and secondary elements", example: "#f0f0f0" },
    { option: "Chat Bubble Style", description: "Shape of the chat toggle button", example: "rounded, square, pill" },
    { option: "Avatar URL", description: "Custom avatar image (optional)", example: "https://..." },
  ];

  const troubleshooting = [
    {
      issue: "Chatbot not appearing on website",
      solutions: [
        "Check that the script tag is placed before </body>",
        "Verify the publishable key is correct",
        "Ensure there are no JavaScript errors in console",
        "Check if the chatbot is active in dashboard",
      ],
    },
    {
      issue: "Styling not applied correctly",
      solutions: [
        "Clear browser cache and refresh",
        "Check that color values are valid hex codes",
        "Verify the script includes latest configuration",
        "Re-copy the widget script after changes",
      ],
    },
    {
      issue: "Messages not being received",
      solutions: [
        "Check Dashboard > Session Monitor for conversations",
        "Verify your knowledge base has content",
        "Test with simple questions first",
        "Contact support if issues persist",
      ],
    },
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Globe className="w-6 h-6 text-primary" />
          <h1 className="text-3xl font-bold">Website Chatbot Setup Guide</h1>
          <Badge variant="secondary">3 minutes</Badge>
        </div>
        <p className="text-lg text-muted-foreground">
          Add an AI-powered chatbot to your website that can handle customer inquiries, 
          provide support, and generate leads 24/7.
        </p>
      </div>

      {/* Overview */}
      <Alert className="glassmorphism border-blue-500/20 bg-blue-500/5">
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>What you'll need:</strong> Access to your website's HTML code and about 3 minutes. 
          The chatbot widget is fully customizable and works on any website.
        </AlertDescription>
      </Alert>

      {/* Features */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Why Add a Website Chatbot?</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {features.map((feature) => {
            const Icon = feature.icon;
            return (
              <div key={feature.title} className="flex items-start gap-3 p-4 rounded-lg bg-card/30 backdrop-blur-sm border border-border/50">
                <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Icon className="w-4 h-4 text-primary" />
                </div>
                <div>
                  <h3 className="font-medium">{feature.title}</h3>
                  <p className="text-sm text-muted-foreground">{feature.description}</p>
                </div>
              </div>
            );
          })}
        </div>
      </section>

      {/* Step-by-Step Guide */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Step-by-Step Setup</h2>
        
        <div className="space-y-6">
          {steps.map((step, index) => {
            const Icon = step.icon;
            return (
              <Card key={index} className="glassmorphism border-white/10">
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                      {index + 1}
                    </div>
                    <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                      <Icon className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{step.title}</CardTitle>
                      <CardDescription>{step.description}</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <ol className="space-y-2">
                    {step.details.map((detail, detailIndex) => (
                      <li key={detailIndex} className="flex items-start gap-3 text-sm">
                        <div className="w-5 h-5 bg-muted rounded-full flex items-center justify-center text-xs font-medium mt-0.5 flex-shrink-0">
                          {detailIndex + 1}
                        </div>
                        <span>{detail}</span>
                      </li>
                    ))}
                  </ol>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </section>

      {/* Customization Options */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Customization Options</h2>
        
        <Card className="glassmorphism border-white/10">
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-border/50">
                    <th className="text-left p-4 font-medium">Option</th>
                    <th className="text-left p-4 font-medium">Description</th>
                    <th className="text-left p-4 font-medium">Example</th>
                  </tr>
                </thead>
                <tbody>
                  {customizationOptions.map((option, index) => (
                    <tr key={index} className="border-b border-border/30 last:border-b-0">
                      <td className="p-4 font-medium">{option.option}</td>
                      <td className="p-4 text-sm text-muted-foreground">{option.description}</td>
                      <td className="p-4 text-sm font-mono bg-muted/30 rounded">{option.example}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Example Code */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Example Embed Code</h2>
        
        <Card className="glassmorphism border-white/10">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Code className="w-5 h-5" />
              Widget Script
            </CardTitle>
            <CardDescription>
              This is what your embed code will look like (with your actual values)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-muted/30 rounded-lg p-4 overflow-x-auto">
              <pre className="text-sm">
                <code>{`<script
  src="${window.location.origin}/Rokovo-widget.js"
  data-publishable-key="pk_live_your_key_here"
  data-business-name="Your Business"
  data-agent-name="AI Assistant"
  data-primary-color="#ff6600"
  data-secondary-color="#f0f0f0"
  data-chat-bubble-style="rounded"
  async>
</script>`}</code>
              </pre>
            </div>
            <div className="mt-4 flex items-center gap-2 text-sm text-muted-foreground">
              <Info className="w-4 h-4" />
              <span>Place this code before the closing &lt;/body&gt; tag in your HTML</span>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Troubleshooting */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Troubleshooting</h2>
        
        <div className="space-y-4">
          {troubleshooting.map((item, index) => (
            <Card key={index} className="glassmorphism border-white/10">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <AlertTriangle className="w-5 h-5 text-yellow-500" />
                  {item.issue}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {item.solutions.map((solution, solutionIndex) => (
                    <li key={solutionIndex} className="flex items-start gap-2 text-sm">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span>{solution}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Best Practices */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Best Practices</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card className="glassmorphism border-white/10">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="w-5 h-5 text-primary" />
                Design Tips
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Use colors that match your brand</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Choose a clear, friendly agent name</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Test on both desktop and mobile</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Keep the design simple and clean</span>
                </li>
              </ul>
            </CardContent>
          </Card>

          <Card className="glassmorphism border-white/10">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="w-5 h-5 text-primary" />
                Performance Tips
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Load the script asynchronously</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Place script at the bottom of the page</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Monitor chatbot performance regularly</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Update knowledge base frequently</span>
                </li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Next Steps */}
      <section className="text-center space-y-4 pt-8 border-t border-border/50">
        <h3 className="text-lg font-semibold">Ready to add your chatbot?</h3>
        <p className="text-muted-foreground">
          Head to your dashboard to set up your website chatbot, or explore other transport options.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link to="/dashboard/transports">
            <Button>
              <Settings className="w-4 h-4 mr-2" />
              Setup Website Chatbot
            </Button>
          </Link>
          <Link to="/guides/transports">
            <Button variant="outline">
              <ArrowRight className="w-4 h-4 mr-2" />
              Other Transports
            </Button>
          </Link>
        </div>
      </section>
    </div>
  );
};

export default ChatbotGuide;
