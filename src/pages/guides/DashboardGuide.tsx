import React from "react";
import { <PERSON> } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Home,
  ArrowRight,
  Settings,
  Database,
  CreditCard,
  Building2,
  Wrench,
  Activity,
  Bot,
  Info,
  CheckCircle,
} from "lucide-react";

const DashboardGuide = () => {
  const sections = [
    {
      title: "Overview",
      description: "Main dashboard with key metrics and quick actions",
      icon: Home,
      path: "/dashboard",
      features: ["Usage statistics", "Recent activity", "Quick setup links", "System status"],
    },
    {
      title: "Billing & Credits",
      description: "Manage your subscription and monitor usage",
      icon: CreditCard,
      path: "/dashboard/subscription",
      features: ["Current plan details", "Usage tracking", "Billing history", "Plan upgrades"],
    },
    {
      title: "Business Profile",
      description: "Configure your business information and settings",
      icon: Building2,
      path: "/dashboard/profile",
      features: ["Company details", "Contact information", "Branding settings", "Account preferences"],
    },
    {
      title: "Knowledge Base",
      description: "Upload and manage your AI's knowledge content",
      icon: Database,
      path: "/dashboard/knowledge-base",
      features: ["Content upload", "Data management", "Search functionality", "Content organization"],
    },
    {
      title: "Tools Management",
      description: "Configure additional tools and integrations",
      icon: Wrench,
      path: "/dashboard/tools",
      features: ["Tool configuration", "API integrations", "Custom functions", "Automation settings"],
    },
    {
      title: "Transports",
      description: "Set up communication channels for your AI agent",
      icon: Settings,
      path: "/dashboard/transports",
      features: ["Channel setup", "Configuration management", "Status monitoring", "Integration guides"],
    },
    {
      title: "Session Monitor",
      description: "View and analyze customer conversations",
      icon: Activity,
      path: "/dashboard/sessions",
      features: ["Live conversations", "Chat history", "Performance metrics", "User analytics"],
    },
    {
      title: "Test Your Agent",
      description: "Test and interact with your AI agent directly",
      icon: Bot,
      path: "/dashboard/test-agent",
      features: ["Direct testing", "Response preview", "Configuration testing", "Debug mode"],
    },
  ];

  const quickStart = [
    {
      step: 1,
      title: "Set up your business profile",
      description: "Add your company information and branding",
      action: "Go to Business Profile",
      path: "/dashboard/profile",
    },
    {
      step: 2,
      title: "Add knowledge base content",
      description: "Upload documents, links, or Q&A pairs",
      action: "Manage Knowledge Base",
      path: "/dashboard/knowledge-base",
    },
    {
      step: 3,
      title: "Configure a transport",
      description: "Set up how customers will reach your agent",
      action: "Setup Transports",
      path: "/dashboard/transports",
    },
    {
      step: 4,
      title: "Test your agent",
      description: "Verify everything works as expected",
      action: "Test Agent",
      path: "/dashboard/test-agent",
    },
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Home className="w-6 h-6 text-primary" />
          <h1 className="text-3xl font-bold">Dashboard Overview Guide</h1>
        </div>
        <p className="text-lg text-muted-foreground">
          Get familiar with your dashboard and learn how to navigate and use all the features 
          to manage your AI agent effectively.
        </p>
      </div>

      {/* Overview */}
      <Alert className="glassmorphism border-blue-500/20 bg-blue-500/5">
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>Dashboard Navigation:</strong> Your dashboard is organized into sections that guide you 
          through the complete setup and management of your AI agent. Each section has specific tools 
          and features to help you succeed.
        </AlertDescription>
      </Alert>

      {/* Quick Start */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Quick Start Checklist</h2>
        <p className="text-muted-foreground">
          Follow these steps to get your AI agent up and running quickly.
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {quickStart.map((item) => (
            <Card key={item.step} className="glassmorphism border-white/10">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                    {item.step}
                  </div>
                  <div>
                    <CardTitle className="text-lg">{item.title}</CardTitle>
                    <CardDescription>{item.description}</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Link to={item.path}>
                  <Button variant="outline" className="w-full">
                    {item.action}
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Dashboard Sections */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Dashboard Sections</h2>
        <p className="text-muted-foreground">
          Detailed overview of each section in your dashboard and what you can accomplish there.
        </p>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {sections.map((section) => {
            const Icon = section.icon;
            return (
              <Card key={section.path} className="glassmorphism border-white/10 hover:border-white/20 transition-colors">
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                      <Icon className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{section.title}</CardTitle>
                      <CardDescription>{section.description}</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Key Features:</h4>
                    <div className="grid grid-cols-1 gap-1">
                      {section.features.map((feature, index) => (
                        <div key={index} className="text-xs flex items-center gap-2">
                          <CheckCircle className="w-3 h-3 text-green-500" />
                          {feature}
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <Link to={section.path}>
                    <Button variant="outline" className="w-full group">
                      Go to {section.title}
                      <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </section>

      {/* Tips */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Dashboard Tips</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card className="glassmorphism border-white/10">
            <CardHeader>
              <CardTitle>Navigation Tips</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Use the sidebar to quickly navigate between sections</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Bookmark frequently used pages</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Check the overview page for quick status updates</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Use the test agent page to verify changes</span>
                </li>
              </ul>
            </CardContent>
          </Card>

          <Card className="glassmorphism border-white/10">
            <CardHeader>
              <CardTitle>Best Practices</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Regularly monitor your session activity</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Keep your knowledge base updated</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Review billing and usage monthly</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Test your agent after making changes</span>
                </li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Next Steps */}
      <section className="text-center space-y-4 pt-8 border-t border-border/50">
        <h3 className="text-lg font-semibold">Ready to explore your dashboard?</h3>
        <p className="text-muted-foreground">
          Start with the quick start checklist above, or dive into any specific section you need help with.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link to="/dashboard">
            <Button>
              <Home className="w-4 h-4 mr-2" />
              Go to Dashboard
            </Button>
          </Link>
          <Link to="/guides">
            <Button variant="outline">
              <ArrowRight className="w-4 h-4 mr-2" />
              Back to Guides
            </Button>
          </Link>
        </div>
      </section>
    </div>
  );
};

export default DashboardGuide;
