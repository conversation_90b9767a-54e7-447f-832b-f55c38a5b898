import React, { useState } from "react";
import { Outlet, Link, useLocation } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Search,
  Home,
  Settings,
  Database,
  MessageSquare,
  Bot,
  Zap,
  Globe,
  BookOpen,
  ChevronRight,
  Menu,
  X,
  HelpCircle,
} from "lucide-react";
import { cn } from "@/lib/utils";

const GuideLayout = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation();

  const guideCategories = [
    {
      title: "Getting Started",
      icon: Home,
      items: [
        { title: "Dashboard Overview", path: "/guides/dashboard", icon: Home },
      ],
    },
    {
      title: "Transports",
      icon: Settings,
      items: [
        { title: "Transport Overview", path: "/guides/transports", icon: Settings },
        { title: "Telegram Bot", path: "/guides/transports/telegram", icon: MessageSquare },
        { title: "Discord Bot", path: "/guides/transports/discord", icon: Bot },
        { title: "Pure API", path: "/guides/transports/api", icon: Zap },
        { title: "Website Chatbot", path: "/guides/transports/chatbot", icon: Globe },
      ],
    },
    {
      title: "Knowledge Base",
      icon: Database,
      items: [
        { title: "Knowledge Management", path: "/guides/knowledge-base", icon: Database },
      ],
    },
  ];

  const allGuideItems = guideCategories.flatMap(category => 
    category.items.map(item => ({ ...item, category: category.title }))
  );

  const filteredItems = searchQuery
    ? allGuideItems.filter(item =>
        item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.category.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : [];

  const isActive = (path: string) => location.pathname === path;

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-background/95">
      {/* Mobile Header */}
      <div className="lg:hidden bg-card/50 backdrop-blur-sm border-b border-border/50 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <HelpCircle className="w-6 h-6 text-primary" />
            <h1 className="text-lg font-semibold">Help Center</h1>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setSidebarOpen(!sidebarOpen)}
          >
            {sidebarOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
          </Button>
        </div>
      </div>

      <div className="flex">
        {/* Sidebar */}
        <div className={cn(
          "fixed inset-y-0 left-0 z-50 w-80 bg-card/30 backdrop-blur-xl border-r border-border/50 transform transition-transform duration-300 lg:relative lg:translate-x-0",
          sidebarOpen ? "translate-x-0" : "-translate-x-full"
        )}>
          <div className="p-6">
            {/* Header */}
            <div className="hidden lg:flex items-center gap-2 mb-6">
              <HelpCircle className="w-6 h-6 text-primary" />
              <h1 className="text-xl font-semibold">Help Center</h1>
            </div>

            {/* Search */}
            <div className="relative mb-6">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Search guides..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 glassmorphism border-white/10"
              />
            </div>

            {/* Search Results */}
            {searchQuery && (
              <div className="mb-6">
                <h3 className="text-sm font-medium text-muted-foreground mb-3">
                  Search Results ({filteredItems.length})
                </h3>
                <div className="space-y-1">
                  {filteredItems.map((item) => {
                    const Icon = item.icon;
                    return (
                      <Link
                        key={item.path}
                        to={item.path}
                        onClick={() => setSidebarOpen(false)}
                        className={cn(
                          "flex items-center gap-3 px-3 py-2 rounded-lg text-sm transition-colors",
                          isActive(item.path)
                            ? "bg-primary/10 text-primary"
                            : "text-muted-foreground hover:text-foreground hover:bg-white/5"
                        )}
                      >
                        <Icon className="w-4 h-4" />
                        <div>
                          <div>{item.title}</div>
                          <div className="text-xs text-muted-foreground">{item.category}</div>
                        </div>
                      </Link>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Navigation */}
            {!searchQuery && (
              <nav className="space-y-6">
                {guideCategories.map((category) => {
                  const CategoryIcon = category.icon;
                  return (
                    <div key={category.title}>
                      <h3 className="flex items-center gap-2 text-sm font-medium text-muted-foreground mb-3">
                        <CategoryIcon className="w-4 h-4" />
                        {category.title}
                      </h3>
                      <div className="space-y-1">
                        {category.items.map((item) => {
                          const Icon = item.icon;
                          return (
                            <Link
                              key={item.path}
                              to={item.path}
                              onClick={() => setSidebarOpen(false)}
                              className={cn(
                                "flex items-center gap-3 px-3 py-2 rounded-lg text-sm transition-colors",
                                isActive(item.path)
                                  ? "bg-primary/10 text-primary"
                                  : "text-muted-foreground hover:text-foreground hover:bg-white/5"
                              )}
                            >
                              <Icon className="w-4 h-4" />
                              {item.title}
                              {isActive(item.path) && (
                                <ChevronRight className="w-3 h-3 ml-auto" />
                              )}
                            </Link>
                          );
                        })}
                      </div>
                    </div>
                  );
                })}
              </nav>
            )}

            {/* Quick Links */}
            <div className="mt-8 pt-6 border-t border-border/50">
              <h3 className="text-sm font-medium text-muted-foreground mb-3">Quick Links</h3>
              <div className="space-y-2">
                <Link
                  to="/dashboard"
                  className="flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors"
                >
                  <Home className="w-4 h-4" />
                  Back to Dashboard
                </Link>
                <Link
                  to="/support"
                  className="flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors"
                >
                  <MessageSquare className="w-4 h-4" />
                  Contact Support
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Overlay */}
        {sidebarOpen && (
          <div
            className="fixed inset-0 bg-black/50 z-40 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* Main Content */}
        <div className="flex-1 min-w-0">
          <div className="max-w-4xl mx-auto p-6 lg:p-8">
            <Outlet />
          </div>
        </div>
      </div>
    </div>
  );
};

export default GuideLayout;
