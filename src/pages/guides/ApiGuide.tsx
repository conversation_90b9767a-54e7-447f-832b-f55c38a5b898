import React from "react";
import { <PERSON> } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Zap,
  ArrowRight,
  CheckCircle,
  Settings,
  Code,
  Shield,
  Info,
  AlertTriangle,
  Copy,
  ExternalLink,
} from "lucide-react";

const ApiGuide = () => {
  const steps = [
    {
      title: "Generate API Key",
      description: "Create your Pure API transport and get credentials",
      details: [
        "Go to Dashboard > Transports",
        "Find the Pure API card",
        "Click 'Setup Pure API'",
        "Your API key will be generated automatically",
        "Copy and securely store the API key",
      ],
    },
    {
      title: "Implement API Calls",
      description: "Integrate the API into your application",
      details: [
        "Use the POST /api/v1/chat endpoint",
        "Include your API key in the Authorization header",
        "Send messages in the request body",
        "Handle responses and errors appropriately",
        "Test thoroughly before going live",
      ],
    },
  ];

  const endpoints = [
    {
      method: "POST",
      path: "/api/v1/chat",
      description: "Send a message to your AI agent",
      auth: "Required",
    },
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Zap className="w-6 h-6 text-primary" />
          <h1 className="text-3xl font-bold">Pure API Setup Guide</h1>
          <Badge variant="outline">Advanced</Badge>
        </div>
        <p className="text-lg text-muted-foreground">
          Integrate your AI agent directly into your applications using our REST API. 
          Perfect for custom integrations and advanced use cases.
        </p>
      </div>

      {/* Overview */}
      <Alert className="glassmorphism border-blue-500/20 bg-blue-500/5">
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>For Developers:</strong> This guide assumes you have programming experience 
          and can implement HTTP requests in your chosen language or framework.
        </AlertDescription>
      </Alert>

      {/* Step-by-Step Guide */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Step-by-Step Setup</h2>
        
        <div className="space-y-6">
          {steps.map((step, index) => (
            <Card key={index} className="glassmorphism border-white/10">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                    {index + 1}
                  </div>
                  <div>
                    <CardTitle className="text-lg">{step.title}</CardTitle>
                    <CardDescription>{step.description}</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <ol className="space-y-2">
                  {step.details.map((detail, detailIndex) => (
                    <li key={detailIndex} className="flex items-start gap-3 text-sm">
                      <div className="w-5 h-5 bg-muted rounded-full flex items-center justify-center text-xs font-medium mt-0.5 flex-shrink-0">
                        {detailIndex + 1}
                      </div>
                      <span>{detail}</span>
                    </li>
                  ))}
                </ol>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* API Reference */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">API Reference</h2>
        
        <Card className="glassmorphism border-white/10">
          <CardHeader>
            <CardTitle>Available Endpoints</CardTitle>
            <CardDescription>REST API endpoints for your AI agent</CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-border/50">
                    <th className="text-left p-4 font-medium">Method</th>
                    <th className="text-left p-4 font-medium">Endpoint</th>
                    <th className="text-left p-4 font-medium">Description</th>
                    <th className="text-center p-4 font-medium">Auth</th>
                  </tr>
                </thead>
                <tbody>
                  {endpoints.map((endpoint, index) => (
                    <tr key={index} className="border-b border-border/30 last:border-b-0">
                      <td className="p-4">
                        <Badge variant="outline">{endpoint.method}</Badge>
                      </td>
                      <td className="p-4 font-mono text-sm">{endpoint.path}</td>
                      <td className="p-4 text-sm">{endpoint.description}</td>
                      <td className="p-4 text-center">
                        <Badge variant={endpoint.auth === "Required" ? "default" : "secondary"}>
                          {endpoint.auth}
                        </Badge>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Code Examples */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Code Examples</h2>
        
        <div className="space-y-6">
          <Card className="glassmorphism border-white/10">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Code className="w-5 h-5" />
                JavaScript/Node.js
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-muted/30 rounded-lg p-4 overflow-x-auto">
                <pre className="text-sm">
                  <code>{`const response = await fetch('/api/v1/chat', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_API_KEY'
  },
  body: JSON.stringify({
    message: 'Hello, how can you help me?',
    sessionId: 'unique-session-id' // optional
  })
});

const data = await response.json();
console.log(data.response);`}</code>
                </pre>
              </div>
            </CardContent>
          </Card>

          <Card className="glassmorphism border-white/10">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Code className="w-5 h-5" />
                Python
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-muted/30 rounded-lg p-4 overflow-x-auto">
                <pre className="text-sm">
                  <code>{`import requests

response = requests.post('/api/v1/chat', 
  headers={
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_API_KEY'
  },
  json={
    'message': 'Hello, how can you help me?',
    'sessionId': 'unique-session-id'  # optional
  }
)

data = response.json()
print(data['response'])`}</code>
                </pre>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Security */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Security Best Practices</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Alert className="glassmorphism border-red-500/20 bg-red-500/5">
            <Shield className="h-4 w-4" />
            <AlertDescription>
              <strong>API Key Security:</strong> Never expose your API key in client-side code. 
              Always make API calls from your backend server.
            </AlertDescription>
          </Alert>

          <Alert className="glassmorphism border-yellow-500/20 bg-yellow-500/5">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Rate Limiting:</strong> Implement proper rate limiting and error handling 
              to avoid overwhelming the API.
            </AlertDescription>
          </Alert>
        </div>
      </section>

      {/* Next Steps */}
      <section className="text-center space-y-4 pt-8 border-t border-border/50">
        <h3 className="text-lg font-semibold">Ready to integrate the API?</h3>
        <p className="text-muted-foreground">
          Head to your dashboard to generate your API key, or explore other transport options.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link to="/dashboard/transports">
            <Button>
              <Settings className="w-4 h-4 mr-2" />
              Setup Pure API
            </Button>
          </Link>
          <Link to="/guides/transports">
            <Button variant="outline">
              <ArrowRight className="w-4 h-4 mr-2" />
              Other Transports
            </Button>
          </Link>
        </div>
      </section>
    </div>
  );
};

export default ApiGuide;
