import React from "react";
import { <PERSON> } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import {
  MessageSquare,
  ArrowRight,
  CheckCircle,
  Clock,
  Users,
  Settings,
  Copy,
  ExternalLink,
  AlertTriangle,
  Info,
  Zap,
  Shield,
  Bot,
} from "lucide-react";

const TelegramGuide = () => {
  const steps = [
    {
      title: "Create a Telegram Bot",
      description: "Use BotFather to create your bot and get the token",
      icon: Bot,
      details: [
        "Open Telegram and search for @BotFather",
        "Send /newbot command",
        "Choose a name for your bot (e.g., 'My Support Bot')",
        "Choose a username ending in 'bot' (e.g., 'mysupportbot')",
        "Copy the bot token provided by BotFather",
      ],
    },
    {
      title: "Configure in Dashboard",
      description: "Add your bot token to the transport settings",
      icon: Settings,
      details: [
        "Go to Dashboard > Transports",
        "Find the Telegram Bot card",
        "Click 'Setup Telegram Bot'",
        "Paste your bot token",
        "Click 'Setup Telegram Bot' to activate",
      ],
    },
    {
      title: "Test Your Bot",
      description: "Verify everything is working correctly",
      icon: CheckCircle,
      details: [
        "Find your bot on Telegram using its username",
        "Send /start command to your bot",
        "Send a test message",
        "Verify responses in Dashboard > Session Monitor",
        "Check that conversations appear correctly",
      ],
    },
  ];

  const features = [
    { title: "Instant Messaging", description: "Real-time conversations with customers", icon: Zap },
    { title: "File Sharing", description: "Support for images, documents, and media", icon: Copy },
    { title: "Bot Commands", description: "Custom commands for quick actions", icon: Bot },
    { title: "Group Support", description: "Works in groups and channels", icon: Users },
  ];

  const troubleshooting = [
    {
      issue: "Bot not responding",
      solutions: [
        "Check that the bot token is correct",
        "Verify the bot is not already used elsewhere",
        "Ensure you've sent /start to the bot first",
        "Check Dashboard > Session Monitor for errors",
      ],
    },
    {
      issue: "Token invalid error",
      solutions: [
        "Double-check the token from BotFather",
        "Make sure there are no extra spaces",
        "Generate a new token if needed",
        "Verify the bot hasn't been deleted",
      ],
    },
    {
      issue: "Messages not appearing in dashboard",
      solutions: [
        "Check your internet connection",
        "Refresh the Session Monitor page",
        "Verify the transport is active",
        "Contact support if issues persist",
      ],
    },
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <MessageSquare className="w-6 h-6 text-primary" />
          <h1 className="text-3xl font-bold">Telegram Bot Setup Guide</h1>
          <Badge variant="secondary">5 minutes</Badge>
        </div>
        <p className="text-lg text-muted-foreground">
          Connect your AI agent to Telegram and start handling customer conversations 
          through one of the world's most popular messaging platforms.
        </p>
      </div>

      {/* Overview */}
      <Alert className="glassmorphism border-blue-500/20 bg-blue-500/5">
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>What you'll need:</strong> A Telegram account and about 5 minutes. 
          This guide will walk you through creating a Telegram bot using BotFather and 
          connecting it to your AI agent.
        </AlertDescription>
      </Alert>

      {/* Features */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Why Choose Telegram?</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {features.map((feature) => {
            const Icon = feature.icon;
            return (
              <div key={feature.title} className="flex items-start gap-3 p-4 rounded-lg bg-card/30 backdrop-blur-sm border border-border/50">
                <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Icon className="w-4 h-4 text-primary" />
                </div>
                <div>
                  <h3 className="font-medium">{feature.title}</h3>
                  <p className="text-sm text-muted-foreground">{feature.description}</p>
                </div>
              </div>
            );
          })}
        </div>
      </section>

      {/* Step-by-Step Guide */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Step-by-Step Setup</h2>
        
        <div className="space-y-6">
          {steps.map((step, index) => {
            const Icon = step.icon;
            return (
              <Card key={index} className="glassmorphism border-white/10">
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                      {index + 1}
                    </div>
                    <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                      <Icon className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{step.title}</CardTitle>
                      <CardDescription>{step.description}</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <ol className="space-y-2">
                    {step.details.map((detail, detailIndex) => (
                      <li key={detailIndex} className="flex items-start gap-3 text-sm">
                        <div className="w-5 h-5 bg-muted rounded-full flex items-center justify-center text-xs font-medium mt-0.5 flex-shrink-0">
                          {detailIndex + 1}
                        </div>
                        <span>{detail}</span>
                      </li>
                    ))}
                  </ol>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </section>

      {/* Important Notes */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Important Notes</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Alert className="glassmorphism border-yellow-500/20 bg-yellow-500/5">
            <Shield className="h-4 w-4" />
            <AlertDescription>
              <strong>Security:</strong> Keep your bot token secure and never share it publicly. 
              Anyone with your token can control your bot.
            </AlertDescription>
          </Alert>

          <Alert className="glassmorphism border-blue-500/20 bg-blue-500/5">
            <Info className="h-4 w-4" />
            <AlertDescription>
              <strong>Bot Limitations:</strong> Telegram bots can't initiate conversations. 
              Users must start the conversation first by sending /start.
            </AlertDescription>
          </Alert>
        </div>
      </section>

      {/* Troubleshooting */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Troubleshooting</h2>
        
        <div className="space-y-4">
          {troubleshooting.map((item, index) => (
            <Card key={index} className="glassmorphism border-white/10">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <AlertTriangle className="w-5 h-5 text-yellow-500" />
                  {item.issue}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {item.solutions.map((solution, solutionIndex) => (
                    <li key={solutionIndex} className="flex items-start gap-2 text-sm">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span>{solution}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Best Practices */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Best Practices</h2>
        
        <Card className="glassmorphism border-white/10">
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <h3 className="font-semibold text-green-600">Do's</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Choose a clear, descriptive bot name</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Set up a bot description and about text</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Test thoroughly before going live</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Monitor conversations regularly</span>
                  </li>
                </ul>
              </div>
              
              <div className="space-y-3">
                <h3 className="font-semibold text-red-600">Don'ts</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <AlertTriangle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                    <span>Don't share your bot token publicly</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <AlertTriangle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                    <span>Don't use the same token in multiple places</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <AlertTriangle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                    <span>Don't ignore error messages</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <AlertTriangle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                    <span>Don't forget to test after changes</span>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Next Steps */}
      <section className="text-center space-y-4 pt-8 border-t border-border/50">
        <h3 className="text-lg font-semibold">Ready to set up your Telegram bot?</h3>
        <p className="text-muted-foreground">
          Head to your dashboard to configure your Telegram transport, or explore other transport options.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link to="/dashboard/transports">
            <Button>
              <Settings className="w-4 h-4 mr-2" />
              Setup Telegram Bot
            </Button>
          </Link>
          <Link to="/guides/transports">
            <Button variant="outline">
              <ArrowRight className="w-4 h-4 mr-2" />
              Other Transports
            </Button>
          </Link>
        </div>
      </section>
    </div>
  );
};

export default TelegramGuide;
