import React from "react";
import { <PERSON> } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Settings,
  MessageSquare,
  Bot,
  Zap,
  Globe,
  ArrowRight,
  CheckCircle,
  Clock,
  Users,
  Smartphone,
  Monitor,
  Code,
  Info,
} from "lucide-react";

const TransportsGuide = () => {
  const transports = [
    {
      title: "Telegram Bot",
      description: "Connect with customers through Telegram messaging",
      icon: MessageSquare,
      path: "/guides/transports/telegram",
      difficulty: "Easy",
      setupTime: "5 minutes",
      features: ["Instant messaging", "File sharing", "Bot commands", "Group support"],
      useCase: "Great for customer support and community engagement",
      badge: "Popular",
      badgeVariant: "default" as const,
    },
    {
      title: "Website Chatbot",
      description: "Embeddable chatbot widget for your website",
      icon: Globe,
      path: "/guides/transports/chatbot",
      difficulty: "Easy",
      setupTime: "3 minutes",
      features: ["Custom styling", "Mobile responsive", "Easy integration", "Real-time chat"],
      useCase: "Perfect for website visitor support and lead generation",
      badge: "Recommended",
      badgeVariant: "secondary" as const,
    },
    {
      title: "Discord Bot",
      description: "Integrate with Discord servers and communities",
      icon: Bot,
      path: "/guides/transports/discord",
      difficulty: "Medium",
      setupTime: "10 minutes",
      features: ["Server integration", "Role management", "Channel support", "Slash commands"],
      useCase: "Ideal for gaming communities and team collaboration",
      badge: "Community",
      badgeVariant: "outline" as const,
    },
    {
      title: "Pure API",
      description: "Direct API integration for custom applications",
      icon: Zap,
      path: "/guides/transports/api",
      difficulty: "Advanced",
      setupTime: "15 minutes",
      features: ["REST API", "Authentication", "Custom integration", "Webhook support"],
      useCase: "For developers building custom integrations",
      badge: "Developer",
      badgeVariant: "outline" as const,
    },
  ];

  const comparisonFeatures = [
    { feature: "Setup Difficulty", telegram: "Easy", chatbot: "Easy", discord: "Medium", api: "Advanced" },
    { feature: "Mobile Support", telegram: "Native", chatbot: "Responsive", discord: "App", api: "Custom" },
    { feature: "Customization", telegram: "Limited", chatbot: "High", discord: "Medium", api: "Full" },
    { feature: "User Base", telegram: "Global", chatbot: "Website", discord: "Gaming", api: "Custom" },
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Settings className="w-6 h-6 text-primary" />
          <h1 className="text-3xl font-bold">Transport Setup Guide</h1>
        </div>
        <p className="text-lg text-muted-foreground">
          Transports are communication channels that connect your AI agent with your customers. 
          Choose the right transport based on where your customers are and how they prefer to communicate.
        </p>
      </div>

      {/* What are Transports */}
      <Alert className="glassmorphism border-blue-500/20 bg-blue-500/5">
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>What are Transports?</strong> Transports are the bridges between your AI agent and your customers. 
          Each transport handles a different communication channel, allowing your agent to respond to customers 
          wherever they are - whether that's on Telegram, your website, Discord, or through a custom application.
        </AlertDescription>
      </Alert>

      {/* Transport Cards */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Available Transports</h2>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {transports.map((transport) => {
            const Icon = transport.icon;
            return (
              <Card key={transport.path} className="glassmorphism border-white/10 hover:border-white/20 transition-all">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                        <Icon className="w-6 h-6 text-primary" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{transport.title}</CardTitle>
                        <CardDescription>{transport.description}</CardDescription>
                      </div>
                    </div>
                    <Badge variant={transport.badgeVariant}>{transport.badge}</Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-muted-foreground" />
                      <span>{transport.setupTime}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-muted-foreground" />
                      <span>{transport.difficulty}</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Key Features:</h4>
                    <div className="grid grid-cols-2 gap-1">
                      {transport.features.map((feature, index) => (
                        <div key={index} className="text-xs flex items-center gap-1">
                          <div className="w-1 h-1 bg-primary rounded-full" />
                          {feature}
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div className="p-3 bg-muted/30 rounded-lg">
                    <p className="text-sm text-muted-foreground">{transport.useCase}</p>
                  </div>
                  
                  <Link to={transport.path}>
                    <Button className="w-full group">
                      Setup Guide
                      <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </section>

      {/* Comparison Table */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Transport Comparison</h2>
        
        <Card className="glassmorphism border-white/10">
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-border/50">
                    <th className="text-left p-4 font-medium">Feature</th>
                    <th className="text-center p-4 font-medium">
                      <div className="flex items-center justify-center gap-2">
                        <MessageSquare className="w-4 h-4" />
                        Telegram
                      </div>
                    </th>
                    <th className="text-center p-4 font-medium">
                      <div className="flex items-center justify-center gap-2">
                        <Globe className="w-4 h-4" />
                        Chatbot
                      </div>
                    </th>
                    <th className="text-center p-4 font-medium">
                      <div className="flex items-center justify-center gap-2">
                        <Bot className="w-4 h-4" />
                        Discord
                      </div>
                    </th>
                    <th className="text-center p-4 font-medium">
                      <div className="flex items-center justify-center gap-2">
                        <Zap className="w-4 h-4" />
                        API
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {comparisonFeatures.map((row, index) => (
                    <tr key={index} className="border-b border-border/30 last:border-b-0">
                      <td className="p-4 font-medium">{row.feature}</td>
                      <td className="p-4 text-center text-sm">{row.telegram}</td>
                      <td className="p-4 text-center text-sm">{row.chatbot}</td>
                      <td className="p-4 text-center text-sm">{row.discord}</td>
                      <td className="p-4 text-center text-sm">{row.api}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Best Practices */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Best Practices</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card className="glassmorphism border-white/10">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5 text-primary" />
                Choosing the Right Transport
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Start with where your customers already are</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Consider your technical expertise level</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Think about customization needs</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>You can set up multiple transports</span>
                </li>
              </ul>
            </CardContent>
          </Card>

          <Card className="glassmorphism border-white/10">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="w-5 h-5 text-primary" />
                Setup Tips
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Test each transport after setup</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Keep your API keys secure</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Monitor transport performance</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Update configurations as needed</span>
                </li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Next Steps */}
      <section className="text-center space-y-4 pt-8 border-t border-border/50">
        <h3 className="text-lg font-semibold">Ready to get started?</h3>
        <p className="text-muted-foreground">
          Choose a transport above to begin setup, or return to your dashboard to manage existing transports.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link to="/dashboard/transports">
            <Button>
              <Settings className="w-4 h-4 mr-2" />
              Manage Transports
            </Button>
          </Link>
          <Link to="/guides">
            <Button variant="outline">
              Back to Guides
            </Button>
          </Link>
        </div>
      </section>
    </div>
  );
};

export default TransportsGuide;
