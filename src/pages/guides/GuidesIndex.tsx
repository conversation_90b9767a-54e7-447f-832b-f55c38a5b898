import React from "react";
import { <PERSON> } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Settings,
  Database,
  MessageSquare,
  Bot,
  Zap,
  Globe,
  Home,
  ArrowRight,
  BookOpen,
  Lightbulb,
  Users,
  Rocket,
} from "lucide-react";

const GuidesIndex = () => {
  const quickStartGuides = [
    {
      title: "Dashboard Overview",
      description: "Get familiar with your dashboard and main features",
      icon: Home,
      path: "/guides/dashboard",
      badge: "Essential",
      badgeVariant: "default" as const,
    },
    {
      title: "Setup Your First Transport",
      description: "Connect your first communication channel",
      icon: Settings,
      path: "/guides/transports",
      badge: "Quick Start",
      badgeVariant: "secondary" as const,
    },
    {
      title: "Add Knowledge Base Content",
      description: "Upload and manage your AI's knowledge",
      icon: Database,
      path: "/guides/knowledge-base",
      badge: "Important",
      badgeVariant: "outline" as const,
    },
  ];

  const transportGuides = [
    {
      title: "Telegram Bot",
      description: "Set up a Telegram bot for customer support",
      icon: MessageSquare,
      path: "/guides/transports/telegram",
      features: ["Bot token setup", "Command configuration", "Message handling"],
    },
    {
      title: "Discord Bot",
      description: "Create a Discord bot for your server",
      icon: Bot,
      path: "/guides/transports/discord",
      features: ["Server integration", "Role permissions", "Channel setup"],
    },
    {
      title: "Pure API",
      description: "Direct API integration for custom applications",
      icon: Zap,
      path: "/guides/transports/api",
      features: ["API key generation", "Endpoint usage", "Authentication"],
    },
    {
      title: "Website Chatbot",
      description: "Embeddable chatbot widget for your website",
      icon: Globe,
      path: "/guides/transports/chatbot",
      features: ["Widget customization", "Embed code", "Styling options"],
    },
  ];

  const popularTopics = [
    { title: "How to train your AI agent", path: "/guides/knowledge-base", icon: Lightbulb },
    { title: "Managing customer conversations", path: "/guides/transports", icon: Users },
    { title: "Optimizing response quality", path: "/guides/knowledge-base", icon: Rocket },
    { title: "Setting up multiple channels", path: "/guides/transports", icon: Settings },
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-2 mb-4">
          <BookOpen className="w-8 h-8 text-primary" />
          <h1 className="text-3xl font-bold">Help Center</h1>
        </div>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Everything you need to know to get the most out of your AI agent platform. 
          Find step-by-step guides, best practices, and troubleshooting tips.
        </p>
      </div>

      {/* Quick Start Section */}
      <section className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-semibold mb-2">Quick Start Guides</h2>
          <p className="text-muted-foreground">
            Essential guides to get you up and running quickly
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {quickStartGuides.map((guide) => {
            const Icon = guide.icon;
            return (
              <Card key={guide.path} className="glassmorphism border-white/10 hover:border-white/20 transition-all hover:scale-105">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                      <Icon className="w-6 h-6 text-primary" />
                    </div>
                    <Badge variant={guide.badgeVariant}>{guide.badge}</Badge>
                  </div>
                  <CardTitle className="text-lg">{guide.title}</CardTitle>
                  <CardDescription>{guide.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <Link to={guide.path}>
                    <Button className="w-full group">
                      Get Started
                      <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </section>

      {/* Transport Guides Section */}
      <section className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-semibold mb-2">Transport Setup Guides</h2>
          <p className="text-muted-foreground">
            Detailed guides for setting up each communication channel
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {transportGuides.map((guide) => {
            const Icon = guide.icon;
            return (
              <Card key={guide.path} className="glassmorphism border-white/10 hover:border-white/20 transition-colors">
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                      <Icon className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{guide.title}</CardTitle>
                      <CardDescription>{guide.description}</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-muted-foreground">What you'll learn:</h4>
                    <ul className="space-y-1">
                      {guide.features.map((feature, index) => (
                        <li key={index} className="text-sm flex items-center gap-2">
                          <div className="w-1.5 h-1.5 bg-primary rounded-full" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <Link to={guide.path}>
                    <Button variant="outline" className="w-full group">
                      View Guide
                      <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </section>

      {/* Popular Topics */}
      <section className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-semibold mb-2">Popular Topics</h2>
          <p className="text-muted-foreground">
            Most searched help topics and frequently asked questions
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {popularTopics.map((topic) => {
            const Icon = topic.icon;
            return (
              <Link
                key={topic.path}
                to={topic.path}
                className="flex items-center gap-3 p-4 rounded-lg bg-card/30 backdrop-blur-sm border border-border/50 hover:border-border transition-colors group"
              >
                <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                  <Icon className="w-4 h-4 text-primary" />
                </div>
                <span className="flex-1 text-sm font-medium">{topic.title}</span>
                <ArrowRight className="w-4 h-4 text-muted-foreground group-hover:text-foreground group-hover:translate-x-1 transition-all" />
              </Link>
            );
          })}
        </div>
      </section>

      {/* Help Footer */}
      <section className="text-center space-y-4 pt-8 border-t border-border/50">
        <h3 className="text-lg font-semibold">Still need help?</h3>
        <p className="text-muted-foreground">
          Can't find what you're looking for? Our support team is here to help.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link to="/support">
            <Button variant="outline">
              <MessageSquare className="w-4 h-4 mr-2" />
              Contact Support
            </Button>
          </Link>
          <Link to="/dashboard">
            <Button>
              <Home className="w-4 h-4 mr-2" />
              Back to Dashboard
            </Button>
          </Link>
        </div>
      </section>
    </div>
  );
};

export default GuidesIndex;
