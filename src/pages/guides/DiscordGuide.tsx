import React from "react";
import { <PERSON> } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Bot,
  ArrowRight,
  CheckCircle,
  Settings,
  Users,
  Shield,
  Info,
  AlertTriangle,
  ExternalLink,
  Clock,
} from "lucide-react";

const DiscordGuide = () => {
  const steps = [
    {
      title: "Create Discord Application",
      description: "Set up a new application in Discord Developer Portal",
      details: [
        "Go to https://discord.com/developers/applications",
        "Click 'New Application' and give it a name",
        "Navigate to the 'Bot' section in the sidebar",
        "Click 'Add Bot' to create a bot user",
        "Copy the bot token (keep it secure!)",
      ],
    },
    {
      title: "Configure Bot Permissions",
      description: "Set up the necessary permissions for your bot",
      details: [
        "In the Bot section, enable 'Message Content Intent'",
        "Go to OAuth2 > URL Generator",
        "Select 'bot' scope and required permissions",
        "Copy the generated URL to invite the bot",
        "Use the URL to add bot to your Discord server",
      ],
    },
    {
      title: "Add to Dashboard",
      description: "Configure the bot in your transport settings",
      details: [
        "Go to Dashboard > Transports",
        "Find the Discord Bot card",
        "Click 'Setup Discord Bot'",
        "Paste your bot token",
        "Click 'Setup Discord Bot' to activate",
      ],
    },
  ];

  const permissions = [
    { name: "Send Messages", description: "Allow bot to send responses", required: true },
    { name: "Read Message History", description: "Read previous messages for context", required: true },
    { name: "Use Slash Commands", description: "Enable slash command functionality", required: false },
    { name: "Embed Links", description: "Send rich embedded content", required: false },
    { name: "Attach Files", description: "Share files and images", required: false },
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Bot className="w-6 h-6 text-primary" />
          <h1 className="text-3xl font-bold">Discord Bot Setup Guide</h1>
          <Badge variant="secondary">10 minutes</Badge>
        </div>
        <p className="text-lg text-muted-foreground">
          Connect your AI agent to Discord and provide automated support for your gaming community or team server.
        </p>
      </div>

      {/* Overview */}
      <Alert className="glassmorphism border-blue-500/20 bg-blue-500/5">
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>What you'll need:</strong> A Discord account, server admin permissions, and about 10 minutes. 
          This guide covers creating a Discord application and configuring the bot.
        </AlertDescription>
      </Alert>

      {/* Step-by-Step Guide */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Step-by-Step Setup</h2>
        
        <div className="space-y-6">
          {steps.map((step, index) => (
            <Card key={index} className="glassmorphism border-white/10">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                    {index + 1}
                  </div>
                  <div>
                    <CardTitle className="text-lg">{step.title}</CardTitle>
                    <CardDescription>{step.description}</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <ol className="space-y-2">
                  {step.details.map((detail, detailIndex) => (
                    <li key={detailIndex} className="flex items-start gap-3 text-sm">
                      <div className="w-5 h-5 bg-muted rounded-full flex items-center justify-center text-xs font-medium mt-0.5 flex-shrink-0">
                        {detailIndex + 1}
                      </div>
                      <span>{detail}</span>
                    </li>
                  ))}
                </ol>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Permissions */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Required Permissions</h2>
        
        <Card className="glassmorphism border-white/10">
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-border/50">
                    <th className="text-left p-4 font-medium">Permission</th>
                    <th className="text-left p-4 font-medium">Description</th>
                    <th className="text-center p-4 font-medium">Required</th>
                  </tr>
                </thead>
                <tbody>
                  {permissions.map((permission, index) => (
                    <tr key={index} className="border-b border-border/30 last:border-b-0">
                      <td className="p-4 font-medium">{permission.name}</td>
                      <td className="p-4 text-sm text-muted-foreground">{permission.description}</td>
                      <td className="p-4 text-center">
                        {permission.required ? (
                          <Badge variant="default">Required</Badge>
                        ) : (
                          <Badge variant="outline">Optional</Badge>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Important Notes */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Important Notes</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Alert className="glassmorphism border-yellow-500/20 bg-yellow-500/5">
            <Shield className="h-4 w-4" />
            <AlertDescription>
              <strong>Security:</strong> Keep your bot token secure and never share it publicly. 
              Anyone with your token can control your bot.
            </AlertDescription>
          </Alert>

          <Alert className="glassmorphism border-blue-500/20 bg-blue-500/5">
            <Info className="h-4 w-4" />
            <AlertDescription>
              <strong>Server Permissions:</strong> You need admin permissions on the Discord server 
              to add the bot. The bot will only work in servers where it's been invited.
            </AlertDescription>
          </Alert>
        </div>
      </section>

      {/* Next Steps */}
      <section className="text-center space-y-4 pt-8 border-t border-border/50">
        <h3 className="text-lg font-semibold">Ready to set up your Discord bot?</h3>
        <p className="text-muted-foreground">
          Head to your dashboard to configure your Discord transport, or explore other transport options.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link to="/dashboard/transports">
            <Button>
              <Settings className="w-4 h-4 mr-2" />
              Setup Discord Bot
            </Button>
          </Link>
          <Link to="/guides/transports">
            <Button variant="outline">
              <ArrowRight className="w-4 h-4 mr-2" />
              Other Transports
            </Button>
          </Link>
        </div>
      </section>
    </div>
  );
};

export default DiscordGuide;
