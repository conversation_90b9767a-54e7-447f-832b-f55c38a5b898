import React from "react";
import { <PERSON> } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Database,
  ArrowRight,
  CheckCircle,
  FileText,
  Link as LinkIcon,
  MessageSquare,
  Type,
  Settings,
  Info,
  Lightbulb,
  Target,
  TrendingUp,
} from "lucide-react";

const KnowledgeBaseGuide = () => {
  const dataTypes = [
    {
      title: "Website Links",
      description: "Scrape content from web pages and documentation",
      icon: LinkIcon,
      features: ["Automatic content extraction", "Regular updates", "Multiple pages at once"],
      bestFor: "Documentation, blog posts, product pages",
    },
    {
      title: "Document Upload",
      description: "Upload PDFs, Word docs, and text files",
      icon: FileText,
      features: ["PDF text extraction", "Word document support", "Batch uploads"],
      bestFor: "Manuals, guides, policies, reports",
    },
    {
      title: "Plain Text",
      description: "Add custom text content directly",
      icon: Type,
      features: ["Rich text editing", "Instant addition", "Easy formatting"],
      bestFor: "FAQs, custom instructions, quick notes",
    },
    {
      title: "Q&A Pairs",
      description: "Structured question and answer format",
      icon: MessageSquare,
      features: ["Structured format", "Easy management", "High accuracy"],
      bestFor: "Common questions, specific scenarios",
    },
  ];

  const bestPractices = [
    {
      title: "Content Quality",
      tips: [
        "Use clear, concise language",
        "Keep information up-to-date",
        "Include relevant examples",
        "Avoid duplicate content",
      ],
    },
    {
      title: "Organization",
      tips: [
        "Group related content together",
        "Use descriptive titles",
        "Tag content appropriately",
        "Regular content audits",
      ],
    },
    {
      title: "Optimization",
      tips: [
        "Monitor AI response quality",
        "Add missing information",
        "Remove outdated content",
        "Test with real questions",
      ],
    },
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Database className="w-6 h-6 text-primary" />
          <h1 className="text-3xl font-bold">Knowledge Base Management Guide</h1>
        </div>
        <p className="text-lg text-muted-foreground">
          Learn how to build and maintain a comprehensive knowledge base that powers 
          your AI agent with accurate, relevant information.
        </p>
      </div>

      {/* Overview */}
      <Alert className="glassmorphism border-blue-500/20 bg-blue-500/5">
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>Why Knowledge Base Matters:</strong> Your knowledge base is the foundation of your AI agent's intelligence. 
          The quality and comprehensiveness of your content directly impacts response accuracy and customer satisfaction.
        </AlertDescription>
      </Alert>

      {/* Data Types */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Content Types</h2>
        <p className="text-muted-foreground">
          Choose the right content type based on your source material and use case.
        </p>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {dataTypes.map((type) => {
            const Icon = type.icon;
            return (
              <Card key={type.title} className="glassmorphism border-white/10 hover:border-white/20 transition-colors">
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                      <Icon className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{type.title}</CardTitle>
                      <CardDescription>{type.description}</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Features:</h4>
                    <ul className="space-y-1">
                      {type.features.map((feature, index) => (
                        <li key={index} className="text-xs flex items-center gap-2">
                          <CheckCircle className="w-3 h-3 text-green-500" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div className="p-3 bg-muted/30 rounded-lg">
                    <p className="text-sm"><strong>Best for:</strong> {type.bestFor}</p>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </section>

      {/* Getting Started */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Getting Started</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="glassmorphism border-white/10">
            <CardHeader>
              <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold mb-2">
                1
              </div>
              <CardTitle className="text-lg">Plan Your Content</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm">
              <p>• Identify key topics customers ask about</p>
              <p>• Gather existing documentation</p>
              <p>• Prioritize most important information</p>
              <p>• Choose appropriate content types</p>
            </CardContent>
          </Card>

          <Card className="glassmorphism border-white/10">
            <CardHeader>
              <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold mb-2">
                2
              </div>
              <CardTitle className="text-lg">Add Content</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm">
              <p>• Start with most common questions</p>
              <p>• Upload existing documentation</p>
              <p>• Add website links for live content</p>
              <p>• Create Q&A pairs for specific scenarios</p>
            </CardContent>
          </Card>

          <Card className="glassmorphism border-white/10">
            <CardHeader>
              <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold mb-2">
                3
              </div>
              <CardTitle className="text-lg">Test & Optimize</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm">
              <p>• Test with real customer questions</p>
              <p>• Monitor response quality</p>
              <p>• Add missing information</p>
              <p>• Update content regularly</p>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Best Practices */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Best Practices</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {bestPractices.map((practice) => (
            <Card key={practice.title} className="glassmorphism border-white/10">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Lightbulb className="w-5 h-5 text-primary" />
                  {practice.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {practice.tips.map((tip, index) => (
                    <li key={index} className="flex items-start gap-2 text-sm">
                      <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span>{tip}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Optimization Tips */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold">Optimization Tips</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card className="glassmorphism border-white/10">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5 text-primary" />
                Content Quality
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Write in your brand's voice and tone</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Include specific examples and use cases</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Keep information current and accurate</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Use clear, simple language</span>
                </li>
              </ul>
            </CardContent>
          </Card>

          <Card className="glassmorphism border-white/10">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5 text-primary" />
                Performance Monitoring
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Review conversation logs regularly</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Identify gaps in knowledge</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Add content for common questions</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Remove or update outdated information</span>
                </li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Next Steps */}
      <section className="text-center space-y-4 pt-8 border-t border-border/50">
        <h3 className="text-lg font-semibold">Ready to build your knowledge base?</h3>
        <p className="text-muted-foreground">
          Start adding content to your knowledge base and watch your AI agent become more helpful and accurate.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link to="/dashboard/knowledge-base">
            <Button>
              <Database className="w-4 h-4 mr-2" />
              Manage Knowledge Base
            </Button>
          </Link>
          <Link to="/guides">
            <Button variant="outline">
              <ArrowRight className="w-4 h-4 mr-2" />
              Back to Guides
            </Button>
          </Link>
        </div>
      </section>
    </div>
  );
};

export default KnowledgeBaseGuide;
