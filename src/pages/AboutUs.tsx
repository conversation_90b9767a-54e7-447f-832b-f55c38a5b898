import React from "react";
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "@/components/DezhNavbar";
import <PERSON><PERSON><PERSON><PERSON>ooter from "@/components/DezhFooter";
import { Users, Target, Award, Brain, Quote } from "lucide-react";

const AboutUs = () => {
  const teamMembers = [
    {
      name: "<PERSON><PERSON>",
      role: "AI Researcher & Co-founder",
      bio: "",
      avatar: "/team3.webp"
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      role: "Software Engineer & Co-founder",
      bio: "",
      avatar: "/team1.jpeg"
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      role: "Product Manager",
      bio: "",
      avatar: "/team2.jpeg"
    }
  ];

  const values = [
    {
      icon: Brain,
      title: "Innovation First",
      description: "We push the boundaries of AI to solve real business challenges with cutting-edge technology."
    },
    {
      icon: Users,
      title: "Customer Obsessed",
      description: "Every feature we build starts with understanding our customers' pain points and success metrics."
    },
    {
      icon: Target,
      title: "Excellence Driven",
      description: "We maintain the highest standards in code quality, security, and system reliability."
    },
    {
      icon: Award,
      title: "Impact Focused",
      description: "We measure success by the tangible business outcomes we deliver for our customers."
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <DezhNavbar />

      {/* Hero Section */}
      <section className="pt-24 pb-16 relative overflow-hidden hero-gradient">
        {/* AI-themed background elements */}
        <div className="absolute top-20 right-10 w-80 h-80 bg-primary/8 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 left-10 w-96 h-96 bg-accent/5 rounded-full blur-3xl animate-pulse-glow"></div>

        {/* Neural network overlay */}
        <div className="absolute inset-0 opacity-[0.03]">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="neural-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
                <circle cx="10" cy="10" r="0.8" fill="currentColor" opacity="0.4" />
                <path d="M 10 0 L 10 20 M 0 10 L 20 10" stroke="currentColor" strokeWidth="0.2" opacity="0.3" />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#neural-pattern)" className="text-primary" />
          </svg>
        </div>

        <div className="section-container relative z-10">
          <div className="max-w-4xl mx-auto">
            <div className="inline-flex items-center px-6 py-3 glassmorphism glassmorphism-hover rounded-full mb-8 opacity-0 animate-fade-in">
              <Users className="w-4 h-4 mr-3 text-primary" />
              <span className="text-sm font-medium text-foreground">Meet Our Team</span>
            </div>

            <h1 className="section-title opacity-0 animate-fade-in stagger-1">
              Building the idea of
              <span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent"> AI as Labour </span>
            </h1>

            <p className="section-subtitle opacity-0 animate-fade-in stagger-2">
              We're a team of engineers and researchers dedicated to making AI as a Labour idea possible.
            </p>
          </div>
        </div>
      </section>

      {/* Company Stats */}
      {/* <section className="py-16 relative">
        <div className="section-container">
          <div className="grid md:grid-cols-4 gap-8">
            {[
              { number: "500K+", label: "Support Tickets Handled", delay: "0s" },
              { number: "99.9%", label: "System Uptime", delay: "0.2s" },
              { number: "150+", label: "Enterprise Customers", delay: "0.4s" },
              { number: "24/7", label: "AI Agent Availability", delay: "0.6s" }
            ].map((stat, index) => (
              <div key={index} className="text-center opacity-0 animate-fade-in" style={{ animationDelay: stat.delay }}>
                <div className="relative">
                  <div className="text-4xl font-bold text-primary mb-2">{stat.number}</div>
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-12 h-12 border-2 border-primary/20 rounded-full animate-pulse-simple"></div>
                </div>
                <p className="text-muted-foreground">{stat.label}</p>
              </div>
            ))}
          </div>
        </div>
      </section> */}

      {/* Our story */}
      <section className="py-16">
        <div className="section-container">
          <div className="max-w-4xl mx-auto">
            <div className="card-modern mb-12 opacity-0 animate-fade-in">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center">
                    <Quote className="w-6 h-6 text-primary" />
                  </div>
                </div>
                <div>
                  <h2 className="text-2xl font-semibold text-foreground mb-4">Who are we?</h2>
                  <p className="text-muted-foreground leading-relaxed">
                    Founded by{" "}
                    <a href="https://dezh.tech" className="font-semibold hover:underline">
                      Dezh Tech
                    </a>{" "}
                    on July 12, 2025, The Rokovo empowers businesses to harness the power of AI agents and large-language models—delivering faster, smarter, and more cost-effective operations.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-24 relative overflow-hidden">
        {/* Floating geometric elements */}
        <div className="absolute top-20 left-20 w-6 h-6 border-2 border-primary/30 rotate-45 animate-float" style={{ animationDelay: '0s' }}></div>
        <div className="absolute bottom-20 right-20 w-8 h-8 border-2 border-accent/30 rounded-full animate-pulse-simple" style={{ animationDelay: '1s' }}></div>

        <div className="section-container">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-semibold text-foreground mb-6">Our Leadership Team</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Experienced leaders from top tech companies, united by the vision of transforming customer support with AI.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {teamMembers.map((member, index) => (
              <div key={index} className="card-modern group opacity-0 animate-fade-in hover:scale-105 transition-all duration-300" style={{ animationDelay: `${index * 0.2}s` }}>
                <div className="relative mb-6">
                  <div className="w-24 h-24 mx-auto rounded-full overflow-hidden ring-4 ring-primary/20 group-hover:ring-primary/40 transition-all duration-300">
                    <img
                      src={member.avatar}
                      alt={member.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-primary rounded-full flex items-center justify-center animate-pulse-simple">
                    <div className="w-2 h-2 bg-background rounded-full"></div>
                  </div>
                </div>

                <div className="text-center">
                  <h3 className="text-xl font-semibold text-foreground mb-1">{member.name}</h3>
                  <p className="text-primary font-medium mb-3">{member.role}</p>
                  <p className="text-sm text-muted-foreground leading-relaxed">{member.bio}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Company Values */}
      <section className="py-24 relative">
        {/* AI circuit pattern */}
        <div className="absolute inset-0 opacity-[0.02]">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="values-circuit" x="0" y="0" width="25" height="25" patternUnits="userSpaceOnUse">
                <path d="M 0 12.5 L 12.5 12.5 L 12.5 0 M 12.5 12.5 L 25 12.5 M 12.5 12.5 L 12.5 25"
                  stroke="currentColor" strokeWidth="0.3" fill="none" opacity="0.6" />
                <circle cx="12.5" cy="12.5" r="1.2" fill="currentColor" opacity="0.4" />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#values-circuit)" className="text-accent" />
          </svg>
        </div>

        <div className="section-container relative z-10">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-semibold text-foreground mb-6">Our Core Values</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              The principles that guide everything we do, from product development to customer relationships.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {values.map((value, index) => (
              <div key={index} className="card-modern group opacity-0 animate-fade-in glassmorphism-hover" style={{ animationDelay: `${index * 0.15}s` }}>
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center group-hover:bg-primary/30 transition-colors duration-300">
                      <value.icon className="w-6 h-6 text-primary" />
                    </div>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-foreground mb-2">{value.title}</h3>
                    <p className="text-muted-foreground leading-relaxed">{value.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
      <DezhFooter />
    </div>
  );
};

export default AboutUs;