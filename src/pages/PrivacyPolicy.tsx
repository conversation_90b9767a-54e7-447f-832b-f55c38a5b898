import React from "react";
import <PERSON>zh<PERSON><PERSON><PERSON> from "@/components/DezhNavbar";
import <PERSON><PERSON><PERSON><PERSON>ooter from "@/components/DezhFooter";
import { Shield, Eye, Lock, Database, Users, FileText } from "lucide-react";

const PrivacyPolicy = () => {
  const sections = [
    {
      title: "Information We Collect",
      icon: Database,
      content: [
        "Account information (name, email, company details)",
        "Usage data and analytics to improve our service",
        "Customer support interactions and chat logs",
        "Technical information like IP addresses and browser data",
        "API usage metrics and performance data"
      ]
    },
    {
      title: "How We Use Your Information",
      icon: Eye,
      content: [
        "Provide and maintain our AI support services",
        "Process payments and manage your account",
        "Improve our AI models and service quality",
        "Send important updates and security notifications",
        "Comply with legal obligations and enforce our terms"
      ]
    },
    {
      title: "Data Security",
      icon: Lock,
      content: [
        "End-to-end encryption for all data transmissions",
        "SOC 2 Type II compliant infrastructure",
        "Regular security audits and penetration testing",
        "Role-based access controls and authentication",
        "Secure data centers with 24/7 monitoring"
      ]
    },
    {
      title: "Data Sharing",
      icon: Users,
      content: [
        "We never sell your personal information",
        "Limited sharing with trusted service providers",
        "Legal compliance when required by law",
        "Business transfers with appropriate protections",
        "Aggregated, anonymized data for research purposes"
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <DezhNavbar />
      
      {/* Hero Section */}
      <section className="pt-24 pb-16 relative overflow-hidden hero-gradient">
        <div className="absolute top-20 right-10 w-80 h-80 bg-primary/8 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 left-10 w-96 h-96 bg-accent/5 rounded-full blur-3xl animate-pulse-glow"></div>
        
        <div className="section-container relative z-10">
          <div className="max-w-4xl mx-auto">
            <div className="inline-flex items-center px-6 py-3 glassmorphism glassmorphism-hover rounded-full mb-8 opacity-0 animate-fade-in">
              <Shield className="w-4 h-4 mr-3 text-primary" />
              <span className="text-sm font-medium text-foreground">Privacy & Security</span>
            </div>
            
            <h1 className="section-title opacity-0 animate-fade-in stagger-1">
              Privacy 
              <span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent"> Policy </span>
            </h1>
            
            <p className="section-subtitle opacity-0 animate-fade-in stagger-2">
              We take your privacy seriously. Learn how we collect, use, and protect your information 
              when you use Rokovo services.
            </p>
            
            <div className="text-sm text-muted-foreground mt-6 opacity-0 animate-fade-in stagger-3">
              Last updated: July 12, 2025
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16">
        <div className="section-container">
          <div className="max-w-4xl mx-auto">
            {/* Overview */}
            <div className="card-modern mb-12 opacity-0 animate-fade-in">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center">
                    <FileText className="w-6 h-6 text-primary" />
                  </div>
                </div>
                <div>
                  <h2 className="text-2xl font-semibold text-foreground mb-4">Overview</h2>
                  <p className="text-muted-foreground leading-relaxed">
                    This Privacy Policy describes how Dezh AI ("we", "our", or "us") collects, uses, and shares 
                    information about you when you use our AI-powered customer support platform. We are committed 
                    to protecting your privacy and maintaining the security of your data.
                  </p>
                </div>
              </div>
            </div>

            {/* Main Sections */}
            <div className="space-y-8">
              {sections.map((section, index) => (
                <div key={index} className="card-modern opacity-0 animate-fade-in glassmorphism-hover" style={{ animationDelay: `${index * 0.1}s` }}>
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center">
                        <section.icon className="w-6 h-6 text-primary" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-foreground mb-4">{section.title}</h3>
                      <ul className="space-y-2">
                        {section.content.map((item, itemIndex) => (
                          <li key={itemIndex} className="flex items-start space-x-3">
                            <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                            <span className="text-muted-foreground leading-relaxed">{item}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Additional Sections */}
            <div className="grid md:grid-cols-2 gap-8 mt-12">
              <div className="card-modern opacity-0 animate-fade-in" style={{ animationDelay: '0.4s' }}>
                <h3 className="text-xl font-semibold text-foreground mb-4">Your Rights</h3>
                <ul className="space-y-2 text-muted-foreground">
                  <li>• Access your personal information</li>
                  <li>• Correct inaccurate data</li>
                  <li>• Delete your account and data</li>
                  <li>• Export your data</li>
                  <li>• Opt-out of marketing communications</li>
                </ul>
              </div>

              <div className="card-modern opacity-0 animate-fade-in" style={{ animationDelay: '0.6s' }}>
                <h3 className="text-xl font-semibold text-foreground mb-4">Contact Us</h3>
                <div className="space-y-2 text-muted-foreground">
                  <p>For privacy-related questions:</p>
                  <p>Email: <EMAIL></p>
                  <p>Address: 123 AI Street, Tech City, TC 12345</p>
                  <p>Phone: +****************</p>
                </div>
              </div>
            </div>

            {/* Updates Notice */}
            <div className="card-modern mt-12 bg-primary/5 border-primary/20 opacity-0 animate-fade-in" style={{ animationDelay: '0.8s' }}>
              <h3 className="text-xl font-semibold text-foreground mb-4">Policy Updates</h3>
              <p className="text-muted-foreground leading-relaxed">
                We may update this Privacy Policy from time to time. We will notify you of any material changes 
                by posting the new Privacy Policy on this page and updating the "Last updated" date. We encourage 
                you to review this Privacy Policy periodically for any changes.
              </p>
            </div>
          </div>
        </div>
      </section>

      <DezhFooter />
    </div>
  );
};

export default PrivacyPolicy;