name: Create and publish a Rokovo-frontend-prod Nginx image

on:
  push:
    tags:
      - "prod-v*"

env:
  REGISTRY: Rokovo-registry.dezh.tech
  IMAGE_NAME: dezh/Rokovo-frontend-prod

jobs:
  build-and-push-image:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Extract version and environment from Git tag
        id: version
        run: |
          RAW_TAG="${GITHUB_REF##*/}"
          VERSION="${RAW_TAG#v}"
          if [[ "$RAW_TAG" == *"-dev" ]]; then
            ENVIRONMENT="development"
          else
            ENVIRONMENT="production"
          fi
          echo "VERSION=$VERSION" >> "$GITHUB_OUTPUT"
          echo "ENVIRONMENT=$ENVIRONMENT" >> "$GITHUB_OUTPUT"

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ secrets.Rokovo_REGISTRY_USERNAME }}
          password: ${{ secrets.Rokovo_REGISTRY_PASSWORD }}

      - name: Cache Docker layers
        uses: actions/cache@v3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - name: Build and push Docker image
        id: push
        uses: docker/build-push-action@v5
        with:
          context: .
          file: Dockerfile
          push: true
          tags: |
            ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ steps.version.outputs.VERSION }}
            ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest
          build-args: |
            BUILD_ENV=production
            LABEL_MAINTAINER=${{ github.actor }}
            LABEL_VERSION=${{ steps.version.outputs.VERSION }}
            LABEL_REPO=${{ github.repository }}
            LABEL_COMMIT=${{ github.sha }}
     
      - name: Call Portainer webhook
        if: success()
        run: |
          curl -X POST -H "Content-Type: application/json" \
          https://Rokovo-hoshyar.dezh.tech/api/webhooks/59403a26-cb98-4866-a583-9fe2ff63cdfb