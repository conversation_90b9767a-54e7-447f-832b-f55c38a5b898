(function () {
  "use strict";

  // Widget configuration and state
  let widgetConfig = {
    publishableKey: null,
    businessName: "Your Business",
    agentName: "AI Assistant",
    primaryColor: "#FF4800",
    secondaryColor: "#E2DDD6",
    chatBubbleStyle: "rounded",
    avatarUrl: null,
    apiBaseUrl: "https://Rokovo-dev-api.dezh.tech",
  };

  let widgetState = {
    isOpen: false,
    isMinimized: false,
    messages: [],
    sessionId: null,
    isTyping: false,
    isLoading: true,
    error: null,
  };

  // Utility functions
  function createElement(tag, className, innerHTML) {
    const element = document.createElement(tag);
    if (className) element.className = className;
    if (innerHTML) element.innerHTML = innerHTML;
    return element;
  }

  function escapeHtml(text) {
    const div = document.createElement("div");
    div.textContent = text;
    return div.innerHTML;
  }

  // API service functions
  async function apiRequest(endpoint, options = {}) {
    const url = `${widgetConfig.apiBaseUrl}/${endpoint}`;

    const response = await fetch(url, {
      method: options.method,
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      body: options.body,
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response}`);
    }

    return response.json();
  }

  async function initSession(externalId) {
    try {
      const response = await apiRequest("transport/widget", {
        method: "POST",
        body: JSON.stringify({
          externalUserId: externalId
        }),
        headers: {
          "api-key": widgetConfig.publishableKey,
          "accept": '*/*',
        }
      });

      widgetState.sessionId = response.data.sessionId;

      return response.data.sessionId;
    } catch (apiError) {
      console.warn("Session API call failed:", apiError);
      return "";
    }
  }

  async function continueSession(content) {
    if (!widgetState.sessionId) {
      return;
    }

    try {
      const response = await apiRequest("transport/widget", {
        method: "PATCH",
        body: JSON.stringify({ sessionId: widgetState.sessionId, content }),
        headers: {
          "api-key": widgetConfig.publishableKey,
          "accept": '*/*',
        }
      });
      return response;
    } catch (error) {
      console.error("Failed to continue session:", error);
      throw error;
    }
  }

  // Color utility functions for dynamic styling
  function hexToHsla(hex, alpha = 1) {
    // Remove # if present
    hex = hex.replace("#", "");

    // Parse hex values
    const r = parseInt(hex.substr(0, 2), 16) / 255;
    const g = parseInt(hex.substr(2, 2), 16) / 255;
    const b = parseInt(hex.substr(4, 2), 16) / 255;

    // Convert to HSL
    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h,
      s,
      l = (max + min) / 2;

    if (max === min) {
      h = s = 0; // achromatic
    } else {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
      switch (max) {
        case r:
          h = (g - b) / d + (g < b ? 6 : 0);
          break;
        case g:
          h = (b - r) / d + 2;
          break;
        case b:
          h = (r - g) / d + 4;
          break;
      }
      h /= 6;
    }

    // Convert to degrees and percentages
    h = Math.round(h * 360);
    s = Math.round(s * 100);
    l = Math.round(l * 100);

    return `hsla(${h}, ${s}%, ${l}%, ${alpha})`;
  }

  function adjustColorBrightness(hex, percent) {
    // Remove # if present
    hex = hex.replace("#", "");

    // Parse hex values
    const num = parseInt(hex, 16);
    const amt = Math.round(2.55 * percent);
    const R = (num >> 16) + amt;
    const G = ((num >> 8) & 0x00ff) + amt;
    const B = (num & 0x0000ff) + amt;

    return (
      "#" +
      (
        0x1000000 +
        (R < 255 ? (R < 1 ? 0 : R) : 255) * 0x10000 +
        (G < 255 ? (G < 1 ? 0 : G) : 255) * 0x100 +
        (B < 255 ? (B < 1 ? 0 : B) : 255)
      )
        .toString(16)
        .slice(1)
    );
  }

  // Widget CSS styles - Dynamic color support
  function injectStyles() {
    if (document.getElementById("Rokovo-widget-styles")) return;

    const styles = `
        /* Import Inter font to match landing page */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

        .Rokovo-widget-container {
          position: fixed;
          bottom: 24px;
          right: 24px;
          z-index: 999999;
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          font-feature-settings: 'cv11', 'ss01';
          font-variation-settings: 'opsz' 32;
        }

        /* Modern Creative Toggle Button */
        .Rokovo-widget-toggle {
          width: 68px;
          height: 68px;
          border-radius: 20px;
          border: none;
          cursor: pointer;
          background: linear-gradient(135deg, ${widgetConfig.primaryColor} 0%, ${adjustColorBrightness(
      widgetConfig.primaryColor,
      10
    )} 50%, ${adjustColorBrightness(widgetConfig.primaryColor, -5)} 100%);
          color: white;
          box-shadow:
            0 8px 32px ${hexToHsla(widgetConfig.primaryColor, 0.4)},
            0 4px 16px ${hexToHsla(widgetConfig.primaryColor, 0.3)},
            inset 0 1px 0 hsla(0, 0%, 100%, 0.2),
            inset 0 -1px 0 hsla(0, 0%, 0%, 0.1);
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
          font-size: 28px;
          position: relative;
          overflow: hidden;
          animation: Rokovo-modern-pulse 3s ease-in-out infinite;
          z-index: 999999;
        }

        /* Ensure SVG inside button doesn't interfere with clicks */
        .Rokovo-widget-toggle svg {
          pointer-events: none;
          user-select: none;
          display: block;
        }

        .Rokovo-widget-toggle::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, hsla(0, 0%, 100%, 0.2), transparent);
          transition: left 0.6s ease;
        }

        .Rokovo-widget-toggle:hover {
          transform: scale(1.05) rotate(-2deg);
          border-radius: 24px;
          box-shadow:
            0 12px 48px ${hexToHsla(widgetConfig.primaryColor, 0.5)},
            0 6px 24px ${hexToHsla(widgetConfig.primaryColor, 0.4)},
            inset 0 1px 0 hsla(0, 0%, 100%, 0.3),
            inset 0 -1px 0 hsla(0, 0%, 0%, 0.1);
        }

        .Rokovo-widget-toggle:hover::before {
          left: 100%;
        }

        .Rokovo-widget-toggle:active {
          transform: scale(0.95) rotate(1deg);
          transition: all 0.1s ease;
        }

        /* Chat Window - Matching SupportChat glassmorphism */
        .Rokovo-widget-chat {
          position: absolute;
          bottom: 80px;
          right: 0;
          width: 384px;
          height: 600px;
          background: hsla(210, 11%, 8%, 0.95);
          backdrop-filter: blur(40px);
          border-radius: 16px;
          box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
          border: 1px solid hsl(215, 16%, 15%);
          display: none;
          flex-direction: column;
          overflow: hidden;
          transform: scale(0);
          opacity: 0;
          transform-origin: bottom right;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          z-index: 999998;
          max-width: calc(100vw - 40px);
          max-height: calc(100vh - 120px);
        }

        .Rokovo-widget-chat.open {
          display: flex;
          transform: scale(1);
          opacity: 1;
        }

        /* Header - Dynamic color support */
        .Rokovo-widget-header {
          background: ${hexToHsla(widgetConfig.primaryColor, 0.1)};
          border-bottom: 1px solid hsl(215, 16%, 15%);
          padding: 24px;
          display: flex;
          align-items: center;
          gap: 16px;
        }

        .Rokovo-widget-avatar {
          width: 48px;
          height: 48px;
          background: ${widgetConfig.primaryColor};
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 24px;
          flex-shrink: 0;
        }

        .Rokovo-widget-info {
          flex: 1;
        }

        .Rokovo-widget-info h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: hsl(210, 40%, 98%);
          line-height: 1.2;
        }

        .Rokovo-widget-info p {
          margin: 4px 0 0 0;
          font-size: 14px;
          color: ${widgetConfig.secondaryColor ? hexToHsla(widgetConfig.secondaryColor, 0.8) : "hsl(217, 10%, 65%)"};
        }

        .Rokovo-widget-status {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-top: 4px;
        }

        .Rokovo-widget-status-dot {
          width: 8px;
          height: 8px;
          background: #10b981;
          border-radius: 50%;
          animation: Rokovo-pulse-dot 2s ease-in-out infinite;
        }

        .Rokovo-widget-status-text {
          font-size: 12px;
          color: ${widgetConfig.secondaryColor ? hexToHsla(widgetConfig.secondaryColor, 0.7) : "hsl(217, 10%, 65%)"};
        }

        .Rokovo-widget-minimize {
          background: none;
          border: none;
          cursor: pointer;
          padding: 8px;
          border-radius: 8px;
          color: ${widgetConfig.secondaryColor ? hexToHsla(widgetConfig.secondaryColor, 0.8) : "hsl(217, 10%, 65%)"};
          transition: background-color 0.2s ease;
          font-size: 18px;
          line-height: 1;
        }

        .Rokovo-widget-minimize:hover {
          background: ${widgetConfig.secondaryColor ? hexToHsla(widgetConfig.secondaryColor, 0.2) : "hsla(215, 16%, 15%, 0.5)"
      };
          color: hsl(210, 40%, 98%);
        }

        /* Messages Area - Matching SupportChat scrollbar */
        .Rokovo-widget-messages {
          flex: 1;
          overflow-y: auto;
          padding: 24px;
          display: flex;
          flex-direction: column;
          gap: 16px;
        }

        .Rokovo-widget-messages::-webkit-scrollbar {
          width: 6px;
        }

        .Rokovo-widget-messages::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.05);
        }

        .Rokovo-widget-messages::-webkit-scrollbar-thumb {
          background: rgba(255, 255, 255, 0.2);
          border-radius: 3px;
        }

        .Rokovo-widget-messages::-webkit-scrollbar-thumb:hover {
          background: rgba(255, 255, 255, 0.3);
        }

        /* Message Styling - Matching SupportChat exactly */
        .Rokovo-widget-message {
          display: flex;
          gap: 12px;
          align-items: flex-start;
        }

        .Rokovo-widget-message.user {
          justify-content: flex-end;
        }

        .Rokovo-widget-message.assistant .Rokovo-widget-message-avatar {
          width: 32px;
          height: 32px;
          background: ${hexToHsla(widgetConfig.primaryColor, 0.2)};
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          color: ${widgetConfig.primaryColor};
          font-size: 16px;
        }

        .Rokovo-widget-message-content {
          max-width: 80%;
          word-wrap: break-word;
        }

        .Rokovo-widget-message.user .Rokovo-widget-message-content {
          background: ${widgetConfig.primaryColor};
          color: white;
          padding: 16px;
          border-radius: 12px;
          border-bottom-right-radius: 4px;
          font-size: 14px;
          line-height: 1.5;
          font-weight: 400;
        }

        .Rokovo-widget-message.assistant .Rokovo-widget-message-content {
          background: ${widgetConfig.secondaryColor ? hexToHsla(widgetConfig.secondaryColor, 0.1) : "hsla(215, 16%, 15%, 0.5)"
      };
          color: hsl(210, 40%, 98%);
          border: 1px solid ${widgetConfig.secondaryColor ? hexToHsla(widgetConfig.secondaryColor, 0.2) : "hsl(215, 16%, 15%)"
      };
          padding: 16px;
          border-radius: 12px;
          border-bottom-left-radius: 4px;
          font-size: 14px;
          line-height: 1.5;
          font-weight: 400;
        }

        /* Typing Indicator */
        .Rokovo-widget-typing {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 16px;
          background: ${widgetConfig.secondaryColor ? hexToHsla(widgetConfig.secondaryColor, 0.1) : "hsla(215, 16%, 15%, 0.5)"
      };
          border: 1px solid ${widgetConfig.secondaryColor ? hexToHsla(widgetConfig.secondaryColor, 0.2) : "hsl(215, 16%, 15%)"
      };
          border-radius: 12px;
          border-bottom-left-radius: 4px;
          font-size: 14px;
          color: ${widgetConfig.secondaryColor ? hexToHsla(widgetConfig.secondaryColor, 0.8) : "hsl(217, 10%, 65%)"};
          font-style: italic;
        }

        .Rokovo-widget-typing-dots {
          display: flex;
          gap: 2px;
        }

        .Rokovo-widget-typing-dot {
          width: 4px;
          height: 4px;
          background: ${widgetConfig.secondaryColor ? hexToHsla(widgetConfig.secondaryColor, 0.8) : "hsl(217, 10%, 65%)"};
          border-radius: 50%;
          animation: Rokovo-typing-bounce 1.4s ease-in-out infinite both;
        }

        .Rokovo-widget-typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .Rokovo-widget-typing-dot:nth-child(2) { animation-delay: -0.16s; }

        /* Input Area - Matching SupportChat */
        .Rokovo-widget-input-area {
          border-top: 1px solid hsl(215, 16%, 15%);
          padding: 24px;
          display: flex;
          gap: 12px;
          align-items: center;
        }

        .Rokovo-widget-input {
          flex: 1;
          background: ${widgetConfig.secondaryColor ? hexToHsla(widgetConfig.secondaryColor, 0.1) : "hsl(210, 11%, 8%)"};
          border: 1px solid ${widgetConfig.secondaryColor ? hexToHsla(widgetConfig.secondaryColor, 0.3) : "hsl(215, 16%, 15%)"
      };
          border-radius: 12px;
          padding: 12px 16px;
          font-size: 14px;
          color: hsl(210, 40%, 98%);
          font-family: inherit;
          outline: none;
          transition: all 0.2s ease;
        }

        .Rokovo-widget-input::placeholder {
          color: ${widgetConfig.secondaryColor ? hexToHsla(widgetConfig.secondaryColor, 0.7) : "hsl(217, 10%, 65%)"};
        }

        .Rokovo-widget-input:focus {
          border-color: ${hexToHsla(widgetConfig.primaryColor, 0.5)};
          box-shadow: 0 0 0 2px ${hexToHsla(widgetConfig.primaryColor, 0.1)};
        }

        .Rokovo-widget-send {
          background: ${widgetConfig.primaryColor};
          color: white;
          border: none;
          border-radius: 12px;
          padding: 12px 16px;
          min-width: 48px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 16px;
          transition: all 0.2s ease;
        }

        .Rokovo-widget-send:hover:not(:disabled) {
          background: ${adjustColorBrightness(widgetConfig.primaryColor, -10)};
        }

        .Rokovo-widget-send:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        /* Footer - Secondary color support */
        .Rokovo-widget-footer {
          padding: 12px 24px;
          text-align: center;
          font-size: 12px;
          color: ${widgetConfig.secondaryColor ? hexToHsla(widgetConfig.secondaryColor, 0.7) : "hsl(217, 10%, 65%)"};
          border-top: 1px solid ${widgetConfig.secondaryColor ? hexToHsla(widgetConfig.secondaryColor, 0.2) : "hsl(215, 16%, 15%)"
      };
          background: ${widgetConfig.secondaryColor ? hexToHsla(widgetConfig.secondaryColor, 0.05) : "transparent"};
        }

        .Rokovo-widget-footer a {
          color: ${widgetConfig.secondaryColor ? hexToHsla(widgetConfig.secondaryColor, 0.8) : "hsl(217, 10%, 65%)"};
          text-decoration: none;
          display: inline-flex;
          align-items: center;
          gap: 6px;
          transition: color 0.2s ease;
          font-weight: 500;
        }

        .Rokovo-widget-footer a:hover {
          color: hsl(210, 40%, 98%);
        }

        .Rokovo-widget-footer img {
          transition: transform 0.2s ease;
        }

        .Rokovo-widget-footer a:hover img {
          transform: scale(1.1);
        }

        /* Loading and Error States */
        .Rokovo-widget-loading {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 40px;
          color: ${widgetConfig.secondaryColor ? hexToHsla(widgetConfig.secondaryColor, 0.8) : "hsl(217, 10%, 65%)"};
          font-size: 14px;
        }

        .Rokovo-widget-error {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 40px;
          color: hsl(0, 84%, 60%);
          text-align: center;
          font-size: 14px;
        }

        /* Modern Creative Animations */
        @keyframes Rokovo-modern-pulse {
          0%, 100% {
            box-shadow:
              0 8px 32px ${hexToHsla(widgetConfig.primaryColor, 0.4)},
              0 4px 16px ${hexToHsla(widgetConfig.primaryColor, 0.3)},
              inset 0 1px 0 hsla(0, 0%, 100%, 0.2),
              inset 0 -1px 0 hsla(0, 0%, 0%, 0.1);
            transform: translateY(0px);
          }
          33% {
            box-shadow:
              0 12px 48px ${hexToHsla(widgetConfig.primaryColor, 0.5)},
              0 6px 24px ${hexToHsla(widgetConfig.primaryColor, 0.4)},
              inset 0 1px 0 hsla(0, 0%, 100%, 0.25),
              inset 0 -1px 0 hsla(0, 0%, 0%, 0.1);
            transform: translateY(-2px);
          }
          66% {
            box-shadow:
              0 16px 64px ${hexToHsla(widgetConfig.primaryColor, 0.6)},
              0 8px 32px ${hexToHsla(widgetConfig.primaryColor, 0.5)},
              inset 0 1px 0 hsla(0, 0%, 100%, 0.3),
              inset 0 -1px 0 hsla(0, 0%, 0%, 0.1);
            transform: translateY(-1px);
          }
        }

        @keyframes Rokovo-pulse-dot {
          0%, 100% {
            opacity: 0.4;
            transform: scale(1);
          }
          50% {
            opacity: 1;
            transform: scale(1.2);
          }
        }

        @keyframes Rokovo-typing-bounce {
          0%, 80%, 100% {
            transform: scale(0);
          }
          40% {
            transform: scale(1);
          }
        }

        /* Mobile Responsiveness - Fixed for better UX */
        @media (max-width: 480px) {
          .Rokovo-widget-container {
            bottom: 16px;
            right: 16px;
            left: auto;
            position: fixed;
          }

          .Rokovo-widget-toggle {
            width: 64px;
            height: 64px;
            font-size: 28px;
            /* Ensure minimum 44px touch target */
            min-width: 44px;
            min-height: 44px;
            border-radius: 50%;
            position: relative;
            z-index: 999999;
          }

          .Rokovo-widget-chat {
            position: fixed;
            width: calc(100vw - 32px);
            height: calc(100vh - 120px);
            max-height: 80vh;
            bottom: 88px;
            right: 16px;
            left: 16px;
            border-radius: 12px;
            transform-origin: bottom right;
          }

          .Rokovo-widget-header {
            padding: 20px;
            gap: 16px;
          }

          .Rokovo-widget-avatar {
            width: 44px;
            height: 44px;
            font-size: 22px;
          }

          .Rokovo-widget-info h3 {
            font-size: 17px;
            line-height: 1.3;
          }

          .Rokovo-widget-info p {
            font-size: 15px;
          }

          .Rokovo-widget-status-text {
            font-size: 13px;
          }

          .Rokovo-widget-messages {
            padding: 20px;
            gap: 16px;
          }

          .Rokovo-widget-message-content {
            font-size: 15px;
            line-height: 1.4;
            padding: 14px 16px;
          }

          .Rokovo-widget-input-area {
            padding: 16px;
            gap: 12px;
            border-top: 1px solid ${widgetConfig.secondaryColor ? hexToHsla(widgetConfig.secondaryColor, 0.2) : "hsl(215, 16%, 15%)"
      };
          }

          .Rokovo-widget-input {
            font-size: 16px; /* Prevents iOS zoom */
            padding: 16px;
            border-radius: 12px;
            min-height: 44px;
            box-sizing: border-box;
          }

          .Rokovo-widget-send {
            padding: 16px;
            min-width: 52px;
            min-height: 52px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .Rokovo-widget-footer {
            padding: 12px 16px;
            font-size: 12px;
          }

          .Rokovo-widget-minimize {
            padding: 12px;
            min-width: 48px;
            min-height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          /* Ensure proper touch targets on mobile */
          .Rokovo-widget-message-content {
            font-size: 16px;
            line-height: 1.4;
            padding: 12px 16px;
          }
        }

        /* Small Mobile Devices (320px - 374px) */
        @media (max-width: 374px) {
          .Rokovo-widget-container {
            bottom: 12px;
            right: 12px;
            position: fixed;
          }

          .Rokovo-widget-toggle {
            width: 56px;
            height: 56px;
            font-size: 24px;
            border-radius: 50%;
            min-width: 44px;
            min-height: 44px;
          }

          .Rokovo-widget-chat {
            position: fixed;
            width: calc(100vw - 24px);
            height: calc(100vh - 100px);
            max-height: 70vh;
            bottom: 76px;
            right: 12px;
            left: 12px;
            border-radius: 12px;
          }

          .Rokovo-widget-header {
            padding: 16px;
          }

          .Rokovo-widget-messages {
            padding: 16px;
          }

          .Rokovo-widget-input-area {
            padding: 16px;
          }

          .Rokovo-widget-input {
            padding: 12px 14px;
          }

          .Rokovo-widget-send {
            padding: 12px 14px;
            min-width: 44px;
            min-height: 44px;
          }
        }

        /* Medium Mobile Devices (375px - 413px) */
        @media (min-width: 375px) and (max-width: 413px) {
          .Rokovo-widget-chat {
            width: calc(100vw - 36px);
            height: calc(100vh - 130px);
            max-height: 550px;
          }
        }

        /* Large Mobile Devices (414px+) */
        @media (min-width: 414px) and (max-width: 480px) {
          .Rokovo-widget-chat {
            width: calc(100vw - 40px);
            height: calc(100vh - 140px);
            max-height: 600px;
          }
        }

        /* Tablet Responsiveness */
        @media (max-width: 768px) and (min-width: 481px) {
          .Rokovo-widget-chat {
            width: 380px;
            height: 600px;
          }

          .Rokovo-widget-toggle {
            width: 64px;
            height: 64px;
          }
        }

        /* High DPI Displays */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
          .Rokovo-widget-toggle {
            border-width: 1px;
          }

          .Rokovo-widget-input,
          .Rokovo-widget-send {
            border-width: 1px;
          }
        }

        /* Reduced Motion Support */
        @media (prefers-reduced-motion: reduce) {
          .Rokovo-widget-toggle {
            animation: none;
          }

          .Rokovo-widget-status-dot {
            animation: none;
          }

          .Rokovo-widget-typing-dot {
            animation: none;
          }

          .Rokovo-widget-chat {
            transition: opacity 0.2s ease;
          }

          * {
            transition-duration: 0.1s !important;
          }
        }

        /* Dark Mode Support (already implemented) */
        @media (prefers-color-scheme: light) {
          /* Widget uses dark theme by default to match landing page */
        }
      `;

    const styleSheet = createElement("style");
    styleSheet.id = "Rokovo-widget-styles";
    styleSheet.textContent = styles;
    document.head.appendChild(styleSheet);
  }

  // Widget HTML structure - Matching SupportChat component exactly
  function createWidgetHTML() {
    const uniqueId = widgetConfig.publishableKey;
    return `
        <button
          class="Rokovo-widget-toggle"
          id="Rokovo-widget-toggle-${uniqueId}"
          aria-label="Open chat with ${escapeHtml(widgetConfig.agentName)}"
          aria-expanded="false"
          role="button"
          tabindex="0"
        >
          <svg width="42" height="30" viewBox="0 0 271 201" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.202393 51.8514C0.202393 47.3734 3.82889 43.7432 8.30239 43.7432H13.7024C18.1759 43.7432 21.8024 47.3734 21.8024 51.8514V116.716C21.8024 121.194 18.1759 124.824 13.7024 124.824H8.30239C3.82889 124.824 0.202393 121.194 0.202393 116.716V51.8514Z" fill="white"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M228.508 0.5C232.149 0.5 235.102 3.45596 235.102 7.10103V152.216C235.102 157.52 232.998 162.607 229.252 166.357L199.006 196.633C196.533 199.109 193.177 200.5 189.68 200.5H48.4886C41.2062 200.5 35.3027 194.59 35.3024 187.301V7.10103C35.3024 3.45596 38.2554 0.5 41.8968 0.5H228.508ZM67.7024 124.824C63.2289 124.824 59.6024 128.454 59.6024 132.932V133.338C59.6024 137.816 63.2289 141.446 67.7024 141.446H127.102C131.576 141.446 135.202 137.816 135.202 133.338V132.932C135.202 128.454 131.576 124.824 127.102 124.824H67.7024ZM67.9186 24.8243C63.3258 24.8243 59.6024 28.5515 59.6024 33.1489V92.1755C59.6024 96.7729 63.3258 100.5 67.9186 100.5H178.714C183.124 100.5 187.354 98.7446 190.473 95.6225L205.93 80.1506C209.049 77.0284 210.802 72.7944 210.802 68.379V33.1489C210.802 28.5515 207.079 24.8243 202.486 24.8243H67.9186Z" fill="white"/>
    <path d="M248.602 51.8514C248.602 47.3734 252.229 43.7432 256.702 43.7432H262.102C266.576 43.7432 270.202 47.3734 270.202 51.8514V116.716C270.202 121.194 266.576 124.824 262.102 124.824H256.702C252.229 124.824 248.602 121.194 248.602 116.716V51.8514Z" fill="white"/>
  </svg>
        </button>
        <div
          class="Rokovo-widget-chat"
          id="Rokovo-widget-chat-${uniqueId}"
          role="dialog"
          aria-labelledby="Rokovo-widget-title-${uniqueId}"
          aria-hidden="true"
        >
          <div class="Rokovo-widget-header">
            <div class="Rokovo-widget-avatar">
              ${widgetConfig.avatarUrl
        ? `<img src="${widgetConfig.avatarUrl}" alt="${escapeHtml(
          widgetConfig.agentName
        )} avatar" style="width: 100%; height: 100%; object-fit: cover; border-radius: 12px;">`
        : `<svg width="28" height="20" viewBox="0 0 271 201" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.202393 51.8514C0.202393 47.3734 3.82889 43.7432 8.30239 43.7432H13.7024C18.1759 43.7432 21.8024 47.3734 21.8024 51.8514V116.716C21.8024 121.194 18.1759 124.824 13.7024 124.824H8.30239C3.82889 124.824 0.202393 121.194 0.202393 116.716V51.8514Z" fill="white"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M228.508 0.5C232.149 0.5 235.102 3.45596 235.102 7.10103V152.216C235.102 157.52 232.998 162.607 229.252 166.357L199.006 196.633C196.533 199.109 193.177 200.5 189.68 200.5H48.4886C41.2062 200.5 35.3027 194.59 35.3024 187.301V7.10103C35.3024 3.45596 38.2554 0.5 41.8968 0.5H228.508ZM67.7024 124.824C63.2289 124.824 59.6024 128.454 59.6024 132.932V133.338C59.6024 137.816 63.2289 141.446 67.7024 141.446H127.102C131.576 141.446 135.202 137.816 135.202 133.338V132.932C135.202 128.454 131.576 124.824 127.102 124.824H67.7024ZM67.9186 24.8243C63.3258 24.8243 59.6024 28.5515 59.6024 33.1489V92.1755C59.6024 96.7729 63.3258 100.5 67.9186 100.5H178.714C183.124 100.5 187.354 98.7446 190.473 95.6225L205.93 80.1506C209.049 77.0284 210.802 72.7944 210.802 68.379V33.1489C210.802 28.5515 207.079 24.8243 202.486 24.8243H67.9186Z" fill="white"/>
    <path d="M248.602 51.8514C248.602 47.3734 252.229 43.7432 256.702 43.7432H262.102C266.576 43.7432 270.202 47.3734 270.202 51.8514V116.716C270.202 121.194 266.576 124.824 262.102 124.824H256.702C252.229 124.824 248.602 121.194 248.602 116.716V51.8514Z" fill="white"/>
  </svg>`
      }
            </div>
            <div class="Rokovo-widget-info">
              <h3 id="Rokovo-widget-title-${uniqueId}">${escapeHtml(widgetConfig.agentName)}</h3>
              <div class="Rokovo-widget-status">
                <div class="Rokovo-widget-status-dot"></div>
                <span class="Rokovo-widget-status-text">Online</span>
              </div>
            </div>
            <button
              class="Rokovo-widget-minimize"
              aria-label="Close chat"
              tabindex="0"
            >
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M18 6L6 18"/>
                <path d="M6 6l12 12"/>
              </svg>
            </button>
          </div>
          <div
            class="Rokovo-widget-messages"
            id="Rokovo-widget-messages-${uniqueId}"
            role="log"
            aria-live="polite"
            aria-label="Chat messages"
          >
            <div class="Rokovo-widget-loading">
              <div>Loading...</div>
            </div>
          </div>
          <div class="Rokovo-widget-input-area" role="form">
            <input
              type="text"
              class="Rokovo-widget-input"
              id="Rokovo-widget-input-${uniqueId}"
              placeholder="Type your message here..."
              aria-label="Type your message"
              disabled
            >
            <button
              class="Rokovo-widget-send"
              id="Rokovo-widget-send-${uniqueId}"
              aria-label="Send message"
              disabled
              tabindex="0"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M2 21l21-9L2 3v7l15 2-15 2v7z"/>
              </svg>
            </button>
          </div>
          <div class="Rokovo-widget-footer">
            <a href="https://Rokovo.dezh.tech" target="_blank" rel="noopener noreferrer" aria-label="Powered by Rokovo - Visit our website">
              
              Powered by <img src="https://Rokovo.dezh.tech/logo.svg" alt="Rokovo logo" style="width: 16px; height: 16px;"> Rokovo
            </a>
          </div>
        </div>
      `;
  }

  // Message rendering - Matching SupportChat component exactly
  function renderMessage(message) {
    const messageDiv = createElement("div", `Rokovo-widget-message ${message.role}`);

    if (message.role === "assistant") {
      const avatar = createElement("div", "Rokovo-widget-message-avatar");
      avatar.innerHTML = widgetConfig.avatarUrl
        ? `<img src="${widgetConfig.avatarUrl}" alt="${escapeHtml(
          widgetConfig.agentName
        )} avatar" style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px;">`
        : `
          <svg width="14" height="10" viewBox="0 0 271 201" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.202393 51.8514C0.202393 47.3734 3.82889 43.7432 8.30239 43.7432H13.7024C18.1759 43.7432 21.8024 47.3734 21.8024 51.8514V116.716C21.8024 121.194 18.1759 124.824 13.7024 124.824H8.30239C3.82889 124.824 0.202393 121.194 0.202393 116.716V51.8514Z" fill="white"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M228.508 0.5C232.149 0.5 235.102 3.45596 235.102 7.10103V152.216C235.102 157.52 232.998 162.607 229.252 166.357L199.006 196.633C196.533 199.109 193.177 200.5 189.68 200.5H48.4886C41.2062 200.5 35.3027 194.59 35.3024 187.301V7.10103C35.3024 3.45596 38.2554 0.5 41.8968 0.5H228.508ZM67.7024 124.824C63.2289 124.824 59.6024 128.454 59.6024 132.932V133.338C59.6024 137.816 63.2289 141.446 67.7024 141.446H127.102C131.576 141.446 135.202 137.816 135.202 133.338V132.932C135.202 128.454 131.576 124.824 127.102 124.824H67.7024ZM67.9186 24.8243C63.3258 24.8243 59.6024 28.5515 59.6024 33.1489V92.1755C59.6024 96.7729 63.3258 100.5 67.9186 100.5H178.714C183.124 100.5 187.354 98.7446 190.473 95.6225L205.93 80.1506C209.049 77.0284 210.802 72.7944 210.802 68.379V33.1489C210.802 28.5515 207.079 24.8243 202.486 24.8243H67.9186Z" fill="white"/>
    <path d="M248.602 51.8514C248.602 47.3734 252.229 43.7432 256.702 43.7432H262.102C266.576 43.7432 270.202 47.3734 270.202 51.8514V116.716C270.202 121.194 266.576 124.824 262.102 124.824H256.702C252.229 124.824 248.602 121.194 248.602 116.716V51.8514Z" fill="white"/>
  </svg>`;
      messageDiv.appendChild(avatar);
    }

    const contentWrapper = createElement("div", "Rokovo-widget-message-content");
    contentWrapper.innerHTML = escapeHtml(message.content);
    messageDiv.appendChild(contentWrapper);

    return messageDiv;
  }

  function renderMessages() {
    const messagesContainer = document.getElementById(`Rokovo-widget-messages-${widgetConfig.publishableKey}`);
    if (!messagesContainer) return;

    if (widgetState.isLoading) {
      messagesContainer.innerHTML = '<div class="Rokovo-widget-loading">Loading...</div>';
      return;
    }

    if (widgetState.error) {
      messagesContainer.innerHTML = `<div class="Rokovo-widget-error">${escapeHtml(widgetState.error)}</div>`;
      return;
    }

    messagesContainer.innerHTML = "";
    widgetState.messages.forEach((message) => {
      messagesContainer.appendChild(renderMessage(message));
    });

    if (widgetState.isTyping) {
      const typingDiv = createElement("div", "Rokovo-widget-message assistant");

      const avatar = createElement("div", "Rokovo-widget-message-avatar");
      avatar.innerHTML = widgetConfig.avatarUrl
        ? `<img src="${widgetConfig.avatarUrl}" alt="${escapeHtml(
          widgetConfig.agentName
        )} avatar" style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px;">`
        : `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M12 8V4H8"/>
            <rect width="16" height="12" x="4" y="8" rx="2"/>
            <path d="M2 14h2"/>
            <path d="M20 14h2"/>
            <path d="M15 13v2"/>
            <path d="M9 13v2"/>
          </svg>`;

      const content = createElement("div", "Rokovo-widget-typing");
      content.innerHTML = `
          ${escapeHtml(widgetConfig.agentName)} is typing
          <div class="Rokovo-widget-typing-dots">
            <div class="Rokovo-widget-typing-dot"></div>
            <div class="Rokovo-widget-typing-dot"></div>
            <div class="Rokovo-widget-typing-dot"></div>
          </div>
        `;

      typingDiv.appendChild(avatar);
      typingDiv.appendChild(content);
      messagesContainer.appendChild(typingDiv);
    }

    // Scroll to bottom
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
  }

  // Widget initialization
  async function initializeWidget() {
    try {
      widgetState.isLoading = true;
      renderMessages();

      // Initialize session
      await initSession(`external_${Date.now()}`);

      // Set welcome message
      const welcomeMessage = {
        id: "1",
        content: `Hello! I'm ${widgetConfig.agentName} from ${widgetConfig.businessName}. How can I help you today?`,
        role: "assistant",
        timestamp: new Date(),
      };

      widgetState.messages = [welcomeMessage];
      widgetState.isLoading = false;

      // Enable input
      const input = document.getElementById(`Rokovo-widget-input-${widgetConfig.publishableKey}`);
      const sendButton = document.getElementById(`Rokovo-widget-send-${widgetConfig.publishableKey}`);
      if (input) input.disabled = false;
      if (sendButton) sendButton.disabled = false;

      renderMessages();
    } catch (error) {
      console.error("Failed to initialize widget:", error);
      widgetState.error = "Failed to load chatbot";
      widgetState.isLoading = false;
      renderMessages();
    }
  }

  // Message sending
  async function sendMessage(content) {
    if (!content.trim() || !widgetState.sessionId || widgetState.isTyping) return;

    // Add user message
    const userMessage = {
      id: Date.now().toString(),
      content: content.trim(),
      role: "user",
      timestamp: new Date(),
    };

    widgetState.messages.push(userMessage);
    widgetState.isTyping = true;
    renderMessages();

    // Clear input
    const input = document.getElementById(`Rokovo-widget-input-${widgetConfig.publishableKey}`);
    if (input) input.value = "";

    try {
      const response = await continueSession(content.trim());

      // Add assistant response
      const assistantMessage = {
        id: (Date.now() + 1).toString(),
        content: response.data.response.content,
        role: "assistant",
        timestamp: new Date(),
      };

      widgetState.messages.push(assistantMessage);
    } catch (error) {
      console.error("Failed to send message:", error);

      // Add error message
      const errorMessage = {
        id: (Date.now() + 1).toString(),
        content: "I'm sorry, I'm having trouble responding right now. Please try again.",
        role: "assistant",
        timestamp: new Date(),
      };

      widgetState.messages.push(errorMessage);
    } finally {
      widgetState.isTyping = false;
      renderMessages();
    }
  }

  // Event handlers
  function setupEventHandlers() {
    const uniqueId = widgetConfig.publishableKey;
    const toggle = document.getElementById(`Rokovo-widget-toggle-${uniqueId}`);
    const chat = document.getElementById(`Rokovo-widget-chat-${uniqueId}`);
    const minimize = document.querySelector(`#Rokovo-widget-container-${uniqueId} .Rokovo-widget-minimize`);
    const input = document.getElementById(`Rokovo-widget-input-${uniqueId}`);
    const sendButton = document.getElementById(`Rokovo-widget-send-${uniqueId}`);

    // Toggle chat visibility
    if (toggle) {
      toggle.addEventListener("click", () => {
        widgetState.isOpen = !widgetState.isOpen;
        if (chat) {
          chat.classList.toggle("open", widgetState.isOpen);
          chat.setAttribute("aria-hidden", (!widgetState.isOpen).toString());
        }

        // Update toggle button and ARIA attributes
        toggle.innerHTML = widgetState.isOpen
          ? `<svg width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M18 6L6 18"/>
              <path d="M6 6l12 12"/>
            </svg>`
          : `<svg width="42" height="30" viewBox="0 0 271 201" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.202393 51.8514C0.202393 47.3734 3.82889 43.7432 8.30239 43.7432H13.7024C18.1759 43.7432 21.8024 47.3734 21.8024 51.8514V116.716C21.8024 121.194 18.1759 124.824 13.7024 124.824H8.30239C3.82889 124.824 0.202393 121.194 0.202393 116.716V51.8514Z" fill="white"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M228.508 0.5C232.149 0.5 235.102 3.45596 235.102 7.10103V152.216C235.102 157.52 232.998 162.607 229.252 166.357L199.006 196.633C196.533 199.109 193.177 200.5 189.68 200.5H48.4886C41.2062 200.5 35.3027 194.59 35.3024 187.301V7.10103C35.3024 3.45596 38.2554 0.5 41.8968 0.5H228.508ZM67.7024 124.824C63.2289 124.824 59.6024 128.454 59.6024 132.932V133.338C59.6024 137.816 63.2289 141.446 67.7024 141.446H127.102C131.576 141.446 135.202 137.816 135.202 133.338V132.932C135.202 128.454 131.576 124.824 127.102 124.824H67.7024ZM67.9186 24.8243C63.3258 24.8243 59.6024 28.5515 59.6024 33.1489V92.1755C59.6024 96.7729 63.3258 100.5 67.9186 100.5H178.714C183.124 100.5 187.354 98.7446 190.473 95.6225L205.93 80.1506C209.049 77.0284 210.802 72.7944 210.802 68.379V33.1489C210.802 28.5515 207.079 24.8243 202.486 24.8243H67.9186Z" fill="white"/>
    <path d="M248.602 51.8514C248.602 47.3734 252.229 43.7432 256.702 43.7432H262.102C266.576 43.7432 270.202 47.3734 270.202 51.8514V116.716C270.202 121.194 266.576 124.824 262.102 124.824H256.702C252.229 124.824 248.602 121.194 248.602 116.716V51.8514Z" fill="white"/>
  </svg>`;
        toggle.setAttribute("aria-expanded", widgetState.isOpen.toString());
        toggle.setAttribute(
          "aria-label",
          widgetState.isOpen ? "Close chat" : `Open chat with ${widgetConfig.agentName}`
        );

        // Initialize widget on first open
        if (widgetState.isOpen && widgetState.messages.length === 0) {
          initializeWidget();
        }
      });

      // Add keyboard support
      toggle.addEventListener("keydown", (e) => {
        if (e.key === "Enter" || e.key === " ") {
          e.preventDefault();
          toggle.click();
        }
      });
    }

    // Minimize chat
    if (minimize) {
      minimize.addEventListener("click", () => {
        widgetState.isOpen = false;
        if (chat) {
          chat.classList.remove("open");
          chat.setAttribute("aria-hidden", "true");
        }
        if (toggle) {
          toggle.innerHTML = `<svg width="42" height="30" viewBox="0 0 271 201" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.202393 51.8514C0.202393 47.3734 3.82889 43.7432 8.30239 43.7432H13.7024C18.1759 43.7432 21.8024 47.3734 21.8024 51.8514V116.716C21.8024 121.194 18.1759 124.824 13.7024 124.824H8.30239C3.82889 124.824 0.202393 121.194 0.202393 116.716V51.8514Z" fill="white"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M228.508 0.5C232.149 0.5 235.102 3.45596 235.102 7.10103V152.216C235.102 157.52 232.998 162.607 229.252 166.357L199.006 196.633C196.533 199.109 193.177 200.5 189.68 200.5H48.4886C41.2062 200.5 35.3027 194.59 35.3024 187.301V7.10103C35.3024 3.45596 38.2554 0.5 41.8968 0.5H228.508ZM67.7024 124.824C63.2289 124.824 59.6024 128.454 59.6024 132.932V133.338C59.6024 137.816 63.2289 141.446 67.7024 141.446H127.102C131.576 141.446 135.202 137.816 135.202 133.338V132.932C135.202 128.454 131.576 124.824 127.102 124.824H67.7024ZM67.9186 24.8243C63.3258 24.8243 59.6024 28.5515 59.6024 33.1489V92.1755C59.6024 96.7729 63.3258 100.5 67.9186 100.5H178.714C183.124 100.5 187.354 98.7446 190.473 95.6225L205.93 80.1506C209.049 77.0284 210.802 72.7944 210.802 68.379V33.1489C210.802 28.5515 207.079 24.8243 202.486 24.8243H67.9186Z" fill="white"/>
    <path d="M248.602 51.8514C248.602 47.3734 252.229 43.7432 256.702 43.7432H262.102C266.576 43.7432 270.202 47.3734 270.202 51.8514V116.716C270.202 121.194 266.576 124.824 262.102 124.824H256.702C252.229 124.824 248.602 121.194 248.602 116.716V51.8514Z" fill="white"/>
  </svg>`;
          toggle.setAttribute("aria-expanded", "false");
          toggle.setAttribute("aria-label", `Open chat with ${widgetConfig.agentName}`);
        }
      });

      // Add keyboard support
      minimize.addEventListener("keydown", (e) => {
        if (e.key === "Enter" || e.key === " ") {
          e.preventDefault();
          minimize.click();
        }
      });
    }

    // Send message on button click
    if (sendButton) {
      sendButton.addEventListener("click", () => {
        if (input) {
          sendMessage(input.value);
        }
      });
    }

    // Send message on Enter key
    if (input) {
      input.addEventListener("keypress", (e) => {
        if (e.key === "Enter" && !e.shiftKey) {
          e.preventDefault();
          sendMessage(input.value);
        }
      });
    }

    // Close chat when clicking outside
    document.addEventListener("click", (e) => {
      const container = document.getElementById(`Rokovo-widget-container-${uniqueId}`);
      if (container && !container.contains(e.target) && widgetState.isOpen) {
        widgetState.isOpen = false;
        if (chat) {
          chat.classList.remove("open");
        }
        if (toggle) {
          toggle.innerHTML = `<svg width="42" height="30" viewBox="0 0 271 201" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M0.202393 51.8514C0.202393 47.3734 3.82889 43.7432 8.30239 43.7432H13.7024C18.1759 43.7432 21.8024 47.3734 21.8024 51.8514V116.716C21.8024 121.194 18.1759 124.824 13.7024 124.824H8.30239C3.82889 124.824 0.202393 121.194 0.202393 116.716V51.8514Z" fill="white"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M228.508 0.5C232.149 0.5 235.102 3.45596 235.102 7.10103V152.216C235.102 157.52 232.998 162.607 229.252 166.357L199.006 196.633C196.533 199.109 193.177 200.5 189.68 200.5H48.4886C41.2062 200.5 35.3027 194.59 35.3024 187.301V7.10103C35.3024 3.45596 38.2554 0.5 41.8968 0.5H228.508ZM67.7024 124.824C63.2289 124.824 59.6024 128.454 59.6024 132.932V133.338C59.6024 137.816 63.2289 141.446 67.7024 141.446H127.102C131.576 141.446 135.202 137.816 135.202 133.338V132.932C135.202 128.454 131.576 124.824 127.102 124.824H67.7024ZM67.9186 24.8243C63.3258 24.8243 59.6024 28.5515 59.6024 33.1489V92.1755C59.6024 96.7729 63.3258 100.5 67.9186 100.5H178.714C183.124 100.5 187.354 98.7446 190.473 95.6225L205.93 80.1506C209.049 77.0284 210.802 72.7944 210.802 68.379V33.1489C210.802 28.5515 207.079 24.8243 202.486 24.8243H67.9186Z" fill="white"/>
    <path d="M248.602 51.8514C248.602 47.3734 252.229 43.7432 256.702 43.7432H262.102C266.576 43.7432 270.202 47.3734 270.202 51.8514V116.716C270.202 121.194 266.576 124.824 262.102 124.824H256.702C252.229 124.824 248.602 121.194 248.602 116.716V51.8514Z" fill="white"/>
  </svg>`;
          toggle.setAttribute("aria-expanded", "false");
          toggle.setAttribute("aria-label", `Open chat with ${widgetConfig.agentName}`);
        }
      }
    });
  }

  // Configuration parsing from script tag and URL parameters
  function parseConfiguration() {
    const script = document.currentScript || document.querySelector('script[src*="Rokovo-widget.js"]');

    if (script) {
      // Parse attributes from script tag
      const publishableKey = script.getAttribute("data-publishable-key");
      const businessName = script.getAttribute("data-business-name");
      const agentName = script.getAttribute("data-agent-name");
      const primaryColor = script.getAttribute("data-primary-color");
      const secondaryColor = script.getAttribute("data-secondary-color");
      const chatBubbleStyle = script.getAttribute("data-chat-bubble-style");
      const avatarUrl = script.getAttribute("data-avatar-url");
      const apiBaseUrl = script.getAttribute("data-api-base-url");

      // Update configuration
      if (publishableKey) widgetConfig.publishableKey = publishableKey;
      if (businessName) widgetConfig.businessName = businessName;
      if (agentName) widgetConfig.agentName = agentName;
      if (primaryColor) widgetConfig.primaryColor = primaryColor;
      if (secondaryColor) widgetConfig.secondaryColor = secondaryColor;
      if (chatBubbleStyle) widgetConfig.chatBubbleStyle = chatBubbleStyle;
      if (avatarUrl) widgetConfig.avatarUrl = avatarUrl;
      if (apiBaseUrl) widgetConfig.apiBaseUrl = apiBaseUrl;
    }

    // Parse URL parameters as fallback
    const urlParams = new URLSearchParams(window.location.search);
    if (!widgetConfig.publishableKey) {
      widgetConfig.publishableKey = urlParams.get("key") || urlParams.get("publishableKey");
    }
    if (!widgetConfig.businessName || widgetConfig.businessName === "Your Business") {
      widgetConfig.businessName = urlParams.get("businessName") || widgetConfig.businessName;
    }
    if (!widgetConfig.agentName || widgetConfig.agentName === "AI Assistant") {
      widgetConfig.agentName = urlParams.get("agentName") || widgetConfig.agentName;
    }
    if (!widgetConfig.primaryColor || widgetConfig.primaryColor === "#ff6600") {
      widgetConfig.primaryColor = urlParams.get("primaryColor") || widgetConfig.primaryColor;
    }
  }

  // Main widget initialization
  function initWidget() {
    // Parse configuration
    parseConfiguration();

    // Validate required configuration
    if (!widgetConfig.publishableKey) {
      console.error("Rokovo Widget: publishableKey is required");
      return;
    }

    // Check if widget already exists for this key
    const existingWidget = document.getElementById(`Rokovo-widget-container-${widgetConfig.publishableKey}`);
    if (existingWidget) {
      console.warn("Rokovo Widget: Widget already initialized for this publishable key");
      return;
    }

    // Inject styles
    injectStyles();

    // Create widget container with unique ID
    const container = createElement("div");
    container.id = `Rokovo-widget-container-${widgetConfig.publishableKey}`;
    container.className = "Rokovo-widget-container";
    container.innerHTML = createWidgetHTML();

    // Add to page
    document.body.appendChild(container);

    // Setup event handlers
    setupEventHandlers();

    console.log("Rokovo Widget initialized successfully");
  }

  // Auto-initialize when DOM is ready
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", initWidget);
  } else {
    initWidget();
  }

  // Expose global API for manual initialization
  window.RokovoWidget = {
    init: initWidget,
    config: widgetConfig,
    state: widgetState,
  };
})();
