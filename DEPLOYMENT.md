# Docker Multi-Environment Deployment Setup

## Overview

This project now uses a unified Docker deployment system that properly handles environment-specific configurations for both development and production environments.

## Key Changes Made

### 1. Unified Dockerfile
- **Replaced**: Separate `dev.dockerfile` and `prod.dockerfile`
- **With**: Single `Dockerfile` that accepts `BUILD_ENV` argument
- **Benefits**: Single source of truth, easier maintenance, consistent build process

### 2. Environment-Specific Configuration
- **Development**: Uses `.env.development` file
- **Production**: Uses `.env.production` file
- **Process**: Dockerfile copies the appropriate env file to `.env.local` based on `BUILD_ENV`

### 3. Updated GitHub Actions Workflows
- **dev workflow**: Passes `BUILD_ENV=development` to Docker build
- **prod workflow**: Passes `BUILD_ENV=production` to Docker build
- **Both**: Use the unified `Dockerfile`

## Environment Files

### .env.development
```
VITE_CLERK_PUBLISHABLE_KEY=pk_test_c2V0LWJlbmdhbC0yMi5jbGVyay5hY2NvdW50cy5kZXYk
VITE_API_BASE_URL=https://Rokovo-dev-api.dezh.tech
```

### .env.production
```
VITE_CLERK_PUBLISHABLE_KEY=pk_live_Y2xlcmsuZGV6aC50ZWNoJA
VITE_API_BASE_URL=https://Rokovo-api.dezh.tech
```

## Build Process

### How It Works
1. **Docker Build**: Accepts `BUILD_ENV` argument (defaults to `production`)
2. **Environment Selection**: Copies appropriate `.env.*` file to `.env.local`
3. **Build Command**: Runs `npm run build:dev` for development, `npm run build` for production
4. **Vite Processing**: Vite reads `.env.local` and injects variables into the build

### Local Testing

#### Development Build
```bash
docker build --build-arg BUILD_ENV=development -t Rokovo-frontend:dev .
```

#### Production Build
```bash
docker build --build-arg BUILD_ENV=production -t Rokovo-frontend:prod .
# or simply (production is default)
docker build -t Rokovo-frontend:prod .
```

## GitHub Actions Deployment

### Development Deployment
- **Trigger**: Push tags matching `dev-v*`
- **Workflow**: `.github/workflows/deploy-dev.yml`
- **Build Args**: `BUILD_ENV=development`
- **Registry**: `Rokovo-registry.dezh.tech/dezh/Rokovo-frontend-dev`

### Production Deployment
- **Trigger**: Push tags matching `prod-v*`
- **Workflow**: `.github/workflows/deploy-prod.yml`
- **Build Args**: `BUILD_ENV=production`
- **Registry**: `Rokovo-registry.dezh.tech/dezh/Rokovo-frontend-prod`

## Verification

To verify that the correct environment is being used:

1. **Check built JavaScript files** for environment-specific URLs:
   - Development should contain: `Rokovo-dev-api.dezh.tech`
   - Production should contain: `Rokovo-api.dezh.tech`

2. **Check Docker image labels**:
   ```bash
   docker inspect <image-name> | grep -A 10 Labels
   ```

## Troubleshooting

### Common Issues

1. **Wrong environment variables in build**
   - Check that the correct `BUILD_ENV` is passed to Docker build
   - Verify the appropriate `.env.*` file exists and has correct values

2. **Build fails**
   - Ensure both `.env.development` and `.env.production` files exist
   - Check that npm scripts `build` and `build:dev` are defined in package.json

3. **Environment not switching**
   - Clear Docker build cache: `docker builder prune`
   - Rebuild with `--no-cache` flag

### Debug Commands

```bash
# Check which environment file is being used during build
docker build --build-arg BUILD_ENV=development --progress=plain .

# Inspect built image environment
docker run --rm <image-name> cat /usr/share/nginx/html/assets/*.js | grep -o 'Rokovo.*\.tech'
```

## Migration Notes

- **Removed Files**: `dev.dockerfile`, `prod.dockerfile`
- **Added Files**: `Dockerfile` (unified), `DEPLOYMENT.md` (this file)
- **Modified Files**: `.github/workflows/deploy-dev.yml`, `.github/workflows/deploy-prod.yml`

## Next Steps

1. Test the deployment by creating appropriate git tags
2. Monitor the first deployments to ensure everything works correctly
3. Update any documentation that referenced the old separate Dockerfiles
