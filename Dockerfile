# -------- Stage 1: Build the React app --------
FROM node:20-alpine AS builder

# Accept build argument for environment
ARG BUILD_ENV=production

WORKDIR /app

# Copy package files
COPY package.json package-lock.json ./

# Install dependencies
RUN npm ci --silent

# Copy source code and configuration files
COPY . .

# Copy the appropriate environment file based on BUILD_ENV
RUN if [ "$BUILD_ENV" = "development" ]; then \
      cp .env.development .env.local; \
    else \
      cp .env.production .env.local; \
    fi

# Build the application with the appropriate mode
RUN if [ "$BUILD_ENV" = "development" ]; then \
      npm run build:dev; \
    else \
      npm run build; \
    fi

# -------- Stage 2: Serve with NGINX --------
FROM nginx:1.25-alpine AS runner

# Remove default config
RUN rm /etc/nginx/conf.d/default.conf

# Copy custom nginx config
COPY nginx/nginx.conf /etc/nginx/conf.d/

# Copy build output from builder stage
COPY --from=builder /app/dist /usr/share/nginx/html

# Add labels for better image management
ARG BUILD_ENV=production
ARG LABEL_MAINTAINER
ARG LABEL_VERSION
ARG LABEL_REPO
ARG LABEL_COMMIT

LABEL maintainer="${LABEL_MAINTAINER}" \
      version="${LABEL_VERSION}" \
      repository="${LABEL_REPO}" \
      commit="${LABEL_COMMIT}" \
      environment="${BUILD_ENV}"

# Healthcheck
HEALTHCHECK --interval=30s --timeout=3s \
  CMD wget -q --spider http://localhost/ || exit 1

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
